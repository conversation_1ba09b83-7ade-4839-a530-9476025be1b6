# Media Analysis Platform for Elections (MAPE)

This project is a frontend implementation of the Media Analysis Platform for Elections in Malawi. It's designed to align with the Malawi Prevention Platform document requirements, providing tools for election monitoring, early warning, and preventive action planning.

## Overview

The Media Analysis Platform for Elections is a comprehensive tool designed to:

1. Monitor election-related content across social media and news platforms
2. Analyze trends, narratives, and potential risks to election integrity
3. Provide early warning of potential threats to peaceful elections
4. Plan and track preventive actions to mitigate identified risks

## Features

The platform has been designed with three key components:

### 1. Election Analytics

This module allows users to:
- Track candidate mentions and sentiment across media platforms
- Monitor election narratives and topics
- Identify geographic patterns in election discourse
- Track risk factors that could affect election integrity

### 2. Early Warning System

This module enables:
- Real-time monitoring of potential election disruptions
- Incident tracking and response coordination
- Trend analysis for proactive intervention
- Geographic mapping of potential hotspots
- Risk indicator tracking

### 3. Preventive Actions

This module supports:
- Planning and tracking preventive interventions
- Assigning responsibilities and deadlines
- Monitoring completion status of planned actions
- Analyzing the impact of interventions
- Resource and asset management for response teams

## Technology Stack

- **Framework**: React with TypeScript
- **UI Components**: Custom component library with ShadCN UI
- **Data Visualization**: Recharts
- **State Management**: React Hooks
- **API Integration**: Custom hooks with fetch API
- **Styling**: Tailwind CSS

## Data Model

The platform uses a comprehensive data model that aligns with the Malawi Prevention Platform document:

1. **Election Analytics Data**:
   - Candidate information with sentiment analysis
   - Narrative tracking and categorization
   - Geographic distribution of election discourse
   - Risk factor assessment

2. **Early Warning System Data**:
   - Alert tracking with severity and status
   - Incident trends over time
   - Geographic heatmap analysis
   - Risk indicators and predictions

3. **Preventive Actions Data**:
   - Action inventory with status tracking
   - Priority classification
   - Impact assessments
   - Resource and team management

## API Documentation

Full API documentation is available in the [api-documentation.md](src/docs/api-documentation.md) file, which outlines the expected data formats, endpoints, and integration patterns.

## Mock Data

For development and demonstration purposes, the application includes mock data services:

- `electionAnalyticsMock.ts`: Mock data for election monitoring
- `earlyWarningSystemMock.ts`: Mock alerts and incident data
- `preventiveActionsMock.ts`: Mock action tracking data
- `apiService.ts`: Simulated API service for frontend integration testing

## Getting Started

1. Clone the repository
2. Install dependencies with `npm install` or `bun install`
3. Start the development server with `npm run dev` or `bun run dev`
4. Access the application at `http://localhost:5173`

## Integration with Backend

The frontend is designed to connect with a RESTful API backend. The integration process:

1. Replace the mock API services with actual API calls
2. Update the authentication flow to work with the backend auth system
3. Ensure data structures align with the schemas outlined in the mock services

## Alignment with Malawi Prevention Platform

This implementation directly addresses the key requirements from the Malawi Prevention Platform document:

1. **Media Monitoring**: Comprehensive tracking of election discourse across platforms
2. **Early Warning**: Sophisticated alert system for potential issues
3. **Prevention Planning**: Structured approach to planning interventions
4. **Response Coordination**: Tools for managing team activities and resources

## Lovable Project

**URL**: https://lovable.dev/projects/8975ace8-ecdb-491a-b743-38ef5f48a9cd

## How to Edit This Code

**Use Lovable**
Simply visit the [Lovable Project](https://lovable.dev/projects/8975ace8-ecdb-491a-b743-38ef5f48a9cd) and start prompting.

**Use your preferred IDE**
You can clone this repo and push changes. The only requirement is having Node.js & npm installed.

## Future Enhancements

1. Mobile application for field teams
2. Offline functionality for areas with limited connectivity
3. Enhanced machine learning for predictive analytics
4. Integration with SMS and other communication channels
5. Expanded language support for local Malawian languages
