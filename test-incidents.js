// Simple test to verify the incidents endpoint
const axios = require('axios');

const testIncidentsAPI = async () => {
  try {
    const url = 'http://localhost:3200/api/reports/incidents?limit=10&offset=0&severity=all&district=all';
    console.log('Testing incidents API:', url);
    
    const response = await axios.get(url);
    console.log('Response status:', response.status);
    console.log('Response data structure:');
    console.log(JSON.stringify(response.data, null, 2));
    
    if (response.data.success && response.data.data && response.data.data.incidents) {
      console.log(`\nFound ${response.data.data.incidents.length} incidents`);
      console.log('First incident:', response.data.data.incidents[0]);
    }
  } catch (error) {
    console.error('Error testing incidents API:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
};

testIncidentsAPI();
