# Enhanced Monitored Incidents - Complete Field Coverage with Nested Data Support

## 📋 All Incident Fields Now Displayed with Nested Data Extraction

### **IMPORTANT: Nested Data Structure Support**
The component now supports extracting incident data from nested `full_analysis_json` structure:
- **Primary Check**: First checks if field exists at top level
- **Fallback Check**: Then checks in `full_analysis_json.incidents[0]` for nested data
- **Smart Extraction**: Helper functions automatically handle both flat and nested structures

### **Core Identification Fields**
- ✅ **ID**: Unique incident identifier
- ✅ **Post Summary**: Brief description of the incident
- ✅ **Details**: Comprehensive incident description
- ✅ **Primary Category**: Main incident classification
- ✅ **Severity Level**: Critical/High/Medium/Low with color coding
- ✅ **District**: Geographic location
- ✅ **Platform**: Source platform with icons
- ✅ **Created At**: Incident creation timestamp
- ✅ **Updated At**: Last modification timestamp

### **Violation Classification Fields**
- ✅ **Electoral Misconduct**: Specific electoral violations
- ✅ **Political Attacks**: Political harassment/intimidation incidents
- ✅ **Law Enforcement Misconduct**: Police/security force violations
- ✅ **Civil Rights Restriction**: Freedom limitation incidents
- ✅ **Physical Integrity Violation**: Violence/assault incidents
- ✅ **Gender-Based Violence**: SGBV in electoral context
- ✅ **Discrimination Type**: Targeted discrimination incidents
- ✅ **PWA Attacks**: Persons with Albinism targeted incidents
- ✅ **Protest Type**: Demonstration categorization

### **Analysis & Assessment Fields**
- ✅ **Sentiment**: Positive/Negative/Neutral with color coding
- ✅ **Election Relevance**: Direct/Indirect/General relevance levels
- ✅ **Alert Level**: Risk assessment level
- ✅ **Risk Assessment**: Detailed risk evaluation
- ✅ **Context Assessment**: Contextual analysis
- ✅ **Entity Involvement Score**: Stakeholder involvement rating
- ✅ **Keyword Density**: Keyword frequency assessment

### **Participant Information**
- ✅ **Perpetrator**: Individual/group responsible
- ✅ **Victims Profile**: Affected parties description
- ✅ **Gender of Victim**: Male/Female/Mixed/Unidentified
- ✅ **Entities Involved**: Detailed stakeholder breakdown with structured display
  - Victims list with color-coded labels
  - Perpetrators list with color-coded labels  
  - Witnesses/sources list with color-coded labels
- ✅ **Political Context**: Political party and institutional context
  - Party involved identification
  - Youth wing involvement tracking
  - Institutional failure assessment

### **Temporal Information**
- ✅ **Date of Incident**: When the incident occurred
- ✅ **Incident Year/Month/Day**: Granular date breakdown
- ✅ **Time Period**: Analysis period
- ✅ **Timestamp Provided**: Original source timestamp

### **Content Analysis Fields**
- ✅ **Original Language**: Source content language
- ✅ **English Translation**: Translated content (if applicable)
- ✅ **Language Detected**: Auto-detected language
- ✅ **Raw Content**: Original source content
- ✅ **Source URL**: Direct link to source with clickable button

### **Thematic Analysis**
- ✅ **Key Themes**: Primary themes identified (displayed as chips)
- ✅ **Keywords Related to Incident**: Relevant keywords (array display)
- ✅ **Total Keywords Found**: Count of relevant keywords
- ✅ **Highest Frequency Category**: Most prominent keyword category
- ✅ **Main Categories Identified**: All categories detected (array display)

### **Geographic & Source Information**
- ✅ **Districts Affected**: All affected areas (array display)
- ✅ **Sources Mentioned**: Referenced sources (array display)
- ✅ **Overall Sentiment**: Aggregate sentiment analysis
- ✅ **Total Incidents**: Related incident count

### **Full Analysis Data**
- ✅ **Full Analysis JSON**: Complete analysis object (expandable)

## 🎨 Display Features

### **Card View Enhancements**
- **Organized Sections**: 
  - Header with badges and metadata
  - Main content area with summary
  - Basic information grid (3 columns on large screens)
  - Violation categories section (highlighted background)
  - **NEW: Entities & Political Context section** (purple background)
  - Keyword analysis section (green background)
  - Analysis fields section
  - Footer with actions and metadata

- **Visual Indicators**:
  - Color-coded sentiment badges (Green=Positive, Red=Negative, Gray=Neutral)
  - Color-coded election relevance (Red=Direct, Yellow=Indirect, Blue=General)
  - Severity level badges with appropriate colors
  - Platform icons for easy identification
  - **NEW: Color-coded entity labels** (Blue=Victims, Red=Perpetrators, Green=Witnesses)
  - **NEW: Color-coded political context** (Purple=Party, Orange=Youth Wing, Red=Institutional Failure)

- **Enhanced Object Rendering**:
  - **NEW: Structured entities_involved display** with labeled sections
  - **NEW: Structured political_context display** with labeled sections
  - Smart rendering of array values as styled chips
  - Expandable display for arrays with 3+ items
  - Proper truncation and tooltips for long content

### **Table View Enhancements**
- **Comprehensive Columns**: 19 columns covering all major fields including entities_involved and political_context
- **Responsive Design**: Horizontal scrolling for wide tables
- **Visual Elements**: Badges, icons, and color coding maintained
- **Compact Display**: Optimized cell content with proper truncation
- **Enhanced Object Support**: Structured objects displayed with color-coded labels

### **Smart Rendering Features**
- **Array Detection**: Automatically detects and formats array values
- **Null Handling**: Proper display of null/undefined values as "N/A"
- **Length Management**: Dynamic truncation based on context and space
- **Visual Formatting**: Arrays displayed as styled chips with count indicators
- **Tooltip Support**: Full content available on hover for truncated items

## 🔧 Technical Implementation

### **Type Safety**
- Comprehensive TypeScript interfaces covering all fields
- Proper typing for optional and array fields
- Type-safe rendering functions

### **Performance**
- Efficient array rendering with slice() for large datasets
- Conditional rendering to avoid empty sections
- Optimized re-renders with proper key management

### **Accessibility**
- Proper ARIA labels and titles
- Color coding supplemented with text indicators
- Keyboard navigation support for interactive elements

## 🚀 Usage

All fields from the API response are now properly displayed in both card and table views. The enhanced display provides:

1. **Complete Coverage**: Every field in the response is accessible
2. **Smart Formatting**: Arrays and complex objects are properly rendered
3. **Visual Hierarchy**: Important information is prominently displayed
4. **Interactive Elements**: Clickable links and expandable content
5. **Responsive Design**: Optimal viewing on all screen sizes

The implementation ensures that users can access and understand all available incident data without losing any information from the comprehensive API response.

## 🔧 Nested Data Extraction Implementation

### **Helper Functions for Data Access**
```typescript
// Generic function to extract data from nested structure
const getNestedIncidentData = (incident, field) => {
  // Check top level first
  if (incident[field] !== undefined && incident[field] !== null) {
    return incident[field];
  }
  
  // Check nested in full_analysis_json.incidents[0]
  if (incident.full_analysis_json?.incidents?.[0]?.[field] !== undefined) {
    return incident.full_analysis_json.incidents[0][field];
  }
  
  return null;
};

// Specialized helper functions
const getPoliticalContext = (incident) => getNestedIncidentData(incident, 'political_context');
const getEntitiesInvolved = (incident) => getNestedIncidentData(incident, 'entities_involved');
const getKeywordsRelated = (incident) => getNestedIncidentData(incident, 'keywords_related_to_incident');
// ... and many more for all violation types
```

### **Data Structure Examples**
**Political Context:**
```json
"political_context": {
  "party_involved": "DPP",
  "youth_wing_involved": null,
  "institutional_failure": null
}
```

**Entities Involved:**
```json
"entities_involved": {
  "victims": ["Michael Usi"],
  "perpetrators": ["DPP"],
  "witnesses_sources": []
}
```

**Keywords Related:**
```json
"keywords_related_to_incident": ["political sabotage"]
```
