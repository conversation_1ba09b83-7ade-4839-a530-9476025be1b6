import { useState, useEffect } from 'react';
import { incidentsService } from '../services/incidentsService';
import { IncidentsResponse, IncidentFilters } from '../types/incidentsTypes';

export const useIncidents = (initialFilters: IncidentFilters = {}) => {
  const [data, setData] = useState<IncidentsResponse['data'] | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<IncidentFilters>(initialFilters);
  const fetchData = async (filterOverrides?: Partial<IncidentFilters>, append = false) => {
    const currentFilters = { ...filters, ...filterOverrides };
    setLoading(true);
    setError(null);
    
    try {
      const response = await incidentsService.getIncidents(currentFilters);
      if (append && data) {
        // Append new incidents to existing data
        setData({
          ...response.data,
          incidents: [...data.incidents, ...response.data.incidents]
        });
      } else {
        // Replace data entirely
        setData(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch incidents');
    } finally {
      setLoading(false);
    }
  };

  const updateFilters = (newFilters: Partial<IncidentFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const loadMore = () => {
    if (data && data.pagination.has_more) {
      const newOffset = (filters.offset || 0) + (filters.limit || 50);
      fetchData({ offset: newOffset }, true);
    }
  };

  useEffect(() => {
    fetchData();
  }, [filters]);

  return {
    data,
    loading,
    error,
    filters,
    updateFilters,
    loadMore,
    refetch: () => fetchData()
  };
};
