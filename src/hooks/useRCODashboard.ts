import { useState, useEffect } from 'react';
import { rcoDashboardService, RCODashboardResponse, RCODashboardFilters } from '../services/rcoDashboardService';

export const useRCODashboard = (initialFilters: RCODashboardFilters) => {
  const [data, setData] = useState<RCODashboardResponse['data'] | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<RCODashboardFilters>(initialFilters);

  const fetchData = async (filterOverrides?: Partial<RCODashboardFilters>) => {
    const currentFilters = { ...filters, ...filterOverrides };
    setLoading(true);
    setError(null);
    
    try {
      const response = await rcoDashboardService.getDashboardData(currentFilters);
      setData(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch RCO dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const updateFilters = (newFilters: Partial<RCODashboardFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  useEffect(() => {
    fetchData();
  }, [filters]);

  return {
    data,
    loading,
    error,
    filters,
    updateFilters,
    refetch: () => fetchData()
  };
};
