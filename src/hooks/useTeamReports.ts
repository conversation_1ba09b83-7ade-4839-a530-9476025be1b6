import { useState, useEffect } from 'react';
import { TeamReportData, TeamReportFilters, TeamName } from '../types/teamReportsTypes';
import { teamReportsService } from '../services/teamReportsService';

export const useTeamReports = (team: TeamName, filters: TeamReportFilters) => {
  const [data, setData] = useState<TeamReportData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchReport = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await teamReportsService.getTeamReport(team, filters);
      if (response.success) {
        setData(response.data);
      } else {
        setError('Failed to fetch team report');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };  useEffect(() => {
    fetchReport();
  }, [
    team, 
    filters.days, 
    filters.district, 
    filters.severity, 
    filters.incidentType, 
    filters.perpetrator, 
    filters.platform
  ]);

  return {
    data,
    loading,
    error,
    refetch: fetchReport,
  };
};
