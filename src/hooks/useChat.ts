import { useState, useCallback, useEffect } from 'react';
import { chatApiService, mockChatResponse } from '@/services/chatApiService';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
}

interface UseChatOptions {
  initialMessages?: Message[];
  conversationId?: string;
  mockMode?: boolean;
}

export const useChat = (options: UseChatOptions = {}) => {
  const {
    initialMessages = [],
    conversationId = "1", // Default to "1" as per your API
    mockMode = false // Set to false to use real API by default
  } = options;

  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const addMessage = useCallback((message: Message) => {
    setMessages(prev => [...prev, message]);
  }, []);

  const sendMessage = useCallback(async (text: string) => {
    if (!text.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: text.trim(),
      sender: 'user',
      timestamp: new Date()
    };

    addMessage(userMessage);
    setIsLoading(true);
    setError(null);

    try {
      let response;
        if (mockMode) {
        // Use mock response for development
        response = await mockChatResponse(text);
      } else {
        // Use actual API with correct payload format
        response = await chatApiService.sendMessage({
          message: text,
          conversationId: conversationId,
          includeHistory: true,
          startNewConversation: false
        });
      }      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: response.response,
        sender: 'ai',
        timestamp: new Date()
      };

      // Log additional response metadata for debugging
      if (response.platform) {
        console.log('🏷️ Platform:', response.platform);
      }
      if (response.model) {
        console.log('🧠 Model:', response.model);
      }
      if (response.conversationLength) {
        console.log('💬 Conversation Length:', response.conversationLength);
      }

      addMessage(aiMessage);
    } catch (error) {
      console.error('Chat error:', error);
      setError('Failed to send message. Please try again.');
      
      // Fallback response on error
      const fallbackMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: 'I apologize, but I\'m having trouble responding right now. Please try again in a moment.',
        sender: 'ai',
        timestamp: new Date()
      };
      addMessage(fallbackMessage);
    } finally {
      setIsLoading(false);
    }
  }, [conversationId, mockMode, addMessage]);

  const clearMessages = useCallback(() => {
    setMessages([]);
    setError(null);
  }, []);

  const retryLastMessage = useCallback(() => {
    if (messages.length >= 2) {
      const lastUserMessage = [...messages].reverse().find(m => m.sender === 'user');
      if (lastUserMessage) {
        // Remove the last AI response and retry
        setMessages(prev => prev.filter(m => 
          !(m.sender === 'ai' && m.timestamp > lastUserMessage.timestamp)
        ));
        sendMessage(lastUserMessage.text);
      }
    }
  }, [messages, sendMessage]);

  const exportMessages = useCallback(() => {
    const exportData = {
      messages,
      conversationId,
      exportDate: new Date().toISOString(),
      totalMessages: messages.length
    };
    return exportData;
  }, [messages, conversationId]);

  // Auto-scroll effect for new messages
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  
  useEffect(() => {
    if (shouldAutoScroll && messages.length > 0) {
      // Trigger scroll to bottom when new messages are added
      const timeoutId = setTimeout(() => {
        const element = document.querySelector('[data-chat-messages-end]');
        element?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
      
      return () => clearTimeout(timeoutId);
    }
  }, [messages, shouldAutoScroll]);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    addMessage,
    clearMessages,
    retryLastMessage,
    exportMessages,
    shouldAutoScroll,
    setShouldAutoScroll
  };
};
