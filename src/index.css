@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Leaflet Map Custom Styles */
.custom-popup .leaflet-popup-content-wrapper {
  @apply rounded-lg shadow-lg;
  background: white;
  border: 1px solid #d1d5db;
}

.custom-popup .leaflet-popup-content {
  @apply text-sm;
  margin: 8px 12px;
  line-height: 1.4;
}

.custom-popup .leaflet-popup-tip {
  background: white;
  border: 1px solid #d1d5db;
}

.district-label-marker {
  font-family: inherit;
}

/* Remove default leaflet popup close button styling */
.leaflet-popup-close-button {
  @apply text-gray-600 hover:text-gray-900;
}

/* Ensure map container has proper background */
.leaflet-container {
  background-color: #f8f9fa !important;
}

/* Hide leaflet attribution for cleaner look */
.leaflet-control-attribution {
  @apply text-xs opacity-60;
}

/* Clickable district styling */
.leaflet-interactive {
  cursor: pointer !important;
}

.leaflet-interactive[data-has-incidents="true"] {
  cursor: pointer !important;
}

.leaflet-interactive[data-has-incidents="false"] {
  cursor: default !important;
}

/* Fix z-index conflicts with modals */
.leaflet-container {
  z-index: 1 !important;
}

.leaflet-control-zoom {
  z-index: 2 !important;
}

.leaflet-control-attribution {
  z-index: 2 !important;
}

/* Ensure dialog/modal components are above leaflet */
[data-radix-popper-content-wrapper] {
  z-index: 9999 !important;
}

[data-radix-dialog-overlay] {
  z-index: 9998 !important;
}

[data-radix-dialog-content] {
  z-index: 9999 !important;
}