// Mock data structure for the PreventiveActions component

export interface Action {
  id: string;
  title: string;
  description: string;
  status: 'Not Started' | 'In Progress' | 'Completed' | 'Delayed';
  priority: 'Critical' | 'High' | 'Medium' | 'Low';
  category: string;
  assignedTo: string;
  dueDate: string;
  createdDate: string;
  completionPercentage: number;
  impact: 'Major' | 'Moderate' | 'Minor';
  tags: string[];
}

export interface ActionsByStatus {
  notStarted: number;
  inProgress: number;
  completed: number;
  delayed: number;
}

export interface ActionsByPriority {
  critical: number;
  high: number;
  medium: number;
  low: number;
}

export interface ActionsByCategory {
  category: string;
  count: number;
  color: string;
}

export interface ActionTrend {
  date: string;
  created: number;
  completed: number;
}

export interface ImpactAssessment {
  area: string;
  beforeScore: number;
  afterScore: number;
  change: number;
  color: string;
}

export interface Resource {
  id: string;
  name: string;
  type: 'Document' | 'Contact' | 'Tool' | 'Website';
  description: string;
  link: string;
  lastUpdated: string;
}

export interface ResponseTeam {
  id: string;
  name: string;
  role: string;
  organization: string;
  contact: string;
  availability: string;
}

export interface PreventiveActionsData {
  summary: {
    totalActions: number;
    completedActions: number;
    criticalPending: number;
    upcomingDeadlines: number;
    averageCompletion: number;
  };
  actionsByStatus: ActionsByStatus;
  actionsByPriority: ActionsByPriority;
  actionsByCategory: ActionsByCategory[];
  actionTrends: ActionTrend[];
  actions: Action[];
  impactAssessments: ImpactAssessment[];
  resources: Resource[];
  responseTeam: ResponseTeam[];
}

// Mock data for the PreventiveActions component
export const preventiveActionsData: PreventiveActionsData = {
  summary: {
    totalActions: 42,
    completedActions: 18,
    criticalPending: 3,
    upcomingDeadlines: 7,
    averageCompletion: 72
  },
  actionsByStatus: {
    notStarted: 8,
    inProgress: 16,
    completed: 18,
    delayed: 0
  },
  actionsByPriority: {
    critical: 5,
    high: 12,
    medium: 18,
    low: 7
  },
  actionsByCategory: [
    { category: "Public Information", count: 12, color: "#3b82f6" },
    { category: "Platform Engagement", count: 9, color: "#10b981" },
    { category: "Community Outreach", count: 8, color: "#f59e0b" },
    { category: "Media Monitoring", count: 7, color: "#8b5cf6" },
    { category: "Institutional Coordination", count: 6, color: "#ef4444" }
  ],
  actionTrends: [
    { date: "May 7", created: 3, completed: 1 },
    { date: "May 14", created: 5, completed: 2 },
    { date: "May 21", created: 8, completed: 4 },
    { date: "May 28", created: 12, completed: 6 },
    { date: "Jun 4", created: 14, completed: 5 }
  ],
  actions: [
    {
      id: "pa1",
      title: "Coordinate fact-checking with major platforms",
      description: "Establish direct communication channels with social media platforms for rapid response to viral misinformation",
      status: "In Progress",
      priority: "Critical",
      category: "Platform Engagement",
      assignedTo: "Sarah Banda",
      dueDate: "2025-06-10",
      createdDate: "2025-05-15",
      completionPercentage: 65,
      impact: "Major",
      tags: ["fact-checking", "platforms", "coordination"]
    },
    {
      id: "pa2",
      title: "Launch voter education campaign",
      description: "Roll out multimedia campaign to educate voters on election procedures and misinformation awareness",
      status: "In Progress",
      priority: "High",
      category: "Public Information",
      assignedTo: "John Phiri",
      dueDate: "2025-06-15",
      createdDate: "2025-05-10",
      completionPercentage: 80,
      impact: "Major",
      tags: ["voter education", "awareness", "media campaign"]
    },
    {
      id: "pa3",
      title: "Train community peace ambassadors",
      description: "Train influential community members to promote peaceful election participation and counter divisive narratives",
      status: "Completed",
      priority: "High",
      category: "Community Outreach",
      assignedTo: "Grace Mwale",
      dueDate: "2025-05-30",
      createdDate: "2025-05-05",
      completionPercentage: 100,
      impact: "Moderate",
      tags: ["training", "community", "peace ambassadors"]
    },
    {
      id: "pa4",
      title: "Develop rapid response protocols",
      description: "Establish clear procedures for responding to election-related incidents and disinformation",
      status: "In Progress",
      priority: "Critical",
      category: "Institutional Coordination",
      assignedTo: "David Chirwa",
      dueDate: "2025-06-08",
      createdDate: "2025-05-12",
      completionPercentage: 70,
      impact: "Major",
      tags: ["protocols", "response", "coordination"]
    },
    {
      id: "pa5",
      title: "Deploy content monitoring system",
      description: "Implement enhanced monitoring solution for real-time detection of election-related threats",
      status: "Not Started",
      priority: "Critical",
      category: "Media Monitoring",
      assignedTo: "Technical Team",
      dueDate: "2025-06-12",
      createdDate: "2025-05-25",
      completionPercentage: 0,
      impact: "Major",
      tags: ["monitoring", "technology", "detection"]
    }
  ],
  impactAssessments: [
    { area: "Misinformation Prevalence", beforeScore: 78, afterScore: 65, change: -13, color: "#10b981" },
    { area: "Hate Speech Incidents", beforeScore: 62, afterScore: 48, change: -14, color: "#10b981" },
    { area: "Public Trust in Process", beforeScore: 45, afterScore: 58, change: 13, color: "#10b981" },
    { area: "Media Coverage Balance", beforeScore: 52, afterScore: 55, change: 3, color: "#10b981" },
    { area: "Election Violence Risk", beforeScore: 68, afterScore: 62, change: -6, color: "#10b981" }
  ],
  resources: [
    {
      id: "res1",
      name: "Election Fact-Checking Portal",
      type: "Website",
      description: "Official website for verifying election-related claims and reporting misinformation",
      link: "https://factcheck.mec.gov.mw",
      lastUpdated: "2025-06-01"
    },
    {
      id: "res2",
      name: "Rapid Response Handbook",
      type: "Document",
      description: "Comprehensive guide for incident response during the election period",
      link: "/documents/rapid-response-handbook.pdf",
      lastUpdated: "2025-05-15"
    },
    {
      id: "res3",
      name: "Media Monitoring Dashboard",
      type: "Tool",
      description: "Real-time monitoring tool for election-related content across platforms",
      link: "/tools/monitoring-dashboard",
      lastUpdated: "2025-06-02"
    },
    {
      id: "res4",
      name: "Election Security Coordinator",
      type: "Contact",
      description: "Main point of contact for election security concerns and incidents",
      link: "tel:+************",
      lastUpdated: "2025-05-30"
    },
    {
      id: "res5",
      name: "Civic Education Materials",
      type: "Document",
      description: "Downloadable education materials for voter awareness campaigns",
      link: "/documents/civic-education-pack.zip",
      lastUpdated: "2025-05-20"
    }
  ],
  responseTeam: [
    {
      id: "team1",
      name: "Dr. Mary Mkandawire",
      role: "Response Team Lead",
      organization: "Electoral Commission",
      contact: "<EMAIL>",
      availability: "24/7"
    },
    {
      id: "team2",
      name: "James Kamanga",
      role: "Social Media Specialist",
      organization: "Digital Rights Initiative",
      contact: "<EMAIL>",
      availability: "Regular Hours"
    },
    {
      id: "team3",
      name: "Chief Inspector Banda",
      role: "Security Liaison",
      organization: "Police Service",
      contact: "<EMAIL>",
      availability: "24/7"
    },
    {
      id: "team4",
      name: "Victoria Mbewe",
      role: "Legal Expert",
      organization: "Law Society",
      contact: "<EMAIL>",
      availability: "On Call"
    },
    {
      id: "team5",
      name: "Technical Response Team",
      role: "Platform Moderation & Support",
      organization: "ICT Authority",
      contact: "<EMAIL>",
      availability: "24/7"
    }
  ]
};
