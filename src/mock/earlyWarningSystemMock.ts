// Mock data structure for the EarlyWarningSystem component

export interface Alert {
  id: string;
  title: string;
  description: string;
  timestamp: string;
  severity: 'Critical' | 'High' | 'Medium' | 'Low';
  status: 'New' | 'In Progress' | 'Resolved';
  source: string;
  location: string;
  category: string;
  color: string;
}

export interface TrendData {
  date: string;
  incidents: number;
  resolved: number;
}

export interface LocationHeatmapData {
  location: string;
  incidentCount: number;
  riskLevel: number;
}

export interface IncidentTypeData {
  type: string;
  count: number;
  color: string;
}

export interface CategoryBreakdownData {
  category: string;
  count: number;
  color: string;
}

export interface PredictionData {
  date: string;
  predictedIncidents: number;
  actualIncidents?: number;
}

export interface RiskIndicator {
  id: string;
  name: string;
  description: string;
  status: 'Normal' | 'Elevated' | 'High' | 'Critical';
  trend: 'Improving' | 'Worsening' | 'Stable';
  lastUpdated: string;
}

export interface EarlyWarningSystemData {
  summary: {
    activeIncidents: number;
    resolvedLastWeek: number;
    averageResponseTime: string;
    criticalAlerts: number;
  };
  recentAlerts: Alert[];
  trends: TrendData[];
  locationHeatmap: LocationHeatmapData[];
  incidentTypes: IncidentTypeData[];
  categoryBreakdown: CategoryBreakdownData[];
  predictions: PredictionData[];
  riskIndicators: RiskIndicator[];
}

// Mock data for the EarlyWarningSystem component
export const earlyWarningSystemData: EarlyWarningSystemData = {
  summary: {
    activeIncidents: 47,
    resolvedLastWeek: 32,
    averageResponseTime: "4h 12m",
    criticalAlerts: 3
  },
  recentAlerts: [
    {
      id: "alert1",
      title: "Potential voting irregularity reports",
      description: "Multiple social media posts claiming ballot stuffing in Lilongwe Central",
      timestamp: "2025-06-03T09:45:00Z",
      severity: "High",
      status: "New",
      source: "Twitter/X",
      location: "Lilongwe Central",
      category: "Election Integrity",
      color: "bg-red-100 text-red-800"
    },
    {
      id: "alert2",
      title: "Coordinated disinformation campaign detected",
      description: "Bot network spreading false claims about candidate disqualification",
      timestamp: "2025-06-03T08:30:00Z",
      severity: "Critical",
      status: "In Progress",
      source: "Multiple Platforms",
      location: "Nationwide",
      category: "Disinformation",
      color: "bg-red-100 text-red-800"
    },
    {
      id: "alert3",
      title: "Hate speech spike in Mzimba region",
      description: "Increasing ethnic tension in social media discussions about local candidates",
      timestamp: "2025-06-03T07:15:00Z",
      severity: "Medium",
      status: "In Progress",
      source: "Facebook",
      location: "Mzimba",
      category: "Hate Speech",
      color: "bg-orange-100 text-orange-800"
    },
    {
      id: "alert4",
      title: "Potential protest mobilization",
      description: "Calls for demonstration at electoral commission headquarters",
      timestamp: "2025-06-02T22:45:00Z",
      severity: "Medium",
      status: "In Progress",
      source: "WhatsApp Groups",
      location: "Blantyre",
      category: "Civil Unrest",
      color: "bg-orange-100 text-orange-800"
    },
    {
      id: "alert5",
      title: "Viral misinformation about polling station closures",
      description: "False information circulating about early closure of polling stations",
      timestamp: "2025-06-02T19:30:00Z",
      severity: "High",
      status: "Resolved",
      source: "Facebook, TikTok",
      location: "Southern Region",
      category: "Misinformation",
      color: "bg-green-100 text-green-800"
    }
  ],
  trends: [
    { date: "May 28", incidents: 12, resolved: 10 },
    { date: "May 29", incidents: 15, resolved: 12 },
    { date: "May 30", incidents: 18, resolved: 14 },
    { date: "May 31", incidents: 17, resolved: 15 },
    { date: "Jun 1", incidents: 22, resolved: 18 },
    { date: "Jun 2", incidents: 25, resolved: 20 },
    { date: "Jun 3", incidents: 28, resolved: 15 }
  ],
  locationHeatmap: [
    { location: "Lilongwe", incidentCount: 87, riskLevel: 75 },
    { location: "Blantyre", incidentCount: 65, riskLevel: 62 },
    { location: "Mzuzu", incidentCount: 42, riskLevel: 48 },
    { location: "Zomba", incidentCount: 36, riskLevel: 40 },
    { location: "Kasungu", incidentCount: 28, riskLevel: 35 },
    { location: "Mangochi", incidentCount: 25, riskLevel: 38 },
    { location: "Karonga", incidentCount: 18, riskLevel: 25 },
    { location: "Salima", incidentCount: 16, riskLevel: 22 }
  ],
  incidentTypes: [
    { type: "Misinformation", count: 120, color: "#3b82f6" },
    { type: "Hate Speech", count: 85, color: "#ef4444" },
    { type: "Technical Issues", count: 62, color: "#f59e0b" },
    { type: "Intimidation", count: 45, color: "#8b5cf6" },
    { type: "Process Violations", count: 38, color: "#10b981" }
  ],
  categoryBreakdown: [
    { category: "Election Integrity", count: 110, color: "#3b82f6" },
    { category: "Disinformation", count: 95, color: "#ef4444" },
    { category: "Hate Speech", count: 75, color: "#f59e0b" },
    { category: "Civil Unrest", count: 50, color: "#10b981" },
    { category: "Technical Issues", count: 35, color: "#8b5cf6" }
  ],
  predictions: [
    { date: "Jun 1", predictedIncidents: 23, actualIncidents: 22 },
    { date: "Jun 2", predictedIncidents: 26, actualIncidents: 25 },
    { date: "Jun 3", predictedIncidents: 30, actualIncidents: 28 },
    { date: "Jun 4", predictedIncidents: 34 },
    { date: "Jun 5", predictedIncidents: 38 },
    { date: "Jun 6", predictedIncidents: 45 },
    { date: "Jun 7", predictedIncidents: 52 }
  ],
  riskIndicators: [
    {
      id: "risk1",
      name: "Hate Speech Index",
      description: "Measures prevalence of hate speech and inflammatory content",
      status: "High",
      trend: "Worsening",
      lastUpdated: "Today at 14:30"
    },
    {
      id: "risk2",
      name: "Misinformation Spread",
      description: "Tracks speed and reach of election-related misinformation",
      status: "Critical",
      trend: "Worsening",
      lastUpdated: "Today at 13:45"
    },
    {
      id: "risk3",
      name: "Violence Incitement",
      description: "Monitors calls for violence or civil unrest",
      status: "Elevated",
      trend: "Stable",
      lastUpdated: "Today at 12:00"
    },
    {
      id: "risk4",
      name: "Election Process Trust",
      description: "Public sentiment regarding electoral process legitimacy",
      status: "Elevated",
      trend: "Improving",
      lastUpdated: "Today at 10:30"
    },
    {
      id: "risk5",
      name: "Media Polarization",
      description: "Measures partisan divide in media coverage",
      status: "Normal",
      trend: "Stable",
      lastUpdated: "Yesterday at 18:00"
    }
  ]
};
