// Mock data structure for the ElectionAnalytics component

export interface CandidateData {
  id: string;
  name: string;
  mentions: number;
  sentiment: number;
  color: string;
  keyTopics: string[];
  trend: 'increasing' | 'decreasing' | 'stable';
  changePercentage: number;
}

export interface NarrativeData {
  id: string;
  name: string;
  value: number;
  color: string;
  description: string;
}

export interface SentimentTrendData {
  date: string;
  positive: number;
  negative: number;
  neutral: number;
}

export interface LocationData {
  name: string;
  value: number;
  color: string;
}

export interface RiskFactor {
  id: number;
  title: string;
  level: 'Critical' | 'High' | 'Medium' | 'Low';
  color: string;
  trend: 'Increasing' | 'Decreasing' | 'Stable';
  lastUpdated: string;
  impact: 'Severe' | 'Moderate' | 'Minor';
}

export interface PreventiveAction {
  id: number;
  title: string;
  priority: 'High' | 'Medium' | 'Low';
  description: string;
  color: string;
}

export interface NarrativeTimelineData {
  date: string;
  fairness: number;
  policy: number;
  corruption: number;
  attacks: number;
  other: number;
}

export interface ElectionAnalyticsData {
  summary: {
    totalMonitoredPosts: number;
    totalMonitoredPostsChange: number;
    candidateMentions: number;
    candidateMentionsChange: number;
    activeAlerts: number;
    activeAlertsChange: number;
    riskLevel: string;
    riskLevelChange: string;
  };
  candidateData: CandidateData[];
  narrativeData: NarrativeData[];
  sentimentTrendData: SentimentTrendData[];
  locationData: LocationData[];
  riskFactors: RiskFactor[];
  preventiveActions: PreventiveAction[];
  narrativeTimeline: NarrativeTimelineData[];
  riskDistribution: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
}

// Mock data for the ElectionAnalytics component
export const electionAnalyticsData: ElectionAnalyticsData = {
  summary: {
    totalMonitoredPosts: 24669,
    totalMonitoredPostsChange: 12,
    candidateMentions: 9782,
    candidateMentionsChange: 8,
    activeAlerts: 47,
    activeAlertsChange: 15,
    riskLevel: "Medium",
    riskLevelChange: "No change"
  },
  candidateData: [
    { 
      id: "cand1", 
      name: "Candidate A", 
      mentions: 3240, 
      sentiment: 65, 
      color: "#3b82f6",
      keyTopics: ["Economy", "Healthcare", "Education"],
      trend: "increasing",
      changePercentage: 5
    },
    { 
      id: "cand2", 
      name: "Candidate B", 
      mentions: 2830, 
      sentiment: 48, 
      color: "#ef4444",
      keyTopics: ["Agriculture", "Infrastructure", "Trade"],
      trend: "stable",
      changePercentage: 0
    },
    { 
      id: "cand3", 
      name: "Candidate C", 
      mentions: 1970, 
      sentiment: 72, 
      color: "#10b981",
      keyTopics: ["Environment", "Youth", "Technology"],
      trend: "increasing",
      changePercentage: 8
    },
    { 
      id: "cand4", 
      name: "Candidate D", 
      mentions: 1540, 
      sentiment: 35, 
      color: "#f59e0b",
      keyTopics: ["Security", "Corruption", "Foreign Policy"],
      trend: "decreasing",
      changePercentage: -3
    },
    { 
      id: "cand5", 
      name: "Candidate E", 
      mentions: 1200, 
      sentiment: 50, 
      color: "#8b5cf6",
      keyTopics: ["Healthcare", "Education", "Women's Rights"],
      trend: "stable",
      changePercentage: 1
    }
  ],
  narrativeData: [
    { 
      id: "narr1", 
      name: "Election Fairness", 
      value: 35, 
      color: "#3b82f6",
      description: "Discussions about election integrity, transparency and fairness of the electoral process"
    },
    { 
      id: "narr2", 
      name: "Policy Debates", 
      value: 25, 
      color: "#10b981",
      description: "Discussions focusing on candidate policies, manifestos and development agendas"
    },
    { 
      id: "narr3", 
      name: "Corruption Allegations", 
      value: 20, 
      color: "#f59e0b",
      description: "Claims and discussions about corruption involving candidates or political parties"
    },
    { 
      id: "narr4", 
      name: "Personal Attacks", 
      value: 15, 
      color: "#ef4444",
      description: "Ad hominem attacks, character assassinations and smear campaigns"
    },
    { 
      id: "narr5", 
      name: "Other Topics", 
      value: 5, 
      color: "#8b5cf6",
      description: "Miscellaneous election-related discussions"
    }
  ],
  sentimentTrendData: [
    { date: "Jan", positive: 65, negative: 35, neutral: 40 },
    { date: "Feb", positive: 58, negative: 42, neutral: 45 },
    { date: "Mar", positive: 52, negative: 48, neutral: 38 },
    { date: "Apr", positive: 48, negative: 52, neutral: 42 },
    { date: "May", positive: 52, negative: 48, neutral: 35 },
    { date: "Jun", positive: 55, negative: 45, neutral: 40 }
  ],
  locationData: [
    { name: "Lilongwe", value: 35, color: "#3b82f6" },
    { name: "Blantyre", value: 30, color: "#10b981" },
    { name: "Mzuzu", value: 15, color: "#f59e0b" },
    { name: "Zomba", value: 12, color: "#ef4444" },
    { name: "Other", value: 8, color: "#8b5cf6" }
  ],
  riskFactors: [
    { 
      id: 1, 
      title: "Hate Speech Prevalence", 
      level: "Medium", 
      color: "bg-orange-100 text-orange-800", 
      trend: "Increasing",
      lastUpdated: "Today at 15:30",
      impact: "Moderate"
    },
    { 
      id: 2, 
      title: "Election Misinformation", 
      level: "High", 
      color: "bg-red-100 text-red-800", 
      trend: "Increasing",
      lastUpdated: "Today at 14:45",
      impact: "Severe"
    },
    { 
      id: 3, 
      title: "Regional Tensions", 
      level: "Low", 
      color: "bg-green-100 text-green-800", 
      trend: "Stable",
      lastUpdated: "Today at 12:20",
      impact: "Minor"
    },
    { 
      id: 4, 
      title: "Media Polarization", 
      level: "Medium", 
      color: "bg-orange-100 text-orange-800", 
      trend: "Stable",
      lastUpdated: "Today at 10:15",
      impact: "Moderate"
    },
    { 
      id: 5, 
      title: "Foreign Influence", 
      level: "Medium", 
      color: "bg-orange-100 text-orange-800", 
      trend: "Increasing",
      lastUpdated: "Yesterday at 18:30",
      impact: "Moderate"
    }
  ],
  preventiveActions: [
    { 
      id: 1, 
      title: "Increase monitoring of election misinformation narratives", 
      priority: "High", 
      description: "Implement rapid response messaging to counter misinformation", 
      color: "border-l-red-500 bg-red-50"
    },
    { 
      id: 2, 
      title: "Engage with community leaders", 
      priority: "Medium", 
      description: "Address regional tensions and promote peaceful dialogue", 
      color: "border-l-orange-500 bg-orange-50"
    },
    { 
      id: 3, 
      title: "Monitor hate speech prevalence", 
      priority: "Medium", 
      description: "Coordinate with platforms for content moderation", 
      color: "border-l-orange-500 bg-orange-50"
    },
    { 
      id: 4, 
      title: "Standard media monitoring", 
      priority: "Low", 
      description: "Continue standard procedures and regular reporting", 
      color: "border-l-green-500 bg-green-50"
    }
  ],
  narrativeTimeline: [
    { date: "Jan", fairness: 25, policy: 30, corruption: 15, attacks: 20, other: 10 },
    { date: "Feb", fairness: 28, policy: 28, corruption: 18, attacks: 18, other: 8 },
    { date: "Mar", fairness: 30, policy: 25, corruption: 20, attacks: 15, other: 10 },
    { date: "Apr", fairness: 32, policy: 22, corruption: 22, attacks: 16, other: 8 },
    { date: "May", fairness: 35, policy: 25, corruption: 20, attacks: 15, other: 5 }
  ],
  riskDistribution: {
    critical: 5,
    high: 20,
    medium: 45,
    low: 30
  }
};
