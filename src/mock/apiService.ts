// Mock API service for the Media Analysis Platform

import { electionAnalyticsData, ElectionAnalyticsData } from './electionAnalyticsMock';
import { earlyWarningSystemData, EarlyWarningSystemData } from './earlyWarningSystemMock';
import { preventiveActionsData, PreventiveActionsData } from './preventiveActionsMock';

// Simulate API response delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// API service class
export class ApiService {
  // Election Analytics
  static async getElectionAnalytics(): Promise<ElectionAnalyticsData> {
    await delay(800); // Simulate network delay
    return electionAnalyticsData;
  }

  static async getCandidateDetails(candidateId: string) {
    await delay(600);
    const candidate = electionAnalyticsData.candidateData.find(c => c.id === candidateId);
    if (!candidate) {
      throw new Error('Candidate not found');
    }
    return {
      ...candidate,
      detailedData: {
        recentStatements: [
          { date: "2025-06-01", content: "Statement on economic policy plans for the next term" },
          { date: "2025-05-28", content: "Response to corruption allegations from opposing candidates" },
          { date: "2025-05-25", content: "Announcement of rural development initiatives" }
        ],
        mediaAppearances: [
          { date: "2025-06-02", channel: "MBC TV", program: "Candidate Interview" },
          { date: "2025-05-30", channel: "Capital Radio", program: "Morning Debate" },
          { date: "2025-05-26", channel: "Times Newspaper", program: "Front Page Profile" }
        ],
        supportDemographics: {
          age: [
            { group: "18-24", percentage: 28 },
            { group: "25-34", percentage: 35 },
            { group: "35-44", percentage: 22 },
            { group: "45-64", percentage: 10 },
            { group: "65+", percentage: 5 }
          ],
          gender: [
            { group: "Male", percentage: 55 },
            { group: "Female", percentage: 45 }
          ],
          region: [
            { name: "Northern", percentage: 25 },
            { name: "Central", percentage: 45 },
            { name: "Southern", percentage: 30 }
          ]
        }
      }
    };
  }

  static async getNarrativeDetails(narrativeId: string) {
    await delay(600);
    const narrative = electionAnalyticsData.narrativeData.find(n => n.id === narrativeId);
    if (!narrative) {
      throw new Error('Narrative not found');
    }
    return {
      ...narrative,
      detailedData: {
        recentExamples: [
          { date: "2025-06-02", platform: "Facebook", content: "Example post content about this narrative" },
          { date: "2025-06-01", platform: "Twitter/X", content: "Another example related to this narrative" },
          { date: "2025-05-30", platform: "TikTok", content: "Video content discussing this narrative" }
        ],
        relatedHashtags: [
          { tag: "#ElectionFairness", count: 1245 },
          { tag: "#TransparentElection", count: 876 },
          { tag: "#VoterRights", count: 543 }
        ],
        influencers: [
          { name: "Media Outlet A", followers: 120000, impact: "High" },
          { name: "Political Commentator B", followers: 85000, impact: "Medium" },
          { name: "Civil Society Org C", followers: 65000, impact: "High" }
        ]
      }
    };
  }

  // Early Warning System
  static async getEarlyWarningSystemData(): Promise<EarlyWarningSystemData> {
    await delay(800);
    return earlyWarningSystemData;
  }

  // Early Warning Report - Real API Integration
  static async getEarlyWarningReport() {
    await delay(800);
    
    // This is the real API endpoint that should be used in production
    // For now, return the sample data structure you provided
    return {
      "success": true,
      "data": {
        "overview": {
          "criticalAlerts": {
            "current": 2,
            "change": "+2",
            "changeDirection": "increase",
            "changePeriod": "since yesterday"
          },
          "highRiskAreas": {
            "current": 0,
            "change": "+0",
            "changeDirection": "increase",
            "changePeriod": "since last week"
          },
          "weeklyIncidents": {
            "current": 137,
            "change": "+0%",
            "changeDirection": "increase",
            "changePeriod": "from previous week"
          },
          "responseRate": {
            "current": 0,
            "change": "+4%",
            "changeDirection": "increase",
            "changePeriod": "improvement"
          }
        },
        "alerts": [
          {
            "id": 1,
            "type": "Hate Speech",
            "severity": "Critical",
            "severityColor": "bg-red-100 text-red-800",
            "timestamp": "2025-08-18T06:30:01.000Z",
            "relativeTime": "2 hours ago",
            "location": "Lilongwe",
            "coordinates": {
              "latitude": -13.9626,
              "longitude": 33.7741
            },
            "source": "Facebook",
            "description": "Hate speech targeting specific groups detected with high engagement potential.",
            "impactScore": 100,
            "status": "active",
            "priority": 1,
            "tags": ["hate-speech", "high-risk"],
            "affectedPopulation": 150000,
            "verificationStatus": "verified",
            "totalPosts": 1,
            "evidencePosts": [
              {
                "id": "facebook_post_133",
                "platform": "Facebook",
                "author": {
                  "username": "fb_user_np9snc7r",
                  "displayName": "Anonymous User",
                  "verified": false,
                  "followers": 1145,
                  "accountAge": "2 months"
                },
                "content": "These people are bringing violence to our elections. They should not be allowed to vote in our districts. Share this message!",
                "originalContent": "These people are bringing violence to our elections. They should not be allowed to vote in our districts. Share this message!",
                "timestamp": "2025-08-18T06:30:01.000Z",
                "relativeTime": "2 hours ago",
                "engagement": {
                  "likes": 305,
                  "shares": 16,
                  "comments": 117,
                  "total": 438
                },
                "reach": {
                  "estimated": 30493,
                  "viral": true
                },
                "sentiment": -0.9,
                "toxicityScore": 0.85,
                "location": "Lilongwe",
                "language": "Mixed",
                "detectionMethods": ["hate-speech-classifier", "safety-scorer", "category-classifier"],
                "riskFactors": ["Hate speech detected", "High safety risk"],
                "status": "flagged"
              }
            ],
            "actions": [
              {
                "action": "Content flagged for review",
                "timestamp": "2025-08-18T06:45:01.000Z",
                "actor": "Automated System",
                "postsAffected": 1
              },
              {
                "action": "Alert sent to authorities",
                "timestamp": "2025-08-18T07:00:01.000Z",
                "actor": "System",
                "details": "Malawi Electoral Commission, Police HQ"
              }
            ],
            "analysisMetrics": {
              "totalEngagement": 9238,
              "uniqueAccounts": 145,
              "networkAnalysis": {
                "coordinatedBehavior": true,
                "botLikelihood": 0.75,
                "influenceNetwork": "High"
              },
              "geographicSpread": ["Lilongwe"],
              "peakViralityTime": "2025-08-18T06:30:01.000Z"
            }
          }
          // Additional alerts from your sample data...
        ],
        "trendData": [
          {
            "date": "2025-08-12",
            "hateSpeeech": 0,
            "misinformation": 0,
            "violence": 0,
            "fraud": 0
          },
          {
            "date": "2025-08-17",
            "hateSpeeech": 1,
            "misinformation": 5,
            "violence": 4,
            "fraud": 3
          },
          {
            "date": "2025-08-18",
            "hateSpeeech": 0,
            "misinformation": 0,
            "violence": 0,
            "fraud": 0
          }
        ],
        "regionData": [
          {
            "name": "National",
            "total": 76,
            "critical": 0,
            "high": 0,
            "medium": 0,
            "low": 76,
            "coordinates": {
              "latitude": -13.2543,
              "longitude": 34.3015
            }
          },
          {
            "name": "Lilongwe",
            "total": 11,
            "critical": 1,
            "high": 0,
            "medium": 0,
            "low": 10,
            "coordinates": {
              "latitude": -13.9626,
              "longitude": 33.7741
            }
          },
          {
            "name": "Blantyre",
            "total": 6,
            "critical": 1,
            "high": 1,
            "medium": 0,
            "low": 4,
            "coordinates": {
              "latitude": -15.7861,
              "longitude": 35.0058
            }
          }
        ],
        "alertTypes": [
          {
            "name": "Hate Speech",
            "count": 1,
            "avgSeverity": "85",
            "color": "bg-red-500"
          },
          {
            "name": "Misinformation",
            "count": 9,
            "avgSeverity": "25",
            "color": "bg-orange-500"
          },
          {
            "name": "Violence Threat",
            "count": 6,
            "avgSeverity": "52",
            "color": "bg-purple-500"
          },
          {
            "name": "Electoral Fraud",
            "count": 14,
            "avgSeverity": "16",
            "color": "bg-yellow-500"
          }
        ],
        "recentPosts": {
          "description": "Latest 10 flagged posts across all alert types",
          "posts": [
            {
              "id": "recent_136",
              "platform": "WhatsApp",
              "content": "Brothers, they are planning to rig the election in our district. Meet at the chief place tonight to discuss our response.",
              "timestamp": "2025-08-18T07:45:01.000Z",
              "alertType": "Electoral Fraud",
              "severity": "High",
              "location": "Zomba",
              "engagement": {
                "groupMembers": 173,
                "forwards": 33
              }
            }
          ]
        },
        "metadata": {
          "lastUpdated": "2025-08-18T08:40:50.716Z",
          "updateFrequency": "realtime",
          "totalAlerts": 5,
          "activeAlerts": 5,
          "resolvedAlerts": 0,
          "dataSource": "Multi-platform monitoring",
          "confidenceLevel": 0.89,
          "postsAnalyzed": 5,
          "postsInLast24Hours": 5
        }
      }
    };
  }

  static async getAlertDetails(alertId: string) {
    await delay(600);
    const alert = earlyWarningSystemData.recentAlerts.find(a => a.id === alertId);
    if (!alert) {
      throw new Error('Alert not found');
    }
    return {
      ...alert,
      detailedData: {
        relatedIncidents: [
          { id: "inc123", title: "Similar incident in different location", date: "2025-05-30" },
          { id: "inc124", title: "Previous occurrence of this type", date: "2025-05-25" }
        ],
        responseActions: [
          { action: "Alert verification team deployed", timestamp: "2025-06-03T09:50:00Z", status: "Completed" },
          { action: "Local authorities notified", timestamp: "2025-06-03T10:05:00Z", status: "Completed" },
          { action: "Public clarification message drafted", timestamp: "2025-06-03T10:30:00Z", status: "In Progress" }
        ],
        evidenceSources: [
          { type: "Social Media Posts", count: 24, firstDetected: "2025-06-03T09:30:00Z" },
          { type: "Media Reports", count: 2, firstDetected: "2025-06-03T09:45:00Z" },
          { type: "Community Reports", count: 5, firstDetected: "2025-06-03T10:00:00Z" }
        ],
        map: {
          latitude: -13.9626,
          longitude: 33.7741,
          zoom: 12
        }
      }
    };
  }

  static async submitAlertResponse(alertId: string, response: { action: string, notes: string }) {
    await delay(500);
    return { success: true, message: "Response submitted successfully" };
  }

  // Preventive Actions
  static async getPreventiveActionsData(): Promise<PreventiveActionsData> {
    await delay(800);
    return preventiveActionsData;
  }

  static async getActionDetails(actionId: string) {
    await delay(600);
    const action = preventiveActionsData.actions.find(a => a.id === actionId);
    if (!action) {
      throw new Error('Action not found');
    }
    return {
      ...action,
      detailedData: {
        timeline: [
          { date: action.createdDate, event: "Action created", user: "System Admin" },
          { date: "2025-05-20", event: "Action assigned", user: action.assignedTo },
          { date: "2025-05-25", event: "Status updated to In Progress", user: action.assignedTo },
          { date: "2025-06-01", event: "Progress update: 65% completed", user: action.assignedTo }
        ],
        relatedResources: preventiveActionsData.resources.slice(0, 2),
        notes: [
          { date: "2025-06-01", user: action.assignedTo, content: "Contacted platform representatives, waiting for response" },
          { date: "2025-05-25", user: "System Admin", content: "Priority upgraded to Critical based on recent events" }
        ],
        dependencies: [
          { id: "dep1", title: "Establish technical infrastructure", status: "Completed" },
          { id: "dep2", title: "Secure budget approval", status: "Completed" }
        ]
      }
    };
  }

  static async updateActionStatus(actionId: string, update: { status: string, completionPercentage: number, notes: string }) {
    await delay(500);
    return { success: true, message: "Action updated successfully" };
  }

  static async createNewAction(actionData: Partial<PreventiveActionsData['actions'][0]>) {
    await delay(800);
    return { 
      success: true, 
      message: "New action created successfully", 
      actionId: `pa${Math.floor(Math.random() * 1000)}` 
    };
  }
}

// API endpoints definition (for documentation purposes)
export const API_ENDPOINTS = {
  // Election Analytics
  ELECTION_ANALYTICS: "/api/election-analytics",
  CANDIDATE_DETAILS: "/api/candidate/:candidateId",
  NARRATIVE_DETAILS: "/api/narrative/:narrativeId",
  
  // Early Warning System
  EARLY_WARNING_SYSTEM: "/api/early-warning-system",
  ALERT_DETAILS: "/api/alert/:alertId",
  ALERT_RESPONSE: "/api/alert/:alertId/response",
  
  // Preventive Actions
  PREVENTIVE_ACTIONS: "/api/preventive-actions",
  ACTION_DETAILS: "/api/action/:actionId",
  UPDATE_ACTION: "/api/action/:actionId",
  CREATE_ACTION: "/api/action/create"
};
