# AI Chat Integration Documentation

## Overview
The floating AI chat feature provides users with an intelligent assistant that can help with platform navigation, data interpretation, and general queries. The chat is implemented as a floating widget that appears on all authenticated pages.

## Components

### 1. FloatingChat (`/src/components/FloatingChat.tsx`)
Main chat component with the following features:
- **Floating Icon**: Always visible chat button
- **Minimizable**: Can be minimized to save screen space
- **Maximizable**: Can be expanded for better conversation view
- **Settings Panel**: Access to chat configuration and actions

### 2. ChatComponents (`/src/components/ChatComponents.tsx`)
Reusable chat UI components:
- **ChatMessage**: Individual message bubbles with avatars
- **TypingIndicator**: Shows when AI is responding
- **ChatSettings**: Settings panel with actions

### 3. ChatApiService (`/src/services/chatApiService.ts`)
API service layer for chat functionality

## API Endpoints

### Chat Message Endpoint
```http
POST /api/chat
Content-Type: application/json

{
  "message": "string",
  "conversation_id": "string",
  "user_id": "string", // optional
  "session_id": "string" // optional
}
```

**Response:**
```json
{
  "response": "string",
  "conversation_id": "string",
  "timestamp": "ISO 8601 string",
  "message_id": "string"
}
```

### Chat History Endpoint
```http
GET /api/chat/history/{conversation_id}
```

**Response:**
```json
{
  "messages": [
    {
      "id": "string",
      "text": "string",
      "sender": "user|ai",
      "timestamp": "ISO 8601 string"
    }
  ],
  "conversation_id": "string"
}
```

### New Conversation Endpoint
```http
POST /api/chat/new
Content-Type: application/json

{
  "user_id": "string" // optional
}
```

**Response:**
```json
{
  "conversation_id": "string"
}
```

### Delete Conversation Endpoint
```http
DELETE /api/chat/conversation/{conversation_id}
```

## Features

### Core Features
- [x] Floating chat icon
- [x] Minimize/maximize functionality
- [x] Real-time messaging interface
- [x] Typing indicators
- [x] Message timestamps
- [x] User and AI avatars
- [x] Chat history
- [x] Settings panel

### Advanced Features
- [x] Export chat history (JSON format)
- [x] Clear chat history
- [x] Responsive design
- [x] Error handling with fallback responses
- [x] Mock responses for development

### Planned Features
- [ ] File upload support
- [ ] Voice message support
- [ ] Chat themes
- [ ] Multi-language support
- [ ] Context-aware responses based on current page

## Usage

### Basic Implementation
The chat is automatically available on all authenticated pages. No additional setup required.

### API Configuration
Update the chat service configuration:

```typescript
import { chatApiService } from '@/services/chatApiService';

// Configure API endpoint and authentication
chatApiService.setConfig('https://your-api-endpoint.com/api', 'your-api-key');
```

### Custom Message Types
Extend the Message interface for custom message types:

```typescript
interface CustomMessage extends Message {
  messageType?: 'text' | 'image' | 'file' | 'system';
  metadata?: Record<string, any>;
}
```

## Backend Integration Requirements

### Database Schema
Recommended tables for chat functionality:

#### conversations
```sql
CREATE TABLE conversations (
  id VARCHAR(255) PRIMARY KEY,
  user_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  title VARCHAR(255),
  is_active BOOLEAN DEFAULT true
);
```

#### messages
```sql
CREATE TABLE messages (
  id VARCHAR(255) PRIMARY KEY,
  conversation_id VARCHAR(255) REFERENCES conversations(id),
  sender ENUM('user', 'ai') NOT NULL,
  message_text TEXT NOT NULL,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  metadata JSON
);
```

### Authentication
The chat service expects standard bearer token authentication:
```http
Authorization: Bearer <your-jwt-token>
```

### Rate Limiting
Implement rate limiting on chat endpoints:
- Max 60 messages per minute per user
- Max 10 new conversations per hour per user

## Testing

### Mock Responses
The system includes mock responses for development:

```typescript
import { mockChatResponse } from '@/services/chatApiService';

const response = await mockChatResponse("Hello!");
console.log(response.response); // Random AI-like response
```

### Error Handling
The chat gracefully handles:
- Network errors
- API timeout
- Invalid responses
- Authentication failures

## Styling and Customization

### Theme Colors
- Primary: `bg-blue-600` (chat header, user messages)
- Secondary: `bg-gray-100` (AI messages, settings)
- Accent: `hover:bg-blue-700` (interactive elements)

### Size Variants
- Minimized: `w-80 h-12`
- Normal: `w-80 h-96`
- Maximized: `w-96 h-[600px]`

### Responsive Behavior
- Mobile: Full width with adjusted positioning
- Tablet: Reduced width but maintains functionality
- Desktop: Full feature set

## Security Considerations

1. **Input Sanitization**: All user inputs are sanitized before sending to API
2. **XSS Protection**: Message content is properly escaped
3. **Authentication**: Chat requires valid user session
4. **Rate Limiting**: Prevents spam and abuse
5. **Data Privacy**: Chat history can be cleared by user

## Performance Optimization

1. **Message Batching**: Multiple messages sent in single request when possible
2. **Virtual Scrolling**: For long conversation histories
3. **Lazy Loading**: Chat history loaded on demand
4. **Debounced Input**: Prevents excessive API calls while typing
5. **Connection Pooling**: Reuses WebSocket connections when available

## Troubleshooting

### Common Issues

**Chat not appearing:**
- Check authentication state
- Verify component is imported in App.tsx
- Check browser console for errors

**Messages not sending:**
- Verify API endpoint configuration
- Check network connectivity
- Review authentication tokens

**Styling issues:**
- Ensure Tailwind CSS is properly configured
- Check for conflicting CSS rules
- Verify all UI components are installed

### Debug Mode
Enable debug logging:

```typescript
// In development
if (process.env.NODE_ENV === 'development') {
  console.log('Chat Debug:', { messages, isLoading, currentUser });
}
```

## Future Enhancements

1. **Real-time Updates**: WebSocket integration for live conversations
2. **Rich Media**: Support for images, files, and links
3. **Chat Templates**: Pre-defined responses for common queries
4. **Analytics Integration**: Track chat usage and effectiveness
5. **Mobile App**: Native mobile chat experience
6. **Voice Interface**: Speech-to-text and text-to-speech capabilities
7. **AI Training**: Learn from conversations to improve responses
8. **Multi-user Chat**: Support for team conversations

## Support

For backend integration support or questions about the chat implementation, please refer to:
- API documentation at `/docs/api-documentation.md`
- Component documentation in individual files
- Test files for usage examples

Ready for backend integration! 🚀
