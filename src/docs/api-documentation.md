# API Documentation for the Media Analysis Platform for Elections

This document outlines the API structure for the Media Analysis Platform for Elections in Malawi. It includes endpoints, data formats, and payload examples to help both frontend and backend developers understand data requirements.

## Base URL

All API endpoints are based at: `https://api.mediaanalysis.mw/v1`

## Authentication

All API endpoints require authentication using JWT tokens:

```
Authorization: Bearer <token>
```

## Common Response Format

All endpoints use a consistent response format:

```json
{
  "success": true|false,
  "data": { ... },
  "message": "Success or error message",
  "timestamp": "2025-06-03T12:34:56Z"
}
```

## API Endpoints

### Authentication

#### POST /auth/login
Authenticates a user and returns a token.

**Request:**
```json
{
  "username": "<EMAIL>",
  "password": "password"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user123",
      "name": "Admin User",
      "email": "<EMAIL>",
      "role": "admin"
    }
  },
  "message": "Authentication successful",
  "timestamp": "2025-06-03T12:34:56Z"
}
```

### Election Analytics

#### GET /election-analytics
Returns comprehensive election analytics data.

**Response:** See [electionAnalyticsMock.ts](../mock/electionAnalyticsMock.ts) for the complete schema.

#### GET /candidate/{candidateId}
Returns detailed information about a specific candidate.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "cand1",
    "name": "Candidate A",
    "mentions": 3240,
    "sentiment": 65,
    "color": "#3b82f6",
    "keyTopics": ["Economy", "Healthcare", "Education"],
    "trend": "increasing",
    "changePercentage": 5,
    "detailedData": {
      "recentStatements": [
        { "date": "2025-06-01", "content": "Statement on economic policy plans" },
        { "date": "2025-05-28", "content": "Response to corruption allegations" }
      ],
      "mediaAppearances": [
        { "date": "2025-06-02", "channel": "MBC TV", "program": "Candidate Interview" }
      ],
      "supportDemographics": {
        "age": [
          { "group": "18-24", "percentage": 28 },
          { "group": "25-34", "percentage": 35 }
        ],
        "gender": [
          { "group": "Male", "percentage": 55 },
          { "group": "Female", "percentage": 45 }
        ],
        "region": [
          { "name": "Northern", "percentage": 25 },
          { "name": "Central", "percentage": 45 },
          { "name": "Southern", "percentage": 30 }
        ]
      }
    }
  },
  "message": "Candidate details retrieved successfully",
  "timestamp": "2025-06-03T12:34:56Z"
}
```

#### GET /narrative/{narrativeId}
Returns detailed information about a specific narrative.

**Response:** See apiService.ts for schema.

### Early Warning System

#### GET /early-warning-system
Returns early warning system data including alerts, trends, and risk indicators.

**Response:** See [earlyWarningSystemMock.ts](../mock/earlyWarningSystemMock.ts) for the complete schema.

#### GET /alert/{alertId}
Returns detailed information about a specific alert.

**Response:** See apiService.ts for schema.

#### POST /alert/{alertId}/response
Submit a response to an alert.

**Request:**
```json
{
  "action": "Issued public clarification",
  "notes": "Coordinated with Election Commission to release official statement"
}
```

**Response:**
```json
{
  "success": true,
  "data": null,
  "message": "Response submitted successfully",
  "timestamp": "2025-06-03T12:34:56Z"
}
```

### Preventive Actions

#### GET /preventive-actions
Returns data about preventive actions planned and taken.

**Response:** See [preventiveActionsMock.ts](../mock/preventiveActionsMock.ts) for the complete schema.

#### GET /action/{actionId}
Returns detailed information about a specific action.

**Response:** See apiService.ts for schema.

#### PUT /action/{actionId}
Update the status of a preventive action.

**Request:**
```json
{
  "status": "In Progress",
  "completionPercentage": 65,
  "notes": "Contacted platform representatives, waiting for response"
}
```

**Response:**
```json
{
  "success": true,
  "data": null,
  "message": "Action updated successfully",
  "timestamp": "2025-06-03T12:34:56Z"
}
```

#### POST /action/create
Create a new preventive action.

**Request:**
```json
{
  "title": "Establish community reporting channels",
  "description": "Create accessible methods for communities to report election-related incidents",
  "priority": "High",
  "category": "Community Outreach",
  "assignedTo": "Grace Mwale",
  "dueDate": "2025-06-25"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "pa123",
    "title": "Establish community reporting channels",
    "status": "Not Started",
    "createdDate": "2025-06-03T12:34:56Z"
  },
  "message": "New action created successfully",
  "timestamp": "2025-06-03T12:34:56Z"
}
```

## Data Sources

The API integrates data from multiple sources:

1. Social media platforms (Facebook, Twitter/X, TikTok)
2. News media (online news outlets, broadcast media transcripts)
3. Election Commission reports
4. Community reporting channels
5. Public opinion surveys

## Rate Limits

To ensure system stability, API endpoints are rate-limited:
- Standard users: 100 requests per minute
- Admin users: 500 requests per minute

## Error Codes

Common error codes:
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 429: Too Many Requests
- 500: Internal Server Error

## Websocket Updates

For real-time data, a websocket connection is available:
```
wss://api.mediaanalysis.mw/v1/ws
```

Supported events:
- `new-alert`: Notification of new system alerts
- `status-change`: Changes to action status
- `trend-update`: Updates to monitored trends

## Data Implementation Notes

1. Backend should implement filtering capabilities for all endpoints
2. Geographic data should be provided with coordinates for mapping features
3. All text content should include language identification for multilingual analysis
4. Timestamps should always be provided in ISO 8601 format with UTC timezone
5. Sentiment analysis should use standardized scores (0-100)
