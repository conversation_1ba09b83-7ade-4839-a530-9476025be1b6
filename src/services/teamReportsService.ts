import axios from 'axios';
import { TeamReportResponse, TeamReportFilters, TeamName } from '../types/teamReportsTypes';

const API_BASE_URL = 'http://localhost:3200/api';

interface RequestBodyItem {
  key: string;
  value: string;
  description?: string;
}

export const teamReportsService = {
  async getTeamReport(
    team: TeamName,
    filters: TeamReportFilters
  ): Promise<TeamReportResponse> {
    console.log(`Building request for team: ${team}`, filters); // Debug log
      // Build the request body in the expected format
    let requestBody: RequestBodyItem[];
    
    if (team === 'communications') {
      // Communications team uses platform and sentiment instead of district and severity
      requestBody = [
        { key: 'days', value: filters.days.toString() },
        { 
          key: 'platform', 
          value: filters.platform || 'all',
          description: 'Platform filter (all, Facebook, Twitter, Website, etc.)'
        },
        { 
          key: 'sentiment', 
          value: filters.sentiment || 'all',
          description: 'Sentiment filter (all, Positive, Negative, Neutral)'
        }
      ];
      console.log('Communications platform added:', filters.platform || 'all'); // Debug log
      console.log('Communications sentiment added:', filters.sentiment || 'all'); // Debug log
    } else {
      // All other teams use district and severity
      requestBody = [
        { key: 'days', value: filters.days.toString() },
        { key: 'district', value: filters.district },
        { key: 'severity', value: filters.severity }
      ];
    }    // Add team-specific parameters
    if (team === 'mesp') {
      requestBody.push({
        key: 'incidentType',
        value: filters.incidentType || 'all',
        description: 'Incident type filter'
      });
      console.log('MESP incidentType added:', filters.incidentType || 'all'); // Debug log
    }
    
    // Add general platform and perpetrator filters for non-Communications teams
    if (team !== 'communications' && filters.platform && filters.platform !== 'all') {
      requestBody.push({
        key: 'platform',
        value: filters.platform,
        description: 'Platform filter'
      });
      console.log('Platform added:', filters.platform); // Debug log
    }
    
    if (team !== 'communications' && filters.perpetrator && filters.perpetrator !== 'all') {
      requestBody.push({
        key: 'perpetrator',
        value: filters.perpetrator,
        description: 'Perpetrator filter'
      });
      console.log('Perpetrator added:', filters.perpetrator); // Debug log
    }

    const url = `${API_BASE_URL}/reports/teams/${team === 'communications' ? 'communication' : team}`;
    const data = JSON.stringify(requestBody);
    
    console.log(`Final URL:`, url); // Debug log
    console.log(`Request body (array):`, requestBody); // Debug log
    console.log(`Request body (JSON string):`, data); // Debug log
    
    const config = {
      method: 'get' as const,
      maxBodyLength: Infinity,
      url: url,
      headers: { 
        'Content-Type': 'application/json'
      },
      data: data
    };

    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      console.error(`Failed to fetch ${team} report:`, error);
      throw new Error(`Failed to fetch ${team} report: ${error}`);
    }
  },
};
