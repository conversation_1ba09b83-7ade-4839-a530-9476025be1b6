// API Test utility for Magwero Chat
import { chatApiService } from './chatApiService';

export const testChatAPI = async () => {
  console.log('🔧 Testing Magwero Chat API...');
  
  try {
    const testMessage = {
      message: "Hello, this is a test message. Can you confirm the API is working?",
      conversationId: "1",
      includeHistory: true,
      startNewConversation: false
    };

    console.log('📤 Sending test message:', testMessage);
    
    const response = await chatApiService.sendMessage(testMessage);
    
    console.log('✅ API Test Successful!');
    console.log('📥 Response:', response);
    
    return {
      success: true,
      response,
      message: 'API connection successful'
    };
    
  } catch (error) {
    console.error('❌ API Test Failed:', error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'API connection failed'
    };
  }
};

// Test function specifically for your endpoint
export const testMagweroAPI = async () => {
  console.log('🔧 Testing Magwero API at http://localhost:3200/api/gemini-chat/chat');
  
  const testPayload = {
    message: "Why was Magwero developed?",
    conversationId: "1",
    includeHistory: true,
    startNewConversation: false
  };

  try {
    const response = await fetch('http://localhost:3200/api/gemini-chat/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }    const data = await response.json();
    console.log('✅ Direct API Test Successful!');
    console.log('📊 Response Data:', data);
    
    if (data.success && data.data) {
      console.log('🤖 AI Response:', data.data.response);
      console.log('🔗 Conversation ID:', data.data.conversationId);
      console.log('🏷️ Platform:', data.data.platform);
      console.log('👥 Developed By:', data.data.developedBy);
      console.log('🧠 Model:', data.data.model);
    }
    
    return { success: true, data };
    
  } catch (error) {
    console.error('❌ Direct API Test Failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

// Export for use in components
export { chatApiService };

// Add this to window for easy testing in browser console
if (typeof window !== 'undefined') {
  (window as any).testMagweroChat = testMagweroAPI;
  (window as any).testChatAPI = testChatAPI;
  console.log('🛠️ Chat API test functions added to window:');
  console.log('   - window.testMagweroChat()');  
  console.log('   - window.testChatAPI()');
}
