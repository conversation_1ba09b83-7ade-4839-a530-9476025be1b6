import axios from 'axios';
import { 
  IncidentsResponse, 
  IncidentFilters, 
  MALAWI_DISTRICTS, 
  INCIDENT_CATEGORIES,
  PLATFORMS,
  MALAWI_CANDIDATES,
  POLITICAL_ENTITIES
} from '../types/incidentsTypes';

const API_BASE_URL = 'http://localhost:3200/api';

export const incidentsService = {  async getIncidents(filters: IncidentFilters = {}): Promise<IncidentsResponse> {
    console.log('Fetching incidents with filters:', filters);
      const params = new URLSearchParams({
      limit: (filters.limit || 50).toString(),
      offset: (filters.offset || 0).toString(),
      severity: filters.severity || 'all',
      district: filters.district || 'all',
      category: filters.category || 'all',
      platform: filters.platform || 'all',
      candidate: filters.candidate || 'all',
      political_entity: filters.political_entity || 'all',
      prominent_name: filters.prominent_name || 'all',
      frequent_phrase: filters.frequent_phrase || 'all',
      political_electoral_misconduct: filters.political_electoral_misconduct || 'all',
      discrimination_disadvantaged: filters.discrimination_disadvantaged || 'all',
      law_enforcement_misconduct: filters.law_enforcement_misconduct || 'all',
      physical_integrity_violation: filters.physical_integrity_violation || 'all',
      gender_based_violence: filters.gender_based_violence || 'all',
      political_attacks_harassment: filters.political_attacks_harassment || 'all',      pwa_attacks: filters.pwa_attacks || 'all',
      protests_demonstrations: filters.protests_demonstrations || 'all',
      perpetrator_profile: filters.perpetrator_profile || 'all',
      victim_profile: filters.victim_profile || 'all',
      sentiment: filters.sentiment || 'all',
      report_period: filters.report_period || 'all_time',
      date_from: filters.date_from || '',
      date_to: filters.date_to || ''
    });
    
    const url = `${API_BASE_URL}/reports/incidents?${params.toString()}`;
    console.log('Incidents URL:', url);
    
    try {
      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch incidents:', error);
      throw new Error(`Failed to fetch incidents: ${error}`);
    }
  }
};
