// Election Monitoring API service
import type { Incident, FilterOptions, ElectionMonitoringDashboardData } from '../types/electionMonitoringEnhanced';

class ElectionMonitoringAPI {
  private baseURL: string;
  
  constructor() {
    this.baseURL = 'http://localhost:3200/api/election';
  }

  /**
   * Get filtered incidents data
   */
  async getIncidents(filters: FilterOptions = {}, page = 1, limit = 50): Promise<{ data: Incident[], total: number, page: number, totalPages: number }> {
    const params = new URLSearchParams();
    
    // Add pagination parameters
    params.append('page', page.toString());
    params.append('limit', limit.toString());
    
    // Add all possible filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(key, v));
        } else {
          params.append(key, value.toString());
        }
      }
    });
    
    try {
      // In development, we'll simulate this with a timeout and mock data
      // In production, this would be a real API call
      // const response = await fetch(`${this.baseURL}/data?${params}`);
      // return await response.json();
      
      await new Promise(resolve => setTimeout(resolve, 800)); // Simulate network delay
      
      // Mock response - this would be replaced with actual API call in production
      return {
        data: [], // Mock data would go here
        total: 1245,
        page,
        totalPages: Math.ceil(1245 / limit)
      };
    } catch (error) {
      console.error('Error fetching incidents:', error);
      throw error;
    }
  }

  /**
   * Get dashboard summary statistics
   */
  async getStatistics(): Promise<any> {
    try {
      // In production: return fetch(`${this.baseURL}/stats`).then(res => res.json());
      await new Promise(resolve => setTimeout(resolve, 500));
      
      return {
        total_incidents: 1245,
        new_incidents_today: 15,
        high_severity_incidents: 87,
        new_high_severity_today: 3,
        districts_affected: 15,
        districts_coverage_percentage: 68,
        platforms_monitored: [854, 326, 48, 17],
        platform_names: ["Facebook", "Twitter", "WhatsApp", "Website"]
      };
    } catch (error) {
      console.error('Error fetching statistics:', error);
      throw error;
    }
  }

  /**
   * Get full dashboard data
   */
  async getDashboardData(): Promise<ElectionMonitoringDashboardData> {
    try {
      // In production: return fetch(`${this.baseURL}/dashboard`).then(res => res.json());
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // This is mock data - would come from the backend API in production
      return {
        stats: {
          total_incidents: 1245,
          new_incidents_today: 15,
          high_severity_incidents: 87,
          new_high_severity_today: 3,
          districts_affected: 15,
          districts_coverage_percentage: 68,
          platforms_monitored: [854, 326, 48, 17],
          platform_names: ["Facebook", "Twitter", "WhatsApp", "Website"]
        },
        incidents: [], // Would contain incident data
        categoryBreakdown: [
          { name: "Political/electoral misconduct", count: 478, percentage: 38, color: "#3b82f6" },
          { name: "Civil Rights Restrictions", count: 356, percentage: 29, color: "#10b981" },
          { name: "Political Attacks", count: 245, percentage: 20, color: "#f59e0b" },
          { name: "Law Enforcement Misconduct", count: 98, percentage: 8, color: "#ef4444" },
          { name: "Protests/Demonstrations", count: 68, percentage: 5, color: "#8b5cf6" }
        ],
        severityByDistrict: [
          { district: "Lilongwe", critical: 8, high: 24, medium: 65, low: 48 },
          { district: "Blantyre", critical: 5, high: 18, medium: 59, low: 42 },
          { district: "Mzuzu", critical: 3, high: 12, medium: 38, low: 27 },
          { district: "Zomba", critical: 2, high: 9, medium: 32, low: 21 }
        ],
        platformAnalysis: [
          { platform: "Facebook", incidents: 854, percentage: 69, color: "#1877f2" },
          { platform: "Twitter", incidents: 326, percentage: 26, color: "#1da1f2" },
          { platform: "WhatsApp", incidents: 48, percentage: 4, color: "#25d366" },
          { platform: "Website", incidents: 17, percentage: 1, color: "#6b7280" }
        ],
        trendData: [
          { date: "2025-06-01", incidents: 32, critical: 2, high: 8, medium: 14, low: 8 },
          { date: "2025-06-02", incidents: 38, critical: 3, high: 10, medium: 16, low: 9 },
          { date: "2025-06-03", incidents: 35, critical: 2, high: 9, medium: 15, low: 9 },
          { date: "2025-06-04", incidents: 42, critical: 4, high: 12, medium: 18, low: 8 },
          { date: "2025-06-05", incidents: 39, critical: 3, high: 10, medium: 17, low: 9 },
          { date: "2025-06-06", incidents: 45, critical: 5, high: 14, medium: 18, low: 8 },
          { date: "2025-06-07", incidents: 52, critical: 6, high: 16, medium: 20, low: 10 }
        ],
        districtData: [
          { name: "Lilongwe", incidents: 145, highSeverity: 32, critical: 8, coordinates: [33.7833, -13.9833] },
          { name: "Blantyre", incidents: 124, highSeverity: 23, critical: 5, coordinates: [35.0050, -15.7861] },
          { name: "Mzuzu", incidents: 80, highSeverity: 15, critical: 3, coordinates: [34.0207, -11.4656] },
          { name: "Zomba", incidents: 64, highSeverity: 11, critical: 2, coordinates: [35.3188, -15.3833] }
        ],
        trendingAlerts: [
          { id: "alert1", title: "High-severity incident: Voter intimidation reported in Lilongwe", severity: "High", time: "15 minutes ago", location: "Lilongwe Central" },
          { id: "alert2", title: "Critical: Misinformation campaign detected on Facebook", severity: "Critical", time: "45 minutes ago", location: "National" },
          { id: "alert3", title: "Resource politicization in Zomba district", severity: "Medium", time: "2 hours ago", location: "Zomba" }
        ]
      };
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      throw error;
    }
  }

  /**
   * Analyze content for potential incidents
   */
  async analyzeContent(content: string, metadata: any = {}): Promise<any> {
    try {
      // In production: 
      // return fetch(`${this.baseURL}/analyze`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ content, metadata })
      // }).then(res => res.json());
      
      await new Promise(resolve => setTimeout(resolve, 1200));
      
      // Mock analysis response
      return {
        result: "potential_incident",
        confidence: 0.89,
        categories: [
          { name: "Political/electoral misconduct", confidence: 0.92 },
          { name: "Civil Rights Restrictions", confidence: 0.45 }
        ],
        severityEstimate: "High",
        recommendedAction: "Review",
        entities: {
          locations: ["Lilongwe"],
          people: ["Candidate A"],
          organizations: ["Political Party X"]
        }
      };
    } catch (error) {
      console.error('Error analyzing content:', error);
      throw error;
    }
  }
}

export default ElectionMonitoringAPI;