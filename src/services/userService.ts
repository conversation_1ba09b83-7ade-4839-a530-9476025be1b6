import {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  UsersListResponse,
  UserStatsResponse,
  UserFilters,
  ApiResponse,
  AdminChangePasswordRequest,
  AuthenticationError,
  AuthorizationError,
} from '@/types/userTypes';
import { authService } from './authService';

const API_BASE_URL = 'http://localhost:3200/api';

class UserService {
  private async fetchWithAuth<T>(
    url: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = authService.getToken();
    
    if (!token) {
      throw new AuthenticationError('No authentication token found');
    }

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...options.headers,
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (response.status === 401) {
        authService.logout();
        throw new AuthenticationError('Session expired. Please login again.');
      }

      if (response.status === 403) {
        throw new AuthorizationError('You do not have permission to perform this action.');
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof AuthenticationError || error instanceof AuthorizationError) {
        throw error;
      }
      console.error('API request error:', error);
      throw new Error('Network error. Please check your connection and try again.');
    }
  }

  async getUsers(filters: UserFilters = {}): Promise<UsersListResponse> {
    const queryParams = new URLSearchParams();
    
    if (filters.page) queryParams.append('page', filters.page.toString());
    if (filters.limit) queryParams.append('limit', filters.limit.toString());
    if (filters.role) queryParams.append('role', filters.role);
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.is_active !== undefined) queryParams.append('is_active', filters.is_active.toString());

    const url = `${API_BASE_URL}/users${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    
    return this.fetchWithAuth<UsersListResponse>(url);
  }

  async getUserById(id: string | number): Promise<ApiResponse<User>> {
    return this.fetchWithAuth<ApiResponse<User>>(`${API_BASE_URL}/users/${id}`);
  }

  async createUser(userData: CreateUserRequest): Promise<ApiResponse<User>> {
    return this.fetchWithAuth<ApiResponse<User>>(`${API_BASE_URL}/users`, {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async updateUser(id: string | number, userData: UpdateUserRequest): Promise<ApiResponse<User>> {
    return this.fetchWithAuth<ApiResponse<User>>(`${API_BASE_URL}/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  async deleteUser(id: string | number): Promise<ApiResponse> {
    return this.fetchWithAuth<ApiResponse>(`${API_BASE_URL}/users/${id}`, {
      method: 'DELETE',
    });
  }

  async getUserStats(): Promise<UserStatsResponse> {
    return this.fetchWithAuth<UserStatsResponse>(`${API_BASE_URL}/users/stats`);
  }

  async changeUserPassword(
    id: string | number, 
    passwordData: AdminChangePasswordRequest
  ): Promise<ApiResponse> {
    return this.fetchWithAuth<ApiResponse>(`${API_BASE_URL}/users/${id}/password`, {
      method: 'PUT',
      body: JSON.stringify(passwordData),
    });
  }

  async testEndpoint(data: any): Promise<ApiResponse> {
    return this.fetchWithAuth<ApiResponse>(`${API_BASE_URL}/users/test`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Utility methods for validation
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 255;
  }

  validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  validateFullName(name: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (name.length < 2 || name.length > 255) {
      errors.push('Full name must be between 2 and 255 characters');
    }

    if (!/^[a-zA-Z\s\-']+$/.test(name)) {
      errors.push('Full name can only contain letters, spaces, hyphens, and apostrophes');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Role checking utilities
  canManageUsers(userRole: string): boolean {
    return userRole === 'admin';
  }

  canViewUsers(userRole: string): boolean {
    return ['admin', 'moderator'].includes(userRole);
  }

  canEditUser(currentUserRole: string, targetUserRole: string): boolean {
    if (currentUserRole === 'admin') return true;
    return false; // Only admins can edit users
  }

  canDeleteUser(currentUserRole: string, targetUserRole: string): boolean {
    if (currentUserRole === 'admin' && targetUserRole !== 'admin') return true;
    return false; // Admins can delete non-admin users
  }

  // Format user data for display
  formatUserForDisplay(user: User) {
    return {
      ...user,
      roleLabel: this.getRoleLabel(user.role),
      statusLabel: user.is_active ? 'Active' : 'Inactive',
      createdAtFormatted: new Date(user.created_at).toLocaleDateString(),
      lastLoginFormatted: user.last_login 
        ? new Date(user.last_login).toLocaleDateString()
        : 'Never',
    };
  }

  private getRoleLabel(role: string): string {
    const roleLabels: Record<string, string> = {
      admin: 'Administrator',
      moderator: 'Moderator',
      viewer: 'Viewer',
    };
    return roleLabels[role] || role;
  }
}

export const userService = new UserService();
