import {
  LoginRequest,
  LoginResponse,
  User,
  AuthUser,
  ApiResponse,
  AuthenticationError,
  AuthorizationError,
} from '@/types/userTypes';

const API_BASE_URL = 'http://localhost:3200/api';
const TOKEN_KEY = 'auth_token';
const USER_KEY = 'auth_user';

class AuthService {
  private token: string | null = null;
  private user: AuthUser | null = null;

  constructor() {
    this.loadFromStorage();
  }

  private loadFromStorage(): void {
    try {
      this.token = localStorage.getItem(TOKEN_KEY);
      const userStr = localStorage.getItem(USER_KEY);
      this.user = userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('Error loading auth data from storage:', error);
      this.clearStorage();
    }
  }

  private saveToStorage(): void {
    try {
      if (this.token) {
        localStorage.setItem(TOKEN_KEY, this.token);
      } else {
        localStorage.removeItem(TOKEN_KEY);
      }

      if (this.user) {
        localStorage.setItem(USER_KEY, JSON.stringify(this.user));
      } else {
        localStorage.removeItem(USER_KEY);
      }
    } catch (error) {
      console.error('Error saving auth data to storage:', error);
    }
  }

  private clearStorage(): void {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(USER_KEY);
    this.token = null;
    this.user = null;
  }

  private async fetchWithAuth<T>(
    url: string,
    options: RequestInit = {}
  ): Promise<T> {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (response.status === 401) {
        this.logout();
        throw new AuthenticationError('Session expired. Please login again.');
      }

      if (response.status === 403) {
        throw new AuthorizationError('You do not have permission to perform this action.');
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof AuthenticationError || error instanceof AuthorizationError) {
        throw error;
      }
      console.error('API request error:', error);
      throw new Error('Network error. Please check your connection and try again.');
    }
  }

  async login(email: string, password: string): Promise<AuthUser> {
    const loginData: LoginRequest = { email, password };

    const response = await this.fetchWithAuth<LoginResponse>(
      `${API_BASE_URL}/users/login`,
      {
        method: 'POST',
        body: JSON.stringify(loginData),
      }
    );

    if (!response.success) {
      throw new AuthenticationError(response.message || 'Login failed');
    }

    this.token = response.data.token;
    this.user = response.data.user;
    this.saveToStorage();

    return this.user;
  }

  logout(): void {
    this.clearStorage();
  }

  async getCurrentUser(): Promise<AuthUser> {
    if (!this.token) {
      throw new AuthenticationError('No authentication token found');
    }

    const response = await this.fetchWithAuth<ApiResponse<User>>(
      `${API_BASE_URL}/users/me`
    );

    if (!response.success) {
      throw new Error(response.message || 'Failed to get user data');
    }

    this.user = response.data;
    this.saveToStorage();

    return this.user;
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    const response = await this.fetchWithAuth<ApiResponse>(
      `${API_BASE_URL}/users/me/password`,
      {
        method: 'PUT',
        body: JSON.stringify({ currentPassword, newPassword }),
      }
    );

    if (!response.success) {
      throw new Error(response.message || 'Failed to change password');
    }
  }

  getToken(): string | null {
    return this.token;
  }

  getUser(): AuthUser | null {
    return this.user;
  }

  isAuthenticated(): boolean {
    return !!(this.token && this.user);
  }

  hasRole(role: string): boolean {
    return this.user?.role === role;
  }

  hasPermission(permission: string): boolean {
    if (!this.user) return false;
    
    // Admin has all permissions
    if (this.user.role === 'admin') return true;
    
    // Check specific permissions based on role
    const rolePermissions: Record<string, string[]> = {
      moderator: ['view_users', 'moderate_content', 'view_reports'],
      viewer: ['view_reports'],
    };

    const userPermissions = rolePermissions[this.user.role] || [];
    return userPermissions.includes(permission);
  }

  isTokenExpired(): boolean {
    if (!this.token) return true;

    try {
      const payload = JSON.parse(atob(this.token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch (error) {
      console.error('Error checking token expiration:', error);
      return true;
    }
  }

  async refreshTokenIfNeeded(): Promise<void> {
    if (this.isTokenExpired()) {
      this.logout();
      throw new AuthenticationError('Session expired. Please login again.');
    }
  }

  updateUser(user: AuthUser): void {
    this.user = user;
    this.saveToStorage();
  }
}

export const authService = new AuthService();
