// Chat API Service
// This service handles all chat-related API calls

export interface ChatMessage {
  message: string;
  conversationId?: string;
  includeHistory?: boolean;
  startNewConversation?: boolean;
  user_id?: string;
  session_id?: string;
}

export interface ChatResponse {
  response: string;
  conversationId?: string;
  conversation_id?: string;
  timestamp?: string;
  message_id?: string;
  platform?: string;
  developedBy?: string;
  model?: string;
  messageLength?: number;
  conversationLength?: number;
}

export interface ApiResponse {
  success: boolean;
  data: ChatResponse;
}

export interface ChatHistory {
  messages: Array<{
    id: string;
    text: string;
    sender: 'user' | 'ai';
    timestamp: string;
  }>;
  conversation_id: string;
}

class ChatApiService {
  private baseUrl: string;
  private apiKey?: string;

  constructor(baseUrl: string = 'http://localhost:3200/api/gemini-chat', apiKey?: string) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
  }

  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`;
    }

    return headers;
  }
  /**
   * Send a message to the AI chat
   */
  async sendMessage(messageData: ChatMessage): Promise<ChatResponse> {
    try {
      const payload = {
        message: messageData.message,
        conversationId: messageData.conversationId || "1",
        includeHistory: messageData.includeHistory !== false, // Default to true
        startNewConversation: messageData.startNewConversation || false
      };

      console.log('Sending chat message:', payload);

      const response = await fetch(`${this.baseUrl}/chat`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Chat API error response:', errorText);
        throw new Error(`Chat API error: ${response.status} ${response.statusText}`);
      }      const data = await response.json();
      console.log('Chat API response:', data);
      
      // Handle the nested response format
      if (data.success && data.data) {
        const responseData = data.data;
        return {
          response: responseData.response || 'No response received',
          conversationId: responseData.conversationId || responseData.conversation_id,
          timestamp: responseData.timestamp || new Date().toISOString(),
          message_id: responseData.message_id || Date.now().toString(),
          platform: responseData.platform,
          developedBy: responseData.developedBy,
          model: responseData.model,
          messageLength: responseData.messageLength,
          conversationLength: responseData.conversationLength
        };
      } else {
        // Fallback for different response format
        return {
          response: data.response || data.message || 'No response received',
          conversationId: data.conversationId || data.conversation_id,
          timestamp: data.timestamp || new Date().toISOString(),
          message_id: data.message_id || Date.now().toString()
        };
      }
    } catch (error) {
      console.error('Error sending chat message:', error);
      throw error;
    }
  }

  /**
   * Get chat history for a conversation
   */
  async getChatHistory(conversationId: string): Promise<ChatHistory> {
    try {
      const response = await fetch(`${this.baseUrl}/chat/history/${conversationId}`, {
        method: 'GET',
        headers: this.getHeaders(),
      });

      if (!response.ok) {
        throw new Error(`Chat history API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching chat history:', error);
      throw error;
    }
  }

  /**
   * Start a new chat conversation
   */
  async startNewConversation(userId?: string): Promise<{ conversation_id: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/chat/new`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({ user_id: userId }),
      });

      if (!response.ok) {
        throw new Error(`New conversation API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error starting new conversation:', error);
      throw error;
    }
  }

  /**
   * Delete a chat conversation
   */
  async deleteConversation(conversationId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/chat/conversation/${conversationId}`, {
        method: 'DELETE',
        headers: this.getHeaders(),
      });

      if (!response.ok) {
        throw new Error(`Delete conversation API error: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error deleting conversation:', error);
      throw error;
    }
  }

  /**
   * Set API configuration
   */
  setConfig(baseUrl?: string, apiKey?: string) {
    if (baseUrl) this.baseUrl = baseUrl;
    if (apiKey) this.apiKey = apiKey;
  }
}

// Create and export a singleton instance
export const chatApiService = new ChatApiService();

// Mock responses for development/testing
export const mockChatResponses = [
  "I'm here to help with Malawi media monitoring! What would you like to know about the data I've collected?",
  "Based on the media data I've monitored from various platforms in Malawi, I can provide insights on that topic.",
  "That's an interesting question about Malawi's media landscape. Let me analyze the data I have...",
  "I understand your concern. Based on my monitoring of Malawi media sources, here's what I've observed:",
  "Great question! This relates to patterns I've detected in Malawi's social media and news platforms.",
  "I can help you with that analysis. Let me walk you through what the Magwero platform has detected.",
  "From the media monitoring data I have from Malawi sources, here's what I can tell you:",
  "I see what you're looking for. Based on my analysis of Malawi media platforms, here are the insights:",
  "That's a common pattern in Malawi's media landscape. Here's what the data shows:",
  "Let me provide context from the media monitoring I've conducted across Malawi platforms.",
];

/**
 * Mock chat service for development
 */
export const mockChatResponse = (message: string): Promise<ChatResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const randomResponse = mockChatResponses[Math.floor(Math.random() * mockChatResponses.length)];
      resolve({
        response: randomResponse,
        conversation_id: 'mock-conversation-id',
        timestamp: new Date().toISOString(),
        message_id: Date.now().toString(),
      });
    }, 1000 + Math.random() * 2000); // Random delay between 1-3 seconds
  });
};

export default ChatApiService;
