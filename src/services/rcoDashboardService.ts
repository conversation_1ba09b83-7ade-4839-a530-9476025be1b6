import axios from 'axios';

const API_BASE_URL = 'http://localhost:3200/api';

export interface RCODashboardFilters {
  days: number;
  district: string;
  severity: string;
  includeAnalysis?: boolean;
}

export interface RCODashboardResponse {
  success: boolean;
  data: {
    team: string;
    period: string;
    overview: {
      total_incidents: number;
      avg_severity: string;
      districts_affected: number;
      platforms_monitored: number;
    };
    key_metrics: {
      undp_mentions: any[];
      international_incidents: any[];
      donor_concerns: any[];
    };
    top_entities: Array<{
      entity: string;
      count: number;
    }>;
    top_keywords: Array<{
      keyword: string;
      count: number;
    }>;
    early_warning: {
      recent_high_severity: Array<{
        id: number;
        post_summary: string;
        primary_category: string;
        severity_level: string;
        district: string;
        created_at: string;
        source_url: string;
      }>;
      trend_data: Array<{
        incident_date: string;
        daily_count: number;
        avg_severity: string;
      }>;
      timestamp: string;
    };
    election_analytics: {
      category_breakdown: Array<{
        primary_category: string;
        count: number;
      }>;
      district_breakdown: Array<{
        district: string;
        count: number;
      }>;
      electoral_misconduct: Array<{
        electoral_misconduct: string;
        count: number;
      }>;
      filters: {
        days: number;
        district: string;
        severity: string;
        focus: string;
      };
    };
    recommendations: Array<{
      priority: string;
      action: string;
      rationale: string;
    }>;
  };
  message: string;
}

export const rcoDashboardService = {
  async getDashboardData(filters: RCODashboardFilters): Promise<RCODashboardResponse> {
    console.log('Fetching RCO dashboard data with filters:', filters);
    
    const params = new URLSearchParams({
      days: filters.days.toString(),
      district: filters.district,
      severity: filters.severity,
      includeAnalysis: (filters.includeAnalysis ?? true).toString()
    });
    
    const url = `${API_BASE_URL}/reports/teams/rco/dashboard?${params.toString()}`;
    console.log('RCO Dashboard URL:', url);
    
    try {
      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch RCO dashboard data:', error);
      throw new Error(`Failed to fetch RCO dashboard data: ${error}`);
    }
  }
};
