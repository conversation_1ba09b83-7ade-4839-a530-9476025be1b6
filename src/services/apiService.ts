import { DashboardApiResponse } from '@/types/dashboardTypes';

const API_BASE_URL = 'http://localhost:3200/api';

class ApiService {
  private async fetchWithErrorHandling<T>(url: string, options?: RequestInit): Promise<T> {
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request error:', error);
      throw error;
    }
  }

  // Dashboard API endpoints
  async getDashboardData(): Promise<DashboardApiResponse> {
    return this.fetchWithErrorHandling<DashboardApiResponse>(
      `${API_BASE_URL}/election/dashboard`
    );
  }

  // Health check endpoint
  async healthCheck(): Promise<{ status: string }> {
    return this.fetchWithErrorHandling<{ status: string }>(`${API_BASE_URL}/health`);
  }

  // Early Warning Report endpoint
  async getEarlyWarningReport(): Promise<any> {
    return this.fetchWithErrorHandling<any>(`${API_BASE_URL}/early-warning/report`);
  }
}

export const apiService = new ApiService();
