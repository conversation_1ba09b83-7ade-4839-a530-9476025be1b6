import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ElectionAnalytics } from "@/components/ElectionAnalytics";
import { EarlyWarningSystem } from "@/components/EarlyWarningSystem";
import { PreventiveActions } from "@/components/PreventiveActions";
import ThematicIssueReport from "@/components/ThematicIssueReport";
import HotspotMappingReport from "@/components/HotspotMappingReport";
import SocioEconomicRisksReport from "@/components/SocioEconomicRisksReport";
import CapacityNeedsAssessmentReport from "@/components/CapacityNeedsAssessmentReport";
import ConflictTypologyReport from "@/components/ConflictTypologyReport";
import { 
  FileText, 
  Calendar, 
  TrendingUp, 
  AlertTriangle, 
  Target, 
  Shield, 
  BarChart3, 
  AlertCircle,
  Vote,
  <PERSON><PERSON><PERSON>ctagon,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  MapPin,
  Users,
  DollarSign,
  Map
} from "lucide-react";

const AllReports = () => {
  const [activeTab, setActiveTab] = useState("election");

  const reportTabs = [
    {
      id: "early-warning",
      label: "Early Warning Report",
      icon: AlertOctagon,
      description: "Early warning system for potential threats and incidents"
    },
    {
      id: "preventive-action",
      label: "Preventive Action Report",
      icon: ShieldCheck,
      description: "Preventive actions to mitigate election-related risks"
    },
    {
      id: "misinformation",
      label: "Misinformation and Disinformation Report",
      icon: Shield,
      description: "Tracking and analysis of false information spread"
    },
    {
      id: "thematic",
      label: "Thematic Issue Report",
      icon: Hash,
      description: "Volume, sentiment, key actors, and narratives around specific themes"
    },
    {
      id: "conflict-typology",
      label: "Conflict Typology Report",
      icon: AlertTriangle,
      description: "Analysis of conflict dynamics, types of violence, and classification"
    },
    {
      id: "hotspot-mapping",
      label: "Crime & Violence Hotspot Mapping",
      icon: Map,
      description: "Crime and violence mapping reports with potential hotspots"
    },
    {
      id: "socio-economic",
      label: "Socio-economic Risks Report",
      icon: DollarSign,
      description: "Comprehensive risks and their nexus with conflict dimensions"
    },
    {
      id: "capacity-needs",
      label: "Capacity Needs Assessment",
      icon: Users,
      description: "Capacity needs mapping for peacebuilding and conflict prevention"
    },
    {
      id: "crisis",
      label: "Crisis Risk Dashboard Integration Report",
      icon: AlertCircle,
      description: "Integration with crisis risk assessment systems"
    },
    {
      id: "election",
      label: "Election Confirmatory Report",
      icon: Vote,
      description: "Election-related monitoring and confirmation reports"
    }
  ];

  const PlaceholderContent = ({ title, description }: { title: string; description: string }) => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {reportTabs.find(tab => tab.label === title)?.icon && 
            React.createElement(reportTabs.find(tab => tab.label === title)!.icon, { className: "w-5 h-5" })
          }
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <FileText className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Report Content Coming Soon</h3>
          <p className="text-gray-600 max-w-md">
            {description}. The content for this report will be implemented based on your requirements.
          </p>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">All Reports</h1>
        <p className="text-gray-600 mt-2">Comprehensive reporting dashboard for monitoring and analysis</p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 lg:grid-cols-5 gap-1">
          {reportTabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <TabsTrigger 
                key={tab.id} 
                value={tab.id}
                className="flex items-center gap-1 text-xs px-1 py-2"
              >
                <Icon className="w-3 h-3" />
                <span className="hidden sm:inline truncate text-xs">{tab.label.split(' ')[0]}</span>
              </TabsTrigger>
            );
          })}
        </TabsList>

        {reportTabs.map((tab) => (
          <TabsContent key={tab.id} value={tab.id} className="mt-6">
            {tab.id === 'election' ? (
              <ElectionAnalytics />
            ) : tab.id === 'early-warning' ? (
              <EarlyWarningSystem />
            ) : tab.id === 'preventive-action' ? (
              <PreventiveActions />
            ) : tab.id === 'thematic' ? (
              <ThematicIssueReport />
            ) : tab.id === 'hotspot-mapping' ? (
              <HotspotMappingReport />
            ) : tab.id === 'socio-economic' ? (
              <SocioEconomicRisksReport />
            ) : tab.id === 'capacity-needs' ? (
              <CapacityNeedsAssessmentReport />
            ) : tab.id === 'conflict-typology' ? (
              <ConflictTypologyReport />
            ) : (
              <PlaceholderContent title={tab.label} description={tab.description} />
            )}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default AllReports;