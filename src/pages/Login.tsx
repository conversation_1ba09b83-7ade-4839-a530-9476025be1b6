
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/App";
import { loginSchema, LoginFormData } from "@/lib/validationSchemas";
import { AuthenticationError } from "@/types/userTypes";

const Login = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { login } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    try {
      await login(data.email, data.password);
      toast({
        title: "Login Successful",
        description: "Welcome to Magwero Media Monitoring System",
      });
      navigate("/dashboard");
    } catch (error) {
      console.error('Login error:', error);
      let errorMessage = "Login failed. Please try again.";

      if (error instanceof AuthenticationError) {
        errorMessage = error.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast({
        title: "Login Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <div className="min-h-screen bg-blue-600 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <Card className="bg-white rounded-lg shadow-xl border-2 border-yellow-400">
          <CardContent className="p-8">            {/* Logos Section */}
            <div className="flex justify-center mb-8">
              <div className="p-2 bg-white rounded-lg shadow-lg">
                <div className="flex items-center justify-center">
                  <img 
                    src="/lovable-uploads/3e2b6588-d21b-42b6-8b8f-30cf6f521314.png" 
                    alt="UNDP and MAGWERO logos" 
                    className="w-[15rem] object-contain"
           
                  />
                </div>
              </div>
            </div>            {/* Title */}
            <div className="text-center mb-8">
              <h1 className="text-gray-600 text-lg font-normal">Media Analysis Platform for Election</h1>
            </div>            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">              <div className="space-y-2">
                <Label htmlFor="email" className="text-gray-700 font-bold text-lg">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  {...register("email")}
                  className="h-12 border-gray-200 rounded-md focus:ring-blue-600 focus:border-blue-600 font-normal"
                />
                {errors.email && (
                  <p className="text-sm text-red-600">{errors.email.message}</p>
                )}
              </div>
                <div className="space-y-2">
                <Label htmlFor="password" className="text-gray-700 font-bold text-lg">Password</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter your password"
                  {...register("password")}
                  className="h-12 border-gray-200 rounded-md focus:ring-blue-600 focus:border-blue-600 font-normal"
                />
                {errors.password && (
                  <p className="text-sm text-red-600">{errors.password.message}</p>
                )}
              </div>
                <Button 
                type="submit" 
                className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white font-normal rounded-md"
                disabled={isLoading}
              >
                {isLoading ? "Signing in..." : "Sign In"}
              </Button>
            </form>

            {/* Footer */}
            <div className="mt-8 text-center">
              <p className="text-gray-400 text-sm">Powered by UNDP Malawi</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Login;
