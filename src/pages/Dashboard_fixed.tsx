import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from "recharts";
import { 
  AlertTriangle, 
  TrendingUp, 
  Users, 
  Shield,
  Bell,
  Monitor,
  BarChart3,
  Settings,
  FileText,
  UserCheck,
  Vote as VoteIcon,
  AlertOctagon,
  ShieldCheck
} from "lucide-react";
import { Navigation } from "@/components/Navigation";
import { AlertCard } from "@/components/AlertCard";
import { SocialMediaFeed } from "@/components/SocialMediaFeed";
import { ElectionAnalytics } from "@/components/ElectionAnalytics";
import { EarlyWarningSystem } from "@/components/EarlyWarningSystem";
import { PreventiveActions } from "@/components/PreventiveActions";
//import { ElectionMonitoringDashboard } from "@/components/ElectionMonitoringDashboard";
import Reports from "./Reports";
import SystemUsers from "./SystemUsers";

interface DashboardProps {
  onLogout: () => void;
}

// Mock data
const platformData = [
  { name: "Facebook", value: 45, color: "#1877F2" },
  { name: "Twitter/X", value: 30, color: "#1DA1F2" },
  { name: "TikTok", value: 25, color: "#FF0050" }
];

const sentimentData = [
  { day: "Mon", positive: 75, negative: 15 },
  { day: "Tue", positive: 78, negative: 18 },
  { day: "Wed", positive: 72, negative: 20 },
  { day: "Thu", positive: 80, negative: 16 },
  { day: "Fri", positive: 82, negative: 14 },
  { day: "Sat", positive: 79, negative: 17 },
  { day: "Sun", positive: 76, negative: 19 }
];

const locationData = [
  { name: "Lilongwe", posts: 8400 },
  { name: "Blantyre", posts: 6200 },
  { name: "Mzuzu", posts: 3400 },
  { name: "Zomba", posts: 2800 },
  { name: "Kasungu", posts: 2100 },
  { name: "Mangochi", posts: 1800 }
];

const Dashboard = ({ onLogout }: DashboardProps) => {
  const [activeTab, setActiveTab] = useState("dashboard");

  // Debug logging
  const handleTabChange = (tab: string) => {
    console.log('Dashboard: Changing tab from', activeTab, 'to', tab);
    setActiveTab(tab);
  };

  console.log('Dashboard: Current activeTab is', activeTab);

  return (
    <div className="h-screen bg-gray-50 flex flex-col overflow-hidden">
      {/* Fixed Header */}
      <div className="flex-shrink-0">
        <Navigation activeTab={activeTab} setActiveTab={handleTabChange} onLogout={onLogout} />
      </div>
      
      <div className="flex flex-1 overflow-hidden">
        {/* Fixed Sidebar */}
        <aside className="w-64 bg-white shadow-sm flex-shrink-0 overflow-y-auto">
          <div className="p-6">
            <div className="space-y-2">
              <button 
                onClick={() => handleTabChange("dashboard")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "dashboard" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <BarChart3 className="w-5 h-5" />
                <span>Dashboard</span>
              </button>
              <button 
                onClick={() => handleTabChange("monitoring")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "monitoring" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <Monitor className="w-5 h-5" />
                <span>Live Monitoring</span>
              </button>
              <button 
                onClick={() => handleTabChange("analytics")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "analytics" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <TrendingUp className="w-5 h-5" />
                <span>Analytics</span>
              </button>
              <button 
                onClick={() => handleTabChange("election_monitoring")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "election_monitoring" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <Shield className="w-5 h-5" />
                <span>Election Monitoring</span>
              </button>
              <button 
                onClick={() => handleTabChange("election_analytics")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "election_analytics" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <VoteIcon className="w-5 h-5" />
                <span>Election Analytics</span>
              </button>
              <button 
                onClick={() => handleTabChange("early_warning")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "early_warning" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <AlertOctagon className="w-5 h-5" />
                <span>Early Warning System</span>
              </button>
              <button 
                onClick={() => handleTabChange("preventive_actions")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "preventive_actions" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <ShieldCheck className="w-5 h-5" />
                <span>Preventive Actions</span>
              </button>
              <button 
                onClick={() => handleTabChange("alerts")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "alerts" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <Bell className="w-5 h-5" />
                <span>Alerts</span>
              </button>
              <button 
                onClick={() => handleTabChange("reports")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "reports" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <FileText className="w-5 h-5" />
                <span>Reports</span>
              </button>
              <button 
                onClick={() => handleTabChange("users")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "users" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <UserCheck className="w-5 h-5" />
                <span>System Users</span>
              </button>
            </div>          </div>
        </aside>

        {/* Scrollable Main Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="p-6">
          {activeTab === "dashboard" && (
            <div className="space-y-6">
              <h1 className="text-3xl font-bold text-gray-900">📊 Social Media Analytics Dashboard</h1>
              
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Posts</p>
                        <p className="text-2xl font-bold text-gray-900">25,384</p>
                      </div>
                      <BarChart3 className="w-8 h-8 text-blue-500" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Active Users</p>
                        <p className="text-2xl font-bold text-gray-900">12,492</p>
                      </div>
                      <Users className="w-8 h-8 text-green-500" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Alerts</p>
                        <p className="text-2xl font-bold text-gray-900">15</p>
                      </div>
                      <AlertTriangle className="w-8 h-8 text-orange-500" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Engagement</p>
                        <p className="text-2xl font-bold text-gray-900">87%</p>
                      </div>
                      <TrendingUp className="w-8 h-8 text-purple-500" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Charts */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Platform Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={platformData}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          dataKey="value"
                        >
                          {platformData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Sentiment Analysis</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={sentimentData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="day" />
                        <YAxis />
                        <Tooltip />
                        <Line type="monotone" dataKey="positive" stroke="#10b981" />
                        <Line type="monotone" dataKey="negative" stroke="#ef4444" />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>

              {/* Location Data */}
              <Card>
                <CardHeader>
                  <CardTitle>Posts by Location</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={locationData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="posts" fill="#3b82f6" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === "monitoring" && <SocialMediaFeed />}
          {activeTab === "analytics" && <div className="p-6"><h2 className="text-2xl font-bold">Analytics Dashboard</h2><p>Detailed analytics coming soon...</p></div>}
          {activeTab === "election_monitoring" && <ElectionMonitoringDashboard />}
          {activeTab === "election_analytics" && <ElectionAnalytics />}
          {activeTab === "early_warning" && <EarlyWarningSystem />}
          {activeTab === "preventive_actions" && <PreventiveActions />}          {activeTab === "alerts" && <AlertCard />}
          {activeTab === "reports" && <Reports />}
          {activeTab === "users" && <SystemUsers />}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Dashboard;
