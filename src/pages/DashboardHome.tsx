import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  <PERSON><PERSON>,
  <PERSON>,
  Cell
} from "recharts";
import { 
  AlertTriangle, 
  TrendingUp, 
  Shield,
  RefreshCw,
  Activity,
  Users,
  MapPin
} from "lucide-react";
import { useDashboardData } from "@/hooks/useDashboardData";
import { PLATFORM_COLORS, SEVERITY_COLORS } from "@/types/dashboardTypes";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import SimpleLeafletMap from "@/components/SimpleLeafletMap";

const DashboardHome = () => {
  const { dashboardData, loading, error, refetch } = useDashboardData(60000); // Auto-refresh every minute

  if (loading && !dashboardData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <RefreshCw className="w-6 h-6 animate-spin" />
          <span>Loading dashboard data...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <AlertTriangle className="w-12 h-12 text-red-500" />
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900">Error Loading Dashboard</h3>
          <p className="text-gray-600">{error}</p>
          <Button 
            onClick={refetch} 
            className="mt-4"
            variant="outline"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="flex items-center justify-center h-64">
        <span>No dashboard data available</span>
      </div>
    );
  }

  // Prepare chart data
  const districtChartData = dashboardData.by_district
    .filter(item => item.district && item.district.trim() !== '')
    .slice(0, 10)
    .map(item => ({
      name: item.district,
      incidents: item.count
    }));

  const platformChartData = dashboardData.by_platform.map(item => ({
    name: item.platform,
    value: item.count,
    color: PLATFORM_COLORS[item.platform] || "#6b7280"
  }));

  const severityChartData = dashboardData.by_severity.map(item => ({
    name: item.severity_level,
    value: item.count,
    color: SEVERITY_COLORS[item.severity_level] || "#6b7280"
  }));

  const monthlyTrendData = dashboardData.monthly_trend.map(item => ({
    month: item.month,
    incidents: item.count
  }));

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Election Monitoring Dashboard</h1>
          <p className="text-gray-600 mt-2">Real-time insights from social media monitoring</p>
        </div>
        <Button 
          onClick={refetch} 
          variant="outline" 
          size="sm"
          disabled={loading}
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Incidents</p>
                <p className="text-3xl font-bold text-blue-600">{dashboardData.summary.total_incidents.toLocaleString()}</p>
              </div>
              <Activity className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Platforms Monitored</p>
                <p className="text-3xl font-bold text-green-600">{dashboardData.summary.platforms}</p>
              </div>
              <Shield className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Categories Tracked</p>
                <p className="text-3xl font-bold text-orange-600">{dashboardData.summary.categories}</p>
              </div>
              <Users className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Districts Covered</p>
                <p className="text-3xl font-bold text-purple-600">{dashboardData.summary.districts}</p>
              </div>
              <MapPin className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Interactive Map Section */}
      <SimpleLeafletMap 
        statisticsData={dashboardData.by_district.reduce((acc, item) => {
          if (item.district && item.district.trim()) {
            acc[item.district] = item.count;
          }
          return acc;
        }, {} as Record<string, number>)}
        categoryData={{
          security_incidents: dashboardData.by_category.find(cat => 
            cat.primary_category.toLowerCase().includes('security'))?.count || 0,
          elections_incidents: dashboardData.by_category.find(cat => 
            cat.primary_category.toLowerCase().includes('election'))?.count || 0,
          health_environment_incidents: dashboardData.by_category.find(cat => 
            cat.primary_category.toLowerCase().includes('health') || 
            cat.primary_category.toLowerCase().includes('environment'))?.count || 0,
          gender_incidents: dashboardData.by_category.find(cat => 
            cat.primary_category.toLowerCase().includes('gender'))?.count || 0,
          governance_hr_incidents: dashboardData.by_category.find(cat => 
            cat.primary_category.toLowerCase().includes('governance') || 
            cat.primary_category.toLowerCase().includes('human rights'))?.count || 0,
        }}
        severityData={dashboardData.by_severity.reduce((acc, item) => {
          acc[item.severity_level] = item.count;
          return acc;
        }, {} as Record<string, number>)}
        loading={loading}
        showCategories={false}
      />

      {/* Category Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Incidents by Category</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="divide-y divide-gray-200">
            {dashboardData.by_category.map((category, index) => (
              <div key={index} className="flex items-center justify-between py-4 first:pt-0 last:pb-0">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    {category.primary_category}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {((category.count / dashboardData.summary.total_incidents) * 100).toFixed(1)}% of total incidents
                  </p>
                </div>
                <div className="text-right">
                  <span className="text-lg font-bold text-blue-600">
                    {category.count}
                  </span>
                  <p className="text-xs text-gray-500">incidents</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* District Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Incidents by District</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={districtChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" angle={-45} textAnchor="end" height={60} />
                <YAxis />
                <Tooltip />
                <Bar dataKey="incidents" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Platform Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Incidents by Platform</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={platformChartData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={120}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {platformChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
            <div className="mt-4 flex flex-wrap gap-2">
              {platformChartData.map((item, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="text-sm">{item.name}: {item.value}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Severity Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Incidents by Severity</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={severityChartData}
                  cx="50%"
                  cy="50%"
                  outerRadius={120}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {severityChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
            <div className="mt-4 flex flex-wrap gap-2">
              {severityChartData.map((item, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="text-sm">{item.name}: {item.value}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Monthly Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Monthly Incident Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={monthlyTrendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="incidents" stroke="#3b82f6" strokeWidth={3} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Incidents */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Incidents</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {dashboardData.recent_incidents.slice(0, 5).map((incident) => (
              <div key={incident.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className={getSeverityColor(incident.severity_level)}>
                        {incident.severity_level}
                      </Badge>
                      <span className="text-sm text-gray-500">#{incident.id}</span>
                      <span className="text-sm text-gray-500">
                        {incident.district || 'Unknown District'}
                      </span>
                    </div>
                    <p className="text-sm text-gray-700 mb-2">
                      {incident.post_summary}
                    </p>
                    <p className="text-xs text-gray-500">
                      Category: {incident.primary_category}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-gray-500">
                      {new Date(incident.created_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

    </div>
  );
};

export default DashboardHome;
