import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Save, 
  AlertTriangle, 
  Mail, 
  Bell, 
  Database, 
  Shield, 
  Zap,
  Globe,
  Clock,
  Users
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const PlatformSettings: React.FC = () => {
  const { toast } = useToast();
  
  // Alert Threshold Settings
  const [alertSettings, setAlertSettings] = useState({
    highRiskThreshold: 85,
    mediumRiskThreshold: 60,
    lowRiskThreshold: 30,
    sentimentThreshold: -0.5,
    viralityThreshold: 1000,
    enableAlerts: true,
  });

  // Email Configuration
  const [emailSettings, setEmailSettings] = useState({
    alertEmails: '<EMAIL>, <EMAIL>, <EMAIL>',
    reportEmails: '<EMAIL>, <EMAIL>',
    smtpServer: 'smtp.magwero.gov.mw',
    smtpPort: '587',
    enableEmailAlerts: true,
    enableDailyReports: true,
  });

  // Monitoring Parameters
  const [monitoringSettings, setMonitoringSettings] = useState({
    scanInterval: 15, // minutes
    keywordRefreshRate: 60, // minutes
    maxPostsPerScan: 500,
    dataRetentionDays: 90,
    enableRealTimeMonitoring: true,
    enableScheduledReports: true,
  });

  // Platform Integration
  const [platformSettings, setPlatformSettings] = useState({
    enableFacebook: true,
    enableTwitter: true,
    enableInstagram: true,
    enableTikTok: false,
    enableYouTube: true,
    enableWhatsApp: false,
    enableNewsWebsites: true,
  });

  // Security Settings
  const [securitySettings, setSecuritySettings] = useState({
    sessionTimeout: 480, // minutes (8 hours)
    maxLoginAttempts: 5,
    enableTwoFactor: false,
    enableAuditLog: true,
    apiRateLimit: 1000, // requests per hour
  });

  const handleSaveSettings = () => {
    // TODO: Implement API call to save settings
    console.log('Saving platform settings:', {
      alertSettings,
      emailSettings,
      monitoringSettings,
      platformSettings,
      securitySettings,
    });

    toast({
      title: "Settings Saved",
      description: "Platform settings have been updated successfully.",
    });
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Platform Settings</h1>
          <p className="text-gray-600 mt-2">
            Configure alert thresholds, notifications, and system parameters for the Magwero platform
          </p>
        </div>
        <Button onClick={handleSaveSettings} className="flex items-center gap-2">
          <Save className="w-4 h-4" />
          Save All Settings
        </Button>
      </div>

      {/* Alert Thresholds */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5" />
            Alert Thresholds
          </CardTitle>
          <CardDescription>
            Configure risk levels and alert triggers for media monitoring
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>High Risk Threshold (%)</Label>
              <Input
                type="number"
                value={alertSettings.highRiskThreshold}
                onChange={(e) => setAlertSettings({...alertSettings, highRiskThreshold: parseInt(e.target.value)})}
                min="0"
                max="100"
              />
              <Badge variant="destructive" className="text-xs">Critical</Badge>
            </div>
            <div className="space-y-2">
              <Label>Medium Risk Threshold (%)</Label>
              <Input
                type="number"
                value={alertSettings.mediumRiskThreshold}
                onChange={(e) => setAlertSettings({...alertSettings, mediumRiskThreshold: parseInt(e.target.value)})}
                min="0"
                max="100"
              />
              <Badge variant="secondary" className="text-xs">Warning</Badge>
            </div>
            <div className="space-y-2">
              <Label>Low Risk Threshold (%)</Label>
              <Input
                type="number"
                value={alertSettings.lowRiskThreshold}
                onChange={(e) => setAlertSettings({...alertSettings, lowRiskThreshold: parseInt(e.target.value)})}
                min="0"
                max="100"
              />
              <Badge variant="outline" className="text-xs">Info</Badge>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Sentiment Threshold (Negative)</Label>
              <Input
                type="number"
                step="0.1"
                value={alertSettings.sentimentThreshold}
                onChange={(e) => setAlertSettings({...alertSettings, sentimentThreshold: parseFloat(e.target.value)})}
                min="-1"
                max="1"
              />
            </div>
            <div className="space-y-2">
              <Label>Virality Threshold (Shares)</Label>
              <Input
                type="number"
                value={alertSettings.viralityThreshold}
                onChange={(e) => setAlertSettings({...alertSettings, viralityThreshold: parseInt(e.target.value)})}
                min="0"
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              checked={alertSettings.enableAlerts}
              onCheckedChange={(checked) => setAlertSettings({...alertSettings, enableAlerts: checked})}
            />
            <Label>Enable Alert Notifications</Label>
          </div>
        </CardContent>
      </Card>

      {/* Email Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="w-5 h-5" />
            Email Configuration
          </CardTitle>
          <CardDescription>
            Configure email addresses and SMTP settings for notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Alert Email Recipients</Label>
            <Textarea
              placeholder="Enter email addresses separated by commas"
              value={emailSettings.alertEmails}
              onChange={(e) => setEmailSettings({...emailSettings, alertEmails: e.target.value})}
              rows={3}
            />
            <p className="text-sm text-gray-500">
              These emails will receive immediate alerts for high-risk content
            </p>
          </div>

          <div className="space-y-2">
            <Label>Report Email Recipients</Label>
            <Textarea
              placeholder="Enter email addresses separated by commas"
              value={emailSettings.reportEmails}
              onChange={(e) => setEmailSettings({...emailSettings, reportEmails: e.target.value})}
              rows={2}
            />
            <p className="text-sm text-gray-500">
              These emails will receive daily/weekly reports
            </p>
          </div>

          <Separator />

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>SMTP Server</Label>
              <Input
                value={emailSettings.smtpServer}
                onChange={(e) => setEmailSettings({...emailSettings, smtpServer: e.target.value})}
                placeholder="smtp.your-server.com"
              />
            </div>
            <div className="space-y-2">
              <Label>SMTP Port</Label>
              <Input
                value={emailSettings.smtpPort}
                onChange={(e) => setEmailSettings({...emailSettings, smtpPort: e.target.value})}
                placeholder="587"
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Switch
                checked={emailSettings.enableEmailAlerts}
                onCheckedChange={(checked) => setEmailSettings({...emailSettings, enableEmailAlerts: checked})}
              />
              <Label>Enable Email Alerts</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                checked={emailSettings.enableDailyReports}
                onCheckedChange={(checked) => setEmailSettings({...emailSettings, enableDailyReports: checked})}
              />
              <Label>Enable Daily Reports</Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Monitoring Parameters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            Monitoring Parameters
          </CardTitle>
          <CardDescription>
            Configure data collection intervals and system performance settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>Scan Interval (minutes)</Label>
              <Input
                type="number"
                value={monitoringSettings.scanInterval}
                onChange={(e) => setMonitoringSettings({...monitoringSettings, scanInterval: parseInt(e.target.value)})}
                min="1"
                max="1440"
              />
            </div>
            <div className="space-y-2">
              <Label>Keyword Refresh (minutes)</Label>
              <Input
                type="number"
                value={monitoringSettings.keywordRefreshRate}
                onChange={(e) => setMonitoringSettings({...monitoringSettings, keywordRefreshRate: parseInt(e.target.value)})}
                min="5"
                max="1440"
              />
            </div>
            <div className="space-y-2">
              <Label>Max Posts per Scan</Label>
              <Input
                type="number"
                value={monitoringSettings.maxPostsPerScan}
                onChange={(e) => setMonitoringSettings({...monitoringSettings, maxPostsPerScan: parseInt(e.target.value)})}
                min="100"
                max="5000"
              />
            </div>
            <div className="space-y-2">
              <Label>Data Retention (days)</Label>
              <Input
                type="number"
                value={monitoringSettings.dataRetentionDays}
                onChange={(e) => setMonitoringSettings({...monitoringSettings, dataRetentionDays: parseInt(e.target.value)})}
                min="7"
                max="365"
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Switch
                checked={monitoringSettings.enableRealTimeMonitoring}
                onCheckedChange={(checked) => setMonitoringSettings({...monitoringSettings, enableRealTimeMonitoring: checked})}
              />
              <Label>Real-time Monitoring</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                checked={monitoringSettings.enableScheduledReports}
                onCheckedChange={(checked) => setMonitoringSettings({...monitoringSettings, enableScheduledReports: checked})}
              />
              <Label>Scheduled Reports</Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Platform Integration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="w-5 h-5" />
            Platform Integration
          </CardTitle>
          <CardDescription>
            Enable or disable monitoring for specific social media platforms
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(platformSettings).map(([platform, enabled]) => (
              <div key={platform} className="flex items-center justify-between p-3 border rounded-lg">
                <Label className="capitalize">
                  {platform.replace('enable', '').replace(/([A-Z])/g, ' $1').trim()}
                </Label>
                <Switch
                  checked={enabled}
                  onCheckedChange={(checked) => setPlatformSettings({...platformSettings, [platform]: checked})}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Security Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Security Settings
          </CardTitle>
          <CardDescription>
            Configure security parameters and access controls
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Session Timeout (minutes)</Label>
              <Input
                type="number"
                value={securitySettings.sessionTimeout}
                onChange={(e) => setSecuritySettings({...securitySettings, sessionTimeout: parseInt(e.target.value)})}
                min="30"
                max="1440"
              />
            </div>
            <div className="space-y-2">
              <Label>Max Login Attempts</Label>
              <Input
                type="number"
                value={securitySettings.maxLoginAttempts}
                onChange={(e) => setSecuritySettings({...securitySettings, maxLoginAttempts: parseInt(e.target.value)})}
                min="3"
                max="10"
              />
            </div>
            <div className="space-y-2">
              <Label>API Rate Limit (req/hour)</Label>
              <Input
                type="number"
                value={securitySettings.apiRateLimit}
                onChange={(e) => setSecuritySettings({...securitySettings, apiRateLimit: parseInt(e.target.value)})}
                min="100"
                max="10000"
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Switch
                checked={securitySettings.enableTwoFactor}
                onCheckedChange={(checked) => setSecuritySettings({...securitySettings, enableTwoFactor: checked})}
              />
              <Label>Two-Factor Authentication</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                checked={securitySettings.enableAuditLog}
                onCheckedChange={(checked) => setSecuritySettings({...securitySettings, enableAuditLog: checked})}
              />
              <Label>Audit Logging</Label>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PlatformSettings;
