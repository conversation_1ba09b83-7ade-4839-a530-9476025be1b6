import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from "recharts";
import { 
  AlertTriangle, 
  TrendingUp, 
  Users, 
  Shield,
  Bell,
  Monitor,
  BarChart3,
  Settings,
  FileText,
  UserCheck,
  Vote as VoteIcon,
  AlertOctagon,
  ShieldCheck
} from "lucide-react";
import { Navigation } from "@/components/Navigation";
import { AlertCard } from "@/components/AlertCard";
import { SocialMediaFeed } from "@/components/SocialMediaFeed";
import { ElectionAnalytics } from "@/components/ElectionAnalytics";
import { EarlyWarningSystem } from "@/components/EarlyWarningSystem";
import { PreventiveActions } from "@/components/PreventiveActions";
import Reports from "./Reports";
import SystemUsers from "./SystemUsers";

interface DashboardProps {
  onLogout: () => void;
}

const dashboardData = [
  { name: "<PERSON><PERSON>we", posts: 8200 },
  { name: "Blantyre", posts: 6300 },
  { name: "Mzuzu", posts: 3400 },
  { name: "Zomba", posts: 2800 },
  { name: "Kasungu", posts: 2100 },
  { name: "Mangochi", posts: 1800 }
];

const sentimentData = [
  { day: "Mon", positive: 75, negative: 15 },
  { day: "Tue", positive: 78, negative: 18 },
  { day: "Wed", positive: 72, negative: 20 },
  { day: "Thu", positive: 80, negative: 16 },
  { day: "Fri", positive: 82, negative: 14 },
  { day: "Sat", positive: 79, negative: 17 },
  { day: "Sun", positive: 76, negative: 19 }
];

const platformData = [
  { name: "Facebook", value: 45, color: "#1877F2" },
  { name: "Twitter/X", value: 30, color: "#1DA1F2" },
  { name: "TikTok", value: 25, color: "#FF0050" }
];

const Dashboard = ({ onLogout }: DashboardProps) => {
  const [activeTab, setActiveTab] = useState("dashboard");

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation activeTab={activeTab} setActiveTab={setActiveTab} onLogout={onLogout} />
      
      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 bg-white shadow-sm min-h-screen">
          <div className="p-6">
            <div className="space-y-2">
              <button 
                onClick={() => setActiveTab("dashboard")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "dashboard" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <BarChart3 className="w-5 h-5" />
                <span>Dashboard</span>
              </button>
              <button 
                onClick={() => setActiveTab("monitoring")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "monitoring" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <Monitor className="w-5 h-5" />
                <span>Live Monitoring</span>
              </button>
              <button 
                onClick={() => setActiveTab("analytics")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "analytics" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <TrendingUp className="w-5 h-5" />
                <span>Analytics</span>
              </button>
              <button 
                onClick={() => setActiveTab("election_analytics")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "election_analytics" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <VoteIcon className="w-5 h-5" />
                <span>Election Analytics</span>
              </button>
              <button 
                onClick={() => setActiveTab("early_warning")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "early_warning" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <AlertOctagon className="w-5 h-5" />
                <span>Early Warning System</span>
              </button>
              <button 
                onClick={() => setActiveTab("preventive_actions")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "preventive_actions" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <ShieldCheck className="w-5 h-5" />
                <span>Preventive Actions</span>
              </button>
              <button 
                onClick={() => setActiveTab("alerts")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "alerts" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <Bell className="w-5 h-5" />
                <span>Alerts</span>
              </button>
              <button 
                onClick={() => setActiveTab("reports")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "reports" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <FileText className="w-5 h-5" />
                <span>Reports</span>
              </button>
              <button 
                onClick={() => setActiveTab("users")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "users" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <UserCheck className="w-5 h-5" />
                <span>System Users</span>
              </button>
              <button 
                onClick={() => setActiveTab("settings")}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === "settings" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
              >
                <Settings className="w-5 h-5" />
                <span>Settings</span>
              </button>
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {activeTab === "dashboard" && (
            <div className="space-y-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Dashboard Overview</h1>
                <p className="text-gray-600 mt-2">Real-time insights from social media monitoring</p>
              </div>

              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card className="border-l-4 border-l-blue-500">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Posts Monitored</p>
                        <p className="text-3xl font-bold text-blue-600">24,669</p>
                      </div>
                      <TrendingUp className="w-8 h-8 text-blue-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-l-4 border-l-green-500">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">System Uptime</p>
                        <p className="text-3xl font-bold text-green-600">99.2%</p>
                      </div>
                      <Shield className="w-8 h-8 text-green-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-l-4 border-l-orange-500">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Active Alerts</p>
                        <p className="text-3xl font-bold text-orange-600">47</p>
                      </div>
                      <AlertTriangle className="w-8 h-8 text-orange-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-l-4 border-l-red-500">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Flagged Content</p>
                        <p className="text-3xl font-bold text-red-600">1,338</p>
                      </div>
                      <AlertTriangle className="w-8 h-8 text-red-500" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Charts Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Geographic Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle>Geographic Distribution of Content</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={dashboardData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="posts" fill="#3b82f6" />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Platform Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle>Platform Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={platformData}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={120}
                          paddingAngle={5}
                          dataKey="value"
                        >
                          {platformData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Sentiment Analysis */}
                <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle>Real-time Sentiment Analysis</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={sentimentData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="day" />
                        <YAxis />
                        <Tooltip />
                        <Line type="monotone" dataKey="positive" stroke="#10b981" strokeWidth={3} />
                        <Line type="monotone" dataKey="negative" stroke="#ef4444" strokeWidth={3} />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>

              {/* Key Metrics Table */}
              <Card>
                <CardHeader>
                  <CardTitle>Key Metrics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-900">89.3%</p>
                      <p className="text-sm text-gray-600">Hate Speech Detection Rate</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-900">8</p>
                      <p className="text-sm text-gray-600">Political Candidates Tracked</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-900">11</p>
                      <p className="text-sm text-gray-600">Political Organizations</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-900">&lt; 5 min</p>
                      <p className="text-sm text-gray-600">Response Time (Alerts)</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === "monitoring" && <SocialMediaFeed />}
          {activeTab === "alerts" && <AlertCard />}
          {activeTab === "reports" && <Reports />}
          {activeTab === "users" && <SystemUsers />}
          {activeTab === "election_analytics" && <ElectionAnalytics />}
          {activeTab === "early_warning" && <EarlyWarningSystem />}
          {activeTab === "preventive_actions" && <PreventiveActions />}
          
          {activeTab === "analytics" && (
            <div className="space-y-6">
              <h1 className="text-3xl font-bold text-gray-900">Analytics & Insights</h1>
              {/* Key Stats */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-green-600">89.3%</div>
                    <div className="text-sm text-gray-600">Detection Accuracy</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-blue-600">342</div>
                    <div className="text-sm text-gray-600">Trending Topics</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-orange-600">15.2%</div>
                    <div className="text-sm text-gray-600">Hate Speech Rate</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-blue-600">8</div>
                    <div className="text-sm text-gray-600">Political Candidates Tracked</div>
                  </CardContent>
                </Card>
              </div>
              
              {/* Charts */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Top Trending Hashtags</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-blue-600 font-semibold">#MalawiElections2025</span>
                        <span className="text-gray-600">2,456</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-blue-600 font-semibold">#VoterRegistration</span>
                        <span className="text-gray-600">1,834</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-blue-600 font-semibold">#Democracy</span>
                        <span className="text-gray-600">1,245</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-blue-600 font-semibold">#Change2025</span>
                        <span className="text-gray-600">987</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-blue-600 font-semibold">#VoterEducation</span>
                        <span className="text-gray-600">743</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Threat Level Analysis</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={200}>
                      <PieChart>
                        <Pie
                          data={[
                            { name: "Low", value: 45, color: "#10b981" },
                            { name: "Medium", value: 30, color: "#f59e0b" },
                            { name: "High", value: 20, color: "#ef4444" },
                            { name: "Critical", value: 5, color: "#7c2d12" }
                          ]}
                          cx="50%"
                          cy="50%"
                          innerRadius={40}
                          outerRadius={80}
                          paddingAngle={5}
                          dataKey="value"
                        >
                          {[
                            { color: "#10b981" },
                            { color: "#f59e0b" },
                            { color: "#ef4444" },
                            { color: "#7c2d12" }
                          ].map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {activeTab === "settings" && <Settings />}
          {(activeTab === "reports" || activeTab === "users") && (
            <div className="text-center py-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                {activeTab === "reports" && "Reports & Export"}
                {activeTab === "users" && "System Users"}
              </h2>
              <p className="text-gray-600">This section is under development</p>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default Dashboard;