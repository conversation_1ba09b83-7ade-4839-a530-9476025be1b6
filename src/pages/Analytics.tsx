import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from "recharts";
import { TeamReportView } from "../components/TeamReportView";
import { TeamReportFiltersComponent } from "../components/TeamReportFilters";
import { RCOOverview } from "../components/RCOOverview";
import { useTeamReports } from "../hooks/useTeamReports";
import { TeamName, TeamReportFilters } from "../types/teamReportsTypes";

const Analytics = () => {
  const [activeTeam, setActiveTeam] = useState<TeamName>('rco');
  
  // Separate filter states for each team
  const [teamFilters, setTeamFilters] = useState<Record<TeamName, TeamReportFilters>>({
    rco: {
      days: 30,
      district: 'all',
      severity: 'all'
    },
    peacebuilding: {
      days: 30,
      district: 'all',
      severity: 'all'
    },
    mesp: {
      days: 30,
      district: 'all',
      severity: 'all',
      incidentType: 'all'
    },    communications: {
      days: 30,
      district: 'all',
      severity: 'all',
      platform: 'all',
      sentiment: 'all'
    }
  });
  const { data, loading, error } = useTeamReports(activeTeam, teamFilters[activeTeam]);

  // Debug logging
  useEffect(() => {
    console.log('Active team changed:', activeTeam);
    console.log('Current filters for team:', teamFilters[activeTeam]);
  }, [activeTeam, teamFilters]);

  const handleTabChange = (value: string) => {
    if (value !== 'overview') {
      setActiveTeam(value as TeamName);
    }
  };
  const handleFiltersChange = (newFilters: TeamReportFilters) => {
    console.log('Filters changing for team:', activeTeam, newFilters); // Debug log
    setTeamFilters(prev => ({
      ...prev,
      [activeTeam]: newFilters
    }));
  };

  return (
    <div className="p-6 space-y-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Data Analytics</h1>
        <p className="text-gray-600 mt-2">
          Comprehensive analysis of social media data and election monitoring insights
        </p>
      </div>

      <Tabs defaultValue="overview" onValueChange={handleTabChange} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="rco">RCO</TabsTrigger>
          <TabsTrigger value="peacebuilding">Peace Building</TabsTrigger>
          <TabsTrigger value="mesp">MESP</TabsTrigger>
          <TabsTrigger value="communications">Communications</TabsTrigger>
        </TabsList>        <TabsContent value="overview" className="space-y-6">
          <RCOOverview />
        </TabsContent>{/* Team Report Tabs */}
        {['rco', 'peacebuilding', 'mesp', 'communications'].map((team) => (
          <TabsContent key={team} value={team} className="space-y-6">
            <TeamReportFiltersComponent
              filters={teamFilters[team as TeamName]}
              onFiltersChange={handleFiltersChange}
              team={team as TeamName}
            />
            <TeamReportView
              data={activeTeam === team ? data : null}
              loading={activeTeam === team ? loading : false}
              error={activeTeam === team ? error : null}
            />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default Analytics;
