import { Outlet } from "react-router-dom";
import { NavigationRouter } from "@/components/NavigationRouter";
import { SidebarNavigation } from "@/components/SidebarNavigation";

interface DashboardProps {
  onLogout: () => void;
}

const Dashboard = ({ onLogout }: DashboardProps) => {
  return (
    <div className="h-screen bg-gray-50 flex flex-col overflow-hidden">
      {/* Fixed Header */}
      <div className="flex-shrink-0">
        <NavigationRouter onLogout={onLogout} />
      </div>
      
      <div className="flex flex-1 overflow-hidden">
        {/* Fixed Sidebar */}
        <aside className="w-64 bg-white shadow-sm flex-shrink-0 overflow-y-auto">
          <div className="p-6">
            <SidebarNavigation />
          </div>
        </aside>

        {/* Scrollable Main Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="p-6">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};

export default Dashboard;
