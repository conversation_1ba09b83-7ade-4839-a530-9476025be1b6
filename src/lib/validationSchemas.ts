import { z } from 'zod';
import { UserRole } from '@/types/userTypes';

// Password validation schema
const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters long')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/\d/, 'Password must contain at least one number');

// Email validation schema
const emailSchema = z
  .string()
  .email('Please enter a valid email address')
  .max(255, 'Email must not exceed 255 characters');

// Full name validation schema
const fullNameSchema = z
  .string()
  .min(2, 'Full name must be at least 2 characters long')
  .max(255, 'Full name must not exceed 255 characters')
  .regex(/^[a-zA-Z\s\-']+$/, 'Full name can only contain letters, spaces, hyphens, and apostrophes');

// Role validation schema
const roleSchema = z.enum(['admin', 'moderator', 'viewer'] as const);

// Login form validation schema
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
});

// Create user form validation schema
export const createUserSchema = z.object({
  full_name: fullNameSchema,
  email: emailSchema,
  password: passwordSchema,
  role: roleSchema,
});

// Update user form validation schema
export const updateUserSchema = z.object({
  full_name: fullNameSchema.optional(),
  email: emailSchema.optional(),
  role: roleSchema.optional(),
  is_active: z.boolean().optional(),
});

// Change password form validation schema
export const changePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: passwordSchema,
    confirmPassword: z.string().min(1, 'Please confirm your new password'),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

// Admin change password schema
export const adminChangePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, 'Your current password is required'),
    newPassword: passwordSchema,
    confirmPassword: z.string().min(1, 'Please confirm the new password'),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

// User filters validation schema
export const userFiltersSchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  role: roleSchema.optional(),
  search: z.string().optional(),
  is_active: z.boolean().optional(),
});

// Profile update schema (for current user)
export const profileUpdateSchema = z.object({
  full_name: fullNameSchema,
  email: emailSchema,
});

// Test endpoint schema
export const testSchema = z.object({
  any: z.any(),
});

// Type exports for form data
export type LoginFormData = z.infer<typeof loginSchema>;
export type CreateUserFormData = z.infer<typeof createUserSchema>;
export type UpdateUserFormData = z.infer<typeof updateUserSchema>;
export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;
export type AdminChangePasswordFormData = z.infer<typeof adminChangePasswordSchema>;
export type UserFiltersFormData = z.infer<typeof userFiltersSchema>;
export type ProfileUpdateFormData = z.infer<typeof profileUpdateSchema>;
export type TestFormData = z.infer<typeof testSchema>;

// Validation helper functions
export const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
  try {
    passwordSchema.parse(password);
    return { isValid: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map((err) => err.message),
      };
    }
    return { isValid: false, errors: ['Invalid password'] };
  }
};

export const validateEmail = (email: string): { isValid: boolean; errors: string[] } => {
  try {
    emailSchema.parse(email);
    return { isValid: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map((err) => err.message),
      };
    }
    return { isValid: false, errors: ['Invalid email'] };
  }
};

export const validateFullName = (name: string): { isValid: boolean; errors: string[] } => {
  try {
    fullNameSchema.parse(name);
    return { isValid: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map((err) => err.message),
      };
    }
    return { isValid: false, errors: ['Invalid full name'] };
  }
};

// Password strength checker
export const getPasswordStrength = (password: string): {
  score: number;
  label: string;
  suggestions: string[];
} => {
  let score = 0;
  const suggestions: string[] = [];

  if (password.length >= 8) score += 1;
  else suggestions.push('Use at least 8 characters');

  if (/[a-z]/.test(password)) score += 1;
  else suggestions.push('Include lowercase letters');

  if (/[A-Z]/.test(password)) score += 1;
  else suggestions.push('Include uppercase letters');

  if (/\d/.test(password)) score += 1;
  else suggestions.push('Include numbers');

  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;
  else suggestions.push('Consider adding special characters');

  if (password.length >= 12) score += 1;

  const labels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong', 'Very Strong'];
  const label = labels[Math.min(score, labels.length - 1)];

  return { score, label, suggestions };
};

// Role permission helpers
export const getRolePermissions = (role: UserRole): string[] => {
  const permissions: Record<UserRole, string[]> = {
    admin: [
      'manage_users',
      'view_users',
      'create_users',
      'edit_users',
      'delete_users',
      'change_user_passwords',
      'view_user_stats',
      'manage_system',
      'view_all_reports',
      'moderate_content',
    ],
    moderator: [
      'view_users',
      'view_all_reports',
      'moderate_content',
    ],
    viewer: [
      'view_reports',
    ],
  };

  return permissions[role] || [];
};

export const hasPermission = (userRole: UserRole, permission: string): boolean => {
  const permissions = getRolePermissions(userRole);
  return permissions.includes(permission);
};
