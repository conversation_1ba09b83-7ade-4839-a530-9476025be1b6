export interface TeamReportFilters {
  days: number;
  district: string;
  severity: string;
  incidentType?: string; // For MESP
  platform?: string;
  perpetrator?: string;
  sentiment?: string; // For Communications
}

export interface TeamOverview {
  total_incidents: number;
  avg_severity: string;
  districts_affected: number;
  platforms_monitored: number;
}

export interface KeyMetrics {
  undp_mentions: any[];
  international_incidents: any[];
  donor_concerns: any[];
}

// MESP-specific key metrics
export interface MESPKeyMetrics {
  electoral_misconduct: Array<{
    primary_category: string;
    count: number;
  }>;
  governance_issues: Array<{
    keyword: string;
    count: number;
  }>;
  political_entities: Array<{
    entity: string;
    count: number;
  }>;
}

// Peace Building specific key metrics
export interface PeaceBuildingKeyMetrics {
  violence_incidents: Array<{
    primary_category: string;
    count: number;
  }>;
  conflict_keywords: Array<{
    keyword: string;
    count: number;
  }>;
  youth_involvement: Array<{
    keyword: string;
    count: number;
  }>;
  hotspot_analysis: Array<{
    id: number;
    post_summary: string;
    primary_category: string;
    severity_level: string;
    district: string;
    created_at: string;
    source_url: string;
  }>;
}

// Peace Building specific data structure
export interface PeaceBuildingOverview {
  total_incidents: number;
  high_severity_count: number;
  districts_affected: number;
  platforms_monitored: number;
  period_analyzed: string;
}

export interface PeaceBuildingIncident {
  id: number;
  post_summary: string;
  primary_category: string;
  severity_level: string;
  district: string;
  created_at: string;
  source_url: string;
}

export interface PeaceBuildingHotspot {
  district: string;
  incident_count: number;
  severity_distribution: {
    Critical: number;
    High: number;
    Medium: number;
    Low: number;
  };
  recent_incidents: PeaceBuildingIncident[];
}

export interface PeaceBuildingRecommendation {
  priority: 'High' | 'Medium' | 'Low';
  action: string;
  rationale: string;
  target_areas: string[];
}

// Communications-specific key metrics
export interface CommunicationsKeyMetrics {
  media_restrictions: Array<{
    restriction_type: string;
    count: number;
  }>;
  misinformation_indicators: Array<{
    indicator: string;
    count: number;
  }>;
  media_entities: Array<{
    entity: string;
    count: number;
  }>;
}

export interface TopEntity {
  entity: string;
  count: number;
}

export interface TopKeyword {
  keyword: string;
  count: number;
}

export interface HighSeverityIncident {
  id: number;
  post_summary: string;
  primary_category: string;
  severity_level: string;
  district: string;
  created_at: string;
  source_url: string;
}

export interface TrendDataPoint {
  incident_date: string;
  daily_count: number;
  avg_severity: string;
}

export interface EarlyWarning {
  recent_high_severity: HighSeverityIncident[];
  trend_data: TrendDataPoint[];
  timestamp: string;
}

export interface CategoryBreakdown {
  primary_category: string;
  count: number;
}

export interface DistrictBreakdown {
  district: string;
  count: number;
}

export interface ElectoralMisconductBreakdown {
  electoral_misconduct: string;
  count: number;
}

export interface ElectionAnalytics {
  category_breakdown: CategoryBreakdown[];
  district_breakdown: DistrictBreakdown[];
  electoral_misconduct: ElectoralMisconductBreakdown[];
  filters: TeamReportFilters & { focus: string };
}

export interface Recommendation {
  priority: string;
  action: string;
  rationale: string;
}

export interface TeamReportData {
  team: string;
  period: string;
  overview?: TeamOverview | PeaceBuildingOverview; // Optional for different team types
  key_metrics: KeyMetrics | MESPKeyMetrics | PeaceBuildingKeyMetrics | CommunicationsKeyMetrics;
  top_entities?: TopEntity[];
  top_keywords?: TopKeyword[];
  early_warning: EarlyWarning;
  election_analytics?: ElectionAnalytics; // Optional for some teams
  recommendations: Recommendation[] | PeaceBuildingRecommendation[];
  recent_incidents?: HighSeverityIncident[] | PeaceBuildingIncident[]; // For Peace Building team
  hotspots?: HighSeverityIncident[] | PeaceBuildingHotspot[]; // For Peace Building team
}

export interface TeamReportResponse {
  success: boolean;
  data: TeamReportData;
  message: string;
}

// Filter option types
export interface FilterOption {
  value: string;
  label: string;
  category?: string;
  description?: string;
  color?: string;
}

export interface TeamFilterOptions {
  days: FilterOption[];
  district: FilterOption[];
  severity: FilterOption[];
  incidentType?: FilterOption[];
  platform?: FilterOption[];
  perpetrator?: FilterOption[];
  sentiment?: FilterOption[];
}

// MESP-specific filter options
export const MESP_FILTER_OPTIONS: TeamFilterOptions = {
  days: [
    { value: "7", label: "Last 7 days" },
    { value: "14", label: "Last 2 weeks" },
    { value: "30", label: "Last 30 days" },
    { value: "60", label: "Last 60 days" },
    { value: "90", label: "Last 90 days" },
    { value: "180", label: "Last 6 months" },
    { value: "365", label: "Last year" }
  ],
  district: [
    { value: "all", label: "All Districts" },
    { value: "National", label: "National Level" },
    { value: "Balaka", label: "Balaka" },
    { value: "Blantyre", label: "Blantyre" },
    { value: "Chikwawa", label: "Chikwawa" },
    { value: "Chiradzulu", label: "Chiradzulu" },
    { value: "Chitipa", label: "Chitipa" },
    { value: "Dedza", label: "Dedza" },
    { value: "Dowa", label: "Dowa" },
    { value: "Karonga", label: "Karonga" },
    { value: "Kasungu", label: "Kasungu" },
    { value: "Likoma", label: "Likoma" },
    { value: "Lilongwe", label: "Lilongwe" },
    { value: "Machinga", label: "Machinga" },
    { value: "Mangochi", label: "Mangochi" },
    { value: "Mchinji", label: "Mchinji" },
    { value: "Mulanje", label: "Mulanje" },
    { value: "Mwanza", label: "Mwanza" },
    { value: "Mzimba", label: "Mzimba" },
    { value: "Mzuzu", label: "Mzuzu" },
    { value: "Neno", label: "Neno" },
    { value: "Nkhata Bay", label: "Nkhata Bay" },
    { value: "Nkhotakota", label: "Nkhotakota" },
    { value: "Nsanje", label: "Nsanje" },
    { value: "Ntcheu", label: "Ntcheu" },
    { value: "Ntchisi", label: "Ntchisi" },
    { value: "Phalombe", label: "Phalombe" },
    { value: "Rumphi", label: "Rumphi" },
    { value: "Salima", label: "Salima" },
    { value: "Thyolo", label: "Thyolo" },
    { value: "Zomba", label: "Zomba" }
  ],
  severity: [
    { value: "all", label: "All Severity Levels" },
    { value: "Critical", label: "Critical", color: "#dc2626", description: "Immediate threat to electoral process or life" },
    { value: "High", label: "High", color: "#ea580c", description: "Significant impact on electoral integrity" },
    { value: "Medium", label: "Medium", color: "#ca8a04", description: "Moderate concern for electoral process" },
    { value: "Low", label: "Low", color: "#16a34a", description: "Minor impact on electoral environment" }
  ],
  incidentType: [
    { value: "all", label: "All Incident Types" },
    { value: "Political/electoral misconduct", label: "Electoral Misconduct", category: "MESP Priority" },
    { value: "Restrictions on civil and political rights", label: "Civil & Political Rights Restrictions", category: "MESP Priority" },
    { value: "Discrimination against disadvantaged groups", label: "Discrimination Against Groups", category: "Governance" },
    { value: "Law enforcement misconduct (Violation of right to liberty and security of persons)", label: "Law Enforcement Misconduct", category: "Security" },
    { value: "Violation of right to physical integrity (violent attacks)", label: "Physical Violence & Attacks", category: "Security" },
    { value: "Politically motivated attacks/harassment/intimidation/incitement", label: "Political Intimidation & Harassment", category: "Political Violence" },
    { value: "Protests/Demonstrations", label: "Protests & Demonstrations", category: "Civil Action" }
  ],
  platform: [
    { value: "all", label: "All Platforms" },
    { value: "Facebook", label: "Facebook" },
    { value: "Twitter", label: "Twitter/X" },
    { value: "WhatsApp", label: "WhatsApp" },
    { value: "Instagram", label: "Instagram" },
    { value: "TikTok", label: "TikTok" },
    { value: "Website", label: "News Websites" },
    { value: "Other", label: "Other Sources" }
  ],
  perpetrator: [
    { value: "all", label: "All Perpetrators" },
    { value: "Democratic Progressive Party (DPP)", label: "DPP", category: "Political Parties" },
    { value: "Malawi Congress Party (MCP)", label: "MCP", category: "Political Parties" },
    { value: "United Transformation Movement (UTM)", label: "UTM", category: "Political Parties" },
    { value: "United Democratic Front (UDF)", label: "UDF", category: "Political Parties" },
    { value: "Government officials", label: "Government Officials", category: "State Actors" },
    { value: "Malawi Electoral Commission (MEC)", label: "MEC", category: "Electoral Bodies" },
    { value: "Malawi Police Service", label: "Police", category: "Security Forces" },
    { value: "Civil society/NGOs", label: "Civil Society/NGOs", category: "Non-State" }
  ]
};

// Peace Building-specific filter options
export const PEACE_BUILDING_FILTER_OPTIONS: TeamFilterOptions = {
  days: [
    { value: "7", label: "Last 7 days" },
    { value: "14", label: "Last 2 weeks" },
    { value: "30", label: "Last 30 days" },
    { value: "60", label: "Last 60 days" },
    { value: "90", label: "Last 90 days" },
    { value: "180", label: "Last 6 months" },
    { value: "365", label: "Last year" }
  ],
  district: [
    { value: "all", label: "All Districts" },
    { value: "National", label: "National Level" },
    { value: "Balaka", label: "Balaka" },
    { value: "Blantyre", label: "Blantyre" },
    { value: "Chikwawa", label: "Chikwawa" },
    { value: "Chiradzulu", label: "Chiradzulu" },
    { value: "Chitipa", label: "Chitipa" },
    { value: "Dedza", label: "Dedza" },
    { value: "Dowa", label: "Dowa" },
    { value: "Karonga", label: "Karonga" },
    { value: "Kasungu", label: "Kasungu" },
    { value: "Likoma", label: "Likoma" },
    { value: "Lilongwe", label: "Lilongwe" },
    { value: "Machinga", label: "Machinga" },
    { value: "Mangochi", label: "Mangochi" },
    { value: "Mchinji", label: "Mchinji" },
    { value: "Mulanje", label: "Mulanje" },
    { value: "Mwanza", label: "Mwanza" },
    { value: "Mzimba", label: "Mzimba" },
    { value: "Mzuzu", label: "Mzuzu" },
    { value: "Neno", label: "Neno" },
    { value: "Nkhata Bay", label: "Nkhata Bay" },
    { value: "Nkhotakota", label: "Nkhotakota" },
    { value: "Nsanje", label: "Nsanje" },
    { value: "Ntcheu", label: "Ntcheu" },
    { value: "Ntchisi", label: "Ntchisi" },
    { value: "Phalombe", label: "Phalombe" },
    { value: "Rumphi", label: "Rumphi" },
    { value: "Salima", label: "Salima" },
    { value: "Thyolo", label: "Thyolo" },
    { value: "Zomba", label: "Zomba" }
  ],
  severity: [
    { value: "all", label: "All Severity Levels" },
    { value: "Critical", label: "Critical", color: "#dc2626", description: "Immediate threat to peace and security" },
    { value: "High", label: "High", color: "#ea580c", description: "Significant threat to community stability" },
    { value: "Medium", label: "Medium", color: "#ca8a04", description: "Moderate concern for peace building" },
    { value: "Low", label: "Low", color: "#16a34a", description: "Minor impact on peace environment" }
  ]
};

// Communications-specific filter options
export const COMMUNICATIONS_FILTER_OPTIONS: TeamFilterOptions = {
  days: [
    { value: "7", label: "Last 7 days" },
    { value: "14", label: "Last 2 weeks" },
    { value: "30", label: "Last 30 days" },
    { value: "60", label: "Last 60 days" },
    { value: "90", label: "Last 90 days" },
    { value: "180", label: "Last 6 months" },
    { value: "365", label: "Last year" }
  ],
  district: [
    { value: "all", label: "All Districts" },
    { value: "National", label: "National Level" },
    { value: "Balaka", label: "Balaka" },
    { value: "Blantyre", label: "Blantyre" },
    { value: "Chikwawa", label: "Chikwawa" },
    { value: "Chiradzulu", label: "Chiradzulu" },
    { value: "Chitipa", label: "Chitipa" },
    { value: "Dedza", label: "Dedza" },
    { value: "Dowa", label: "Dowa" },
    { value: "Karonga", label: "Karonga" },
    { value: "Kasungu", label: "Kasungu" },
    { value: "Likoma", label: "Likoma" },
    { value: "Lilongwe", label: "Lilongwe" },
    { value: "Machinga", label: "Machinga" },
    { value: "Mangochi", label: "Mangochi" },
    { value: "Mchinji", label: "Mchinji" },
    { value: "Mulanje", label: "Mulanje" },
    { value: "Mwanza", label: "Mwanza" },
    { value: "Mzimba", label: "Mzimba" },
    { value: "Mzuzu", label: "Mzuzu" },
    { value: "Neno", label: "Neno" },
    { value: "Nkhata Bay", label: "Nkhata Bay" },
    { value: "Nkhotakota", label: "Nkhotakota" },
    { value: "Nsanje", label: "Nsanje" },
    { value: "Ntcheu", label: "Ntcheu" },
    { value: "Ntchisi", label: "Ntchisi" },
    { value: "Phalombe", label: "Phalombe" },
    { value: "Rumphi", label: "Rumphi" },
    { value: "Salima", label: "Salima" },
    { value: "Thyolo", label: "Thyolo" },
    { value: "Zomba", label: "Zomba" }
  ],
  severity: [
    { value: "all", label: "All Severity Levels" },
    { value: "Critical", label: "Critical", color: "#dc2626", description: "Immediate threat to media freedom" },
    { value: "High", label: "High", color: "#ea580c", description: "Significant impact on information flow" },
    { value: "Medium", label: "Medium", color: "#ca8a04", description: "Moderate concern for media environment" },
    { value: "Low", label: "Low", color: "#16a34a", description: "Minor impact on communication" }
  ],
  platform: [
    { value: "all", label: "All Platforms" },
    { value: "Facebook", label: "Facebook" },
    { value: "Twitter", label: "Twitter/X" },
    { value: "WhatsApp", label: "WhatsApp" },
    { value: "Instagram", label: "Instagram" },
    { value: "TikTok", label: "TikTok" },
    { value: "Website", label: "News Websites" },
    { value: "Radio", label: "Radio" },
    { value: "Television", label: "Television" },
    { value: "Print", label: "Print Media" },
    { value: "Other", label: "Other Sources" }
  ],
  sentiment: [
    { value: "all", label: "All Sentiments" },
    { value: "Positive", label: "Positive", color: "#16a34a", description: "Positive coverage or sentiment" },
    { value: "Neutral", label: "Neutral", color: "#6b7280", description: "Neutral or factual coverage" },
    { value: "Negative", label: "Negative", color: "#dc2626", description: "Negative coverage or sentiment" },
    { value: "Mixed", label: "Mixed", color: "#ca8a04", description: "Mixed sentiment coverage" }
  ]
};

export type TeamName = 'rco' | 'peacebuilding' | 'mesp' | 'communications';
