// Core types for election monitoring dashboard

export interface IncidentCategory {
  id: string;
  name: string;
  specificViolations: string[];
}

export const INCIDENT_CATEGORIES: IncidentCategory[] = [
  {
    id: "political_misconduct",
    name: "Political/electoral misconduct",
    specificViolations: [
      "Voter registration irregularities",
      "Ballot stuffing/tampering",
      "Voter bribery",
      "Biased electoral officials",
      "Voter disenfranchisement",
      "Politicization of public resources"
    ]
  },
  {
    id: "civil_rights",
    name: "Restrictions on civil and political rights",
    specificViolations: [
      "Freedom of assembly restrictions",
      "Media censorship",
      "Internet/social media restrictions",
      "Interference with campaign activities",
      "Restrictions on freedom of movement"
    ]
  },
  {
    id: "political_attacks",
    name: "Politically motivated attacks/harassment/intimidation/incitement",
    specificViolations: [
      "Physical attacks on politicians/supporters",
      "Destruction of campaign materials",
      "Intimidation of voters",
      "Online harassment/cyberbullying",
      "Hate speech/incitement",
      "Threats against candidates or supporters"
    ]
  },
  {
    id: "law_enforcement",
    name: "Law enforcement misconduct",
    specificViolations: [
      "Excessive use of force",
      "Arbitrary arrests of opposition",
      "Biased application of laws",
      "Failure to protect political gatherings",
      "Intimidation by security forces"
    ]
  },
  {
    id: "protests",
    name: "Protests/Demonstrations",
    specificViolations: [
      "Violent protests",
      "Security force response to protests",
      "Counter-demonstrations/clashes",
      "Protest-related property damage",
      "Unlawful assembly"
    ]
  }
];

export const DISTRICTS = [
  "National",
  "Lilongwe",
  "Blantyre",
  "Mzuzu",
  "Zomba",
  "Kasungu",
  "Mangochi",
  "Karonga",
  "Nkhata Bay",
  "Mzimba",
  "Rumphi",
  "Nkhotakota",
  "Salima",
  "Dedza",
  "Ntcheu",
  "Balaka",
  "Machinga",
  "Mulanje",
  "Thyolo",
  "Chiradzulu",
  "Phalombe",
  "Chikwawa",
  "Nsanje",
  "Ntchisi",
  "Dowa",
  "Mchinji",
  "Chitipa",
  "Neno",
  "Likoma"
];

export const PLATFORMS = [
  "Facebook", 
  "Twitter", 
  "WhatsApp", 
  "Website"
];

export const PERPETRATOR_TYPES = [
  "Government officials",
  "Political affiliates - MCP",
  "Political affiliates - DPP",
  "Political affiliates - UTM",
  "Political affiliates - Other parties",
  "Traditional leaders",
  "Security force (police, army)",
  "Private citizens",
  "Religious leaders",
  "Media outlets",
  "Foreign actors",
  "Unknown"
];

export const VICTIM_TYPES = [
  "Civilians",
  "Political candidates",
  "Political party supporters",
  "Civil society/NGOs",
  "Media",
  "Women",
  "Youth",
  "Elderly",
  "Persons with disabilities",
  "Ethnic/religious minorities"
];

export const SEVERITY_LEVELS = [
  { value: "Critical", color: "bg-red-100 text-red-800", badgeColor: "bg-red-500" },
  { value: "High", color: "bg-orange-100 text-orange-800", badgeColor: "bg-orange-500" },
  { value: "Medium", color: "bg-yellow-100 text-yellow-800", badgeColor: "bg-yellow-500" },
  { value: "Low", color: "bg-green-100 text-green-800", badgeColor: "bg-green-500" }
];