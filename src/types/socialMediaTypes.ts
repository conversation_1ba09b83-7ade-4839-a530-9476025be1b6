// Data model for Facebook social media listening backend integration

// Direct database schema types (based on provided SQL schema)
export interface RawFacebookPost {
  id: number;
  post_id: string;
  page_id: string;
  post_text: string | null;
  post_time: string;
  post_category: string | null;
  like_count: number;
  comments_count: number;
  shares_count: number;
  love_count: number;
  wow_count: number;
  haha_count: number;
  sad_count: number;
  angry_count: number;
  post_url: string;
  is_video: boolean;
  video_time: string | null;
  video_length: number | null;
  entry_time: string;
  entry_hour: number;
  entry_date: string;
  video_view_count: number | null;
  post_tags: string | null;
  post_location: string | null;
}

export interface RawFacebookComment {
  id: number;
  comment_id: string;
  post_id: string;
  comment_text: string | null;
  comment_time: string;
  comment_author: string;
  comment_author_id: string | null;
  reactions_count: number;
  entry_date: string;
}

export interface RawFacebookPage {
  id: number;
  page_id: string;
  page_name: string;
  page_category: string;
  page_about: string | null;
  followers_count: number;
  likes_count: number;
  page_url: string;
  page_verification_status: string | null;
  entry_date: string;
  address: string | null;
  phone: string | null;
  emails: string | null;
  website: string | null;
  founded_date: string | null;
  country: string | null;
}

export interface RawFacebookMedia {
  id: number;
  media_id: string;
  post_id: string;
  media_url: string;
  media_type: string;
  width: number | null;
  height: number | null;
  entry_date: string;
}

export interface FacebookPostAuthor {
  name: string;
  username: string;
  profile_url?: string;
  profile_image_url?: string;
  verified: boolean;
}

export interface FacebookPostMedia {
  media_id: string;
  media_url: string;
  media_type: 'photo' | 'video' | 'gif' | 'other';
  width?: number;
  height?: number;
  duration?: number; // For videos (in seconds)
  thumbnail_url?: string;
  alt_text?: string;
}

export interface FacebookPostMetrics {
  likes_count: number;
  comments_count: number;
  shares_count: number;
}

export interface FacebookPostEngagement {
  total_engagement: number; // Sum of likes, comments, shares
  engagement_rate: number; // Engagement relative to author followers
  virality_score: number; // Custom score based on engagement velocity
}

export interface FacebookPost {
  id: number;
  post_id: string;
  author: FacebookPostAuthor;
  content: string;
  created_at: string; // ISO date string
  metrics: FacebookPostMetrics;
  engagement: FacebookPostEngagement;
  media?: FacebookPostMedia[];
  urls?: string[];
  hashtags?: string[];
  mentions?: string[];
  post_url: string;
  post_type: string;
  sentiment: number; // -1 to 1 scale
  scraped_at: string; // ISO date string
  
  // Election-specific fields
  election_related: boolean;
  candidates_mentioned?: string[];
  election_topics?: string[];
  risk_level?: 'low' | 'medium' | 'high' | 'critical';
  risk_factors?: string[];
  geographic_focus?: string;
}

export interface FacebookComment {
  id: number;
  comment_id: string;
  post_id: string;
  author: FacebookPostAuthor;
  content: string;
  created_at: string;
  likes_count: number;
  replies_count: number;
  media?: FacebookPostMedia[];
  sentiment: number;
  is_reply: boolean;
  parent_comment_id?: string;
  election_related: boolean;
  risk_level?: 'low' | 'medium' | 'high' | 'critical';
}

export interface FacebookProfile {
  id: number;
  username: string;
  name: string;
  page_type: 'page' | 'profile' | 'group';
  about?: string;
  location?: string;
  website?: string;
  followers_count: number;
  likes_count: number;
  verified: boolean;
  profile_url: string;
  profile_image_url?: string;
  cover_image_url?: string;
  last_scraped_at: string;
  
  // Election-specific fields
  is_candidate?: boolean;
  is_political_entity?: boolean;
  political_alignment?: string;
  influence_score?: number;
  verified_by_platform?: boolean;
}

export interface FacebookPage extends FacebookProfile {
  page_id: string;
  category: string;
  subcategories?: string[];
  page_created_date: string;
  has_blue_badge: boolean;
  page_description?: string;
  contact_info?: string;
}

// API response interfaces for social media endpoints

export interface FacebookPostsResponse {
  posts: FacebookPost[];
  total_count: number;
  page: number;
  per_page: number;
  has_more: boolean;
  
  // Analytics summaries
  summary?: {
    total_posts: number;
    avg_sentiment: number;
    top_hashtags: { tag: string, count: number }[];
    engagement_overview: {
      total_engagement: number;
      likes: number;
      comments: number;
      shares: number;
    };
  };
}

export interface FacebookPostAnalytics {
  post: FacebookPost;
  detailed_sentiment: {
    score: number;
    positive: number;
    negative: number;
    neutral: number;
    key_phrases: string[];
  };
  engagement_timeline: {
    timestamp: string;
    likes: number;
    comments: number;
    shares: number;
  }[];
  similar_posts: {
    post_id: string;
    content_snippet: string;
    similarity_score: number;
    url: string;
  }[];
  topic_classification: {
    main_topic: string;
    subtopics: { name: string, confidence: number }[];
  };
  risk_assessment: {
    overall_score: number;
    factors: {
      name: string;
      level: 'low' | 'medium' | 'high' | 'critical';
      description: string;
    }[];
  };
}

export interface FacebookProfileAnalytics {
  profile: FacebookProfile;
  post_activity: {
    total_posts: number;
    posts_per_day: number;
    most_active_hours: { hour: number, count: number }[];
    most_active_days: { day: string, count: number }[];
  };
  engagement_metrics: {
    avg_likes_per_post: number;
    avg_comments_per_post: number;
    avg_shares_per_post: number;
    engagement_rate: number;
    engagement_trend: { date: string, value: number }[];
  };
  content_analysis: {
    top_hashtags: { tag: string, count: number }[];
    top_mentions: { username: string, count: number }[];
    top_topics: { topic: string, count: number }[];
    sentiment_distribution: {
      positive: number;
      neutral: number;
      negative: number;
    };
  };
  network_analysis: {
    frequently_interacts_with: {
      username: string;
      name: string;
      interaction_count: number;
    }[];
    audience_demographics?: {
      age_groups?: { group: string, percentage: number }[];
      gender_distribution?: { gender: string, percentage: number }[];
      top_locations?: { location: string, percentage: number }[];
    };
  };
  election_relevance: {
    is_significant_actor: boolean;
    political_leaning_confidence: number;
    candidate_mention_frequency: {
      candidate_name: string;
      mention_count: number;
      sentiment: number;
    }[];
    narrative_amplification: {
      narrative: string;
      amplification_score: number;
    }[];
  };
}

// Integration schemas for the Media Analysis Platform
// These interfaces help map the social media data to our frontend components

export interface SocialMediaElectionData {
  posts_analyzed: number;
  election_related_posts: number;
  platforms: {
    name: string;
    post_count: number;
    percentage: number;
  }[];
  candidates: {
    name: string;
    mentions: number;
    sentiment: number;
    trending_direction: 'up' | 'down' | 'stable';
    key_narratives: string[];
  }[];
  risk_factors: {
    factor: string;
    level: 'Critical' | 'High' | 'Medium' | 'Low';
    trending: 'Increasing' | 'Decreasing' | 'Stable';
    source_platforms: string[];
    last_updated: string;
  }[];
  geographic_distribution: {
    region: string;
    post_count: number;
    sentiment: number;
    main_topics: string[];
  }[];
  narrative_tracking: {
    narrative: string;
    volume: number;
    sentiment: number;
    key_influencers: {
      name: string;
      username: string;
      platform: string;
      influence_score: number;
    }[];
    example_posts: {
      content_snippet: string;
      url: string;
      engagement: number;
    }[];
  }[];
}

export interface EarlyWarningSocialData {
  alerts: {
    id: string;
    title: string;
    description: string;
    source_platform: string;
    source_posts: string[];
    severity: 'Critical' | 'High' | 'Medium' | 'Low';
    detected_at: string;
    location: string;
    status: 'New' | 'In Progress' | 'Resolved';
  }[];
  trending_hashtags: {
    tag: string;
    volume: number;
    sentiment: number;
    velocity: number;
    related_to_election: boolean;
  }[];
  coordinated_activity: {
    id: string;
    description: string;
    accounts_involved: number;
    first_detected: string;
    platforms: string[];
    content_focus: string;
    confidence_score: number;
  }[];
  emerging_narratives: {
    narrative: string;
    first_detected: string;
    growth_rate: number;
    platforms: string[];
    key_accounts: {
      username: string;
      platform: string;
      verified: boolean;
    }[];
    risk_assessment: 'Critical' | 'High' | 'Medium' | 'Low';
  }[];
  platform_metrics: {
    platform: string;
    total_election_content: number;
    harmful_content_percentage: number;
    intervention_rate: number;
    average_response_time: number; // in hours
  }[];
}

// These types will help ensure the backend data can be properly consumed by our frontend components
