// Comprehensive TypeScript types for Monitored Incidents functionality

// Interface for detailed entities involved structure
export interface EntitiesInvolved {
  victims?: string[];
  perpetrators?: string[];
  witnesses_sources?: string[];
}

// Interface for political context information
export interface PoliticalContext {
  party_involved?: string | null;
  youth_wing_involved?: string | null;
  institutional_failure?: string | null;
}

export interface Incident {
  id: number;
  post_summary: string;
  primary_category: string;
  severity_level: 'Critical' | 'High' | 'Medium' | 'Low';
  district: string | null;
  created_at: string;
  platform: string;
  perpetrator: string;
  victims_profile: string;
  details: string;
  date_of_incident: string;
  
  // Enhanced structured fields
  entities_involved?: EntitiesInvolved | any;
  political_context?: PoliticalContext | any;
  keywords_related_to_incident?: string[] | null;
  physical_integrity_violation?: string | null;
  
  // Additional fields from the full analysis
  original_language?: string;
  english_translation?: string | null;
  raw_content?: string;
  source_url?: string;
  timestamp_provided?: string;
  civil_rights_restriction?: string | null;
  electoral_misconduct?: string | null;
  discrimination_type?: string | null;
  law_enforcement_misconduct?: string | null;
  gender_based_violence?: string | null;
  political_attacks?: string | null;
  pwa_attacks?: string | null;
  protest_type?: string | null;
  gender_of_victim?: string;
  incident_year?: number;
  incident_month?: number;
  incident_day?: number;
  sentiment?: string;
  election_relevance?: string;
  total_incidents?: number;
  main_categories_identified?: string[];
  districts_affected?: string[];
  time_period?: string;
  overall_sentiment?: string;
  key_themes?: string[];
  sources_mentioned?: string[];
  language_detected?: string;
  keyword_density?: string | null;
  alert_level?: string | null;
  entity_involvement_score?: string | null;
  context_assessment?: string | null;
  risk_assessment?: string | null;
  total_keywords_found?: number;
  highest_frequency_category?: string | null;
  full_analysis_json?: any;
  updated_at?: string;
}

export interface IncidentFilters {
  limit?: number;
  offset?: number;
  severity?: 'all' | 'Critical' | 'High' | 'Medium' | 'Low';
  district?: string;
  category?: string;
  platform?: string;
  candidate?: string;
  political_entity?: string;
  prominent_name?: string;
  frequent_phrase?: string;
  political_electoral_misconduct?: string;
  discrimination_disadvantaged?: string;
  law_enforcement_misconduct?: string;
  physical_integrity_violation?: string;
  gender_based_violence?: string;
  political_attacks_harassment?: string;
  pwa_attacks?: string;
  protests_demonstrations?: string;  perpetrator_profile?: string;
  victim_profile?: string;
  sentiment?: string;
  report_period?: string;
  date_from?: string;
  date_to?: string;
}

export interface IncidentsPagination {
  total: number;
  limit: number;
  offset: number;
  has_more: boolean;
}

export interface IncidentsFiltersApplied {
  severity: string;
  district: string;
  category: string;
  platform: string;
}

export interface IncidentsData {
  incidents: Incident[];
  pagination: IncidentsPagination;
  filters_applied: IncidentsFiltersApplied;
}

export interface IncidentsResponse {
  success: boolean;
  data: IncidentsData;
  message: string;
}

// Incident Categories as per the prompt requirements
export const INCIDENT_CATEGORIES = [
  'Political/electoral misconduct',
  'Violation of right to physical integrity (violent attacks)',
  'Law enforcement misconduct (Violation of right to liberty and security of persons)',
  'Restrictions on civil and political rights',
  'Politically motivated attacks/harassment/intimidation/incitement',
  'Misinformation'
] as const;

export type IncidentCategory = typeof INCIDENT_CATEGORIES[number];

// Severity levels
export const SEVERITY_LEVELS = ['Critical', 'High', 'Medium', 'Low'] as const;
export type SeverityLevel = typeof SEVERITY_LEVELS[number];

// Malawi Districts (comprehensive list)
export const MALAWI_DISTRICTS = [
  'National',
  'Balaka',
  'Blantyre',
  'Chikwawa',
  'Chiradzulu',
  'Chitipa',
  'Dedza',
  'Dowa',
  'Karonga',
  'Kasungu',
  'Likoma',
  'Lilongwe',
  'Machinga',
  'Mangochi',
  'Mchinji',
  'Mulanje',
  'Mwanza',
  'Mzimba',
  'Mzuzu',
  'Neno',
  'Nkhata Bay',
  'Nkhotakota',
  'Nsanje',
  'Ntcheu',
  'Ntchisi',
  'Phalombe',
  'Rumphi',
  'Salima',
  'Thyolo',
  'Zomba'
] as const;

export type MalawiDistrict = typeof MALAWI_DISTRICTS[number];

// Platform types
export const PLATFORMS = [
  'Twitter',
  'Facebook',
  'Instagram',
  'Website',
  'TikTok',
  'WhatsApp',
  'Other'
] as const;

export type Platform = typeof PLATFORMS[number];

// Malawi Political Candidates
export const MALAWI_CANDIDATES = [
  'Lazarus McCarthy Chakwera',
  'Peter Mutharika', 
  'Dalitso Kabambe',
  'Peter Kuwani',
  'Atupele Muluzi',
  'Reverend Hadwick Kaliya',
  'Professor John Eugene Chisi',
  'Joyce Banda'
] as const;

export type MalawiCandidate = typeof MALAWI_CANDIDATES[number];

// Political Parties, CSOs, Movements, and Groupings
export const POLITICAL_ENTITIES = [
  // Political Parties
  'MCP - Malawi Congress Party',
  'DPP - Democratic Progressive Party', 
  'UTM - United Transformation Movement',
  'UDF - United Democratic Front',
  'PP - People\'s Party',
  'AFORD - Alliance for Democracy',
  'DEPECO - Democratic People\'s Congress',
  'FP - Freedom Party',
  'UP - Umodzi Party',
  'TPM - Tikonze People\'s Movement',
  'Republican Party',
  'MGODE - Movement for Genuine Democracy',
  'NCD - New Congress for Democracy',
  'PDP - People\'s Development Party',
  'PETRA - People\'s Transformation Party',
  'MAFUNDE - Malawi Forum for Unity and Development',
  
  // Youth Wings & Movements
  'DPP Cadets',
  'MCP National Youth League',
  'Kokoliko',
  'Mighty Tambala Graduates',
  'Born Free',
  'MCPDN - MCP Development Network',
  'CTM - Chilima Transformation Movement',
  'Citizen Transformation Movement',
  
  // Civil Society Organizations
  'HRDC - Human Rights Defenders Coalition',
  'CHRR - Centre for Human Rights and Rehabilitation',
  'HRDN - Human Rights Defenders Network',
  'CDEDI - Centre for Democracy & Economic Development Initiatives',
  'PAC - Public Affairs Committee',
  'NAP - National Advocacy Platform',
  'MISA - Media Institute of Southern Africa',
  'CONGOMA - Council for Non-Governmental Organizations',
  'MEJN - Malawi Economic Justice Network',
  'CFJ - Citizens for Justice',
  'CCJP - Catholic Commission for Justice and Peace',
  'NGO GCN - NGO Gender Coordinating Network',
  'WROs - Women Rights Organizations',
  'WLOs - Women-Led Organizations',
  
  // Government Institutions
  'MEC - Malawi Electoral Commission',
  'NRB - National Registration Bureau',
  'ACB - Anti-Corruption Bureau',
  'MHRC - Malawi Human Rights Commission',
  'NAO - National Audit Office',
  'Office of the Ombudsman',
  'DPP Office - Directorate of Public Prosecutions',
  'DPAD - Directorate of Public Assets Declaration',
  'PSRC - Public Sector Reform Commission',
  'ODPP - Office of Director of Public Procurement',
  
  // Security & Justice
  'MPS - Malawi Police Service',
  'MDF - Malawi Defence Force',
  'Judiciary',
  'Traditional Authorities',
  'Chiefs',
  
  // International Organizations
  'UNDP - United Nations Development Programme',
  'UNDP Malawi',
  'UN RCO - UN Resident Coordinator Office',
  'UN Women',
  'UNICEF',
  'OHCHR - Office of the High Commissioner for Human Rights',
  'African Union',
  'SADC - Southern African Development Community',
  'Commonwealth Secretariat',
  'European Union',
  
  // Professional & Business Organizations
  'MCCI - Malawi Confederation of Commerce and Industry',
  'NCIC - National Construction Industry Council',
  'Law Society of Malawi',
  'Medical Association of Malawi'
] as const;

export type PoliticalEntity = typeof POLITICAL_ENTITIES[number];

// Prominent Names in Malawi Politics
export const PROMINENT_NAMES = [
  'Kondwani Nankhumwa',
  'Dalitso Kabambe',
  'Nicholas Dausi',
  'Timothy Mtambo',
  'Gift Trapence',
  'Sylvester Namiwa',
  'Bon Kalindo',
  'Bakili Muluzi',
  'Brown Mpinganjira',
  'Charles Mchacha',
  'Patricia Kaliati',
  'Richard Chimwendo Banda',
  'Jomo Isaac Osman',
  'Onjezani Kenani',
  'Martha Chizuma',
  'Frank Mwenifumbo',
  'Michael Bizwick Usi',
  'Simplex Chithyola Banda',
  'Nancy Tembo',
  'Sam Kawale',
  'Owen Chomanika',
  'Enock Kamzingeni Chihana',
  'Titus Mvalo',
  'Ezekiel Peter Ching\'oma',
  'Khumbize Kandodo Chiponda',
  'Sosten Gwengwe',
  'Uchizi Mkandawire',
  'Dr. Jessie Kabwira',
  'Madalitso Wirima Kambauwa',
  'Jacob Hara',
  'Ken Zikhale Ng\'oma',
  'Vera Kamtukule',
  'Jean Muonaowauza Sendeza',
  'Vitumbiko A.Z. Mumba',
  'Monica Chang\'anamuno',
  'Abida Mia',
  'Ibrahim Matola',
  'Deus Gumba',
  'Moses Kunkuyu Kalongashawa',
  'Joyce Chitsulo',
  'Halima Alima Daud',
  'Nancy Chaola Mdooko',
  'Liana Kakhobwe Chapota',
  'Patricia Nangozo Kainga',
  'Fenella Frost',
  'Challa Getachew'
] as const;

export type ProminentName = typeof PROMINENT_NAMES[number];

// Frequent Words/Phrases - Please provide the actual list
export const FREQUENT_PHRASES = [
  // TODO: Add actual frequent phrases from your analysis
  'political sabotage',
  'electoral misconduct',
  'campaign violence',
  'voter intimidation'
] as const;

export type FrequentPhrase = typeof FREQUENT_PHRASES[number];

// Violation Categories with their specific subcategories
export const POLITICAL_ELECTORAL_MISCONDUCT = [
  'Discrimination/politicization in allocation of resources (e.g lands, goods and services)',
  'Disruption of political meetings/destruction of campaign materials',
  'Electoral misconduct (e.g selling of voter registration cards, violation of electoral laws/code of conduct)',
  'Physical attacks against properties'
] as const;

export const DISCRIMINATION_DISADVANTAGED_GROUPS = [
  'Religious discrimination',
  'Discrimination against persons with disabilities',
  'Discrimination on the basis of sexual orientation',
  'Discrimination on the basis of nationality',
  'Discrimination against migrants',
  'Discrimination against IDPs',
  'Discrimination against persons deprived of liberty (Prisoners, Detainees)',
  'Discrimination against COVID19 patients'
] as const;

export const LAW_ENFORCEMENT_MISCONDUCT = [
  'Arbitrary arrest/detention',
  'Police harassment/instigated violence/excessive use of force',
  'Torture and ill-treatment',
  'Impunity (reluctance to arrest, investigate or prosecute)',
  'Extra-judicial killings/Deaths in custody'
] as const;

export const PHYSICAL_INTEGRITY_VIOLATIONS = [
  'Attempted murder, abductions, violent assaults',
  'Death threats',
  'Inter-communal violence',
  'Mob violence',
  'Violent attacks against HRDs/civil society actors/journalists',
  'Bombing/Explosions'
] as const;

export const GENDER_BASED_VIOLENCE = [
  'Electoral violence/SGBV',
  'Electoral gender-based hate speech, intimidation, harassment, discrimination'
] as const;

export const POLITICAL_ATTACKS_HARASSMENT = [
  'Incitement/hate speech',
  'Harassment/intimidation/death threats',
  'Use of defamation/state security laws'
] as const;

export const PWA_ATTACKS = [
  'Attempted killings/abductions of PWA',
  'Killings/abductions of PWA',
  'Inciting violence against PWA',
  'Discrimination against PWA'
] as const;

export const PROTESTS_DEMONSTRATIONS = [
  'Politically motivated protests',
  'Economically motivated protests',
  'Socially motivated protests',
  'Pandemic motivated protests'
] as const;

// Type definitions
export type PoliticalElectoralMisconduct = typeof POLITICAL_ELECTORAL_MISCONDUCT[number];
export type DiscriminationDisadvantagedGroups = typeof DISCRIMINATION_DISADVANTAGED_GROUPS[number];
export type LawEnforcementMisconduct = typeof LAW_ENFORCEMENT_MISCONDUCT[number];
export type PhysicalIntegrityViolations = typeof PHYSICAL_INTEGRITY_VIOLATIONS[number];
export type GenderBasedViolence = typeof GENDER_BASED_VIOLENCE[number];
export type PoliticalAttacksHarassment = typeof POLITICAL_ATTACKS_HARASSMENT[number];
export type PWAAttacks = typeof PWA_ATTACKS[number];
export type ProtestsDemonstrations = typeof PROTESTS_DEMONSTRATIONS[number];

// Perpetrator/Victim Profiles organized by category
export const PERPETRATOR_VICTIM_PROFILES = {
  'Political Parties & Affiliates': [
    'Democratic Progressive Party (DPP)',
    'Malawi Congress Party (MCP)',
    'United Transformation Movement (UTM)',
    'United Democratic Front (UDF)',
    'People\'s Party (PP)',
    'Alliance for Democracy (AFORD)',
    'Democratic People\'s Congress (DEPECO)',
    'Freedom Party (FP)',
    'Umodzi Party (UP)',
    'Tikonze People\'s Movement (TPM)',
    'Republican Party',
    'Movement for Genuine Democracy (MGODE)',
    'New Congress for Democracy (NCD)',
    'People\'s Development Party (PDP)',
    'People\'s Transformation Party (Petra)',
    'Malawi Forum for Unity and Development (Mafunde)',
    'Political affiliates - opposition parties',
    'Political affiliates - Independent',
    'Political affiliates - MCP & UTM alliance',
    'Political affiliates - DPP & UDF alliance'
  ],
  'Political Youth Wings & Movements': [
    'DPP Cadets',
    'MCP National Youth League',
    'Kokoliko',
    'Mighty Tambala Graduates',
    'Born Free',
    'MCPDN',
    'Chilima Transformation Movement',
    'Citizen Transformation Movement'
  ],
  'Civil Society Organizations': [
    'Human Rights Defenders Coalition (HRDC)',
    'Centre for Human Rights and Rehabilitation (CHRR)',
    'Human Rights Defenders Network (HRDN)',
    'Centre for Democracy & Economic Development Initiatives (CDEDI)',
    'Public Affairs Committee (PAC)',
    'National Advocacy Platform (NAP)',
    'Media Institute of Southern Africa (MISA)',
    'Council for Non-Governmental Organizations (CONGOMA)',
    'Malawi Economic Justice Network (MEJIN)',
    'Citizens for Justice (CFJ)',
    'Catholic Commission for Justice and Peace (CCJP)',
    'NGO Gender Coordinating Network (NGO GCN)',
    'Women Rights Organizations (WROs)',
    'Women-Led Organizations (WLOs)'
  ],
  'Government Institutions': [
    'Malawi Electoral Commission (MEC)',
    'National Registration Bureau (NRB)',
    'Anti-Corruption Bureau (ACB)',
    'Malawi Human Rights Commission (MHRC)',
    'National Audit Office',
    'Office of the Ombudsman',
    'Directorate of Public Prosecutions',
    'Directorate of Public Assets Declaration',
    'Public Sector Reform Commission',
    'Office of Director of Public Procurement'
  ],
  'Security & Justice': [
    'Malawi Police Service',
    'Malawi Defence Force',
    'Judiciary',
    'Traditional Authorities/Chiefs'
  ],
  'International Organizations': [
    'United Nations Development Programme (UNDP)',
    'UNDP Malawi',
    'UN Resident Coordinator Office (RCO)',
    'UN Women',
    'UNICEF',
    'OHCHR',
    'African Union',
    'SADC',
    'Commonwealth Secretariat',
    'European Union'
  ],
  'Professional & Business': [
    'Malawi Confederation of Commerce and Industry (MCCI)',
    'National Construction Industry Council (NCIC)',
    'Law Society of Malawi',
    'Medical Association'
  ],
  'Key Political Candidates': [
    'Lazarus McCarthy Chakwera (MCP)',
    'Peter Mutharika (DPP)',
    'Dalitso Kabambe (UTM)',
    'Peter Kuwani',
    'Atupele Muluzi (UDF)',
    'Reverend Hadwick Kaliya',
    'Professor John Eugene Chisi (Umodzi Party)',
    'Joyce Banda (PP)'
  ]
} as const;

// Flatten all profiles for easy access
export const ALL_PROFILES = Object.values(PERPETRATOR_VICTIM_PROFILES).flat();

// Sentiment options for filtering
export const SENTIMENT_OPTIONS = [
  'Negative',
  'Neutral', 
  'Positive'
] as const;

export type SentimentOption = typeof SENTIMENT_OPTIONS[number];

// Report period options for filtering
export const REPORT_PERIOD_OPTIONS = [
  'all_time',
  'last_7_days',
  'daily',
  'weekly', 
  'monthly',
  'custom'
] as const;

export type ReportPeriodOption = typeof REPORT_PERIOD_OPTIONS[number];

// Report period display labels
export const REPORT_PERIOD_LABELS = {
  'all_time': 'All Time',
  'last_7_days': 'Last 7 Days',
  'daily': 'Daily',
  'weekly': 'Weekly',
  'monthly': 'Monthly',
  'custom': 'Custom Range'
} as const;
