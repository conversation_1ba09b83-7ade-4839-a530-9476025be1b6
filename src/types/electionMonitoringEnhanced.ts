// Enhanced interfaces for the Election Monitoring Dashboard

export interface Incident {
  id: string;
  date_of_incident: string;
  district: string;
  platform: 'Facebook' | 'Twitter' | 'WhatsApp' | 'Website';
  primary_category: string;
  specific_violation: string;
  severity_level: 'Critical' | 'High' | 'Medium' | 'Low';
  summary: string;
  description: string;
  perpetrator: string;
  victims_profile: string;
  election_relevance: 'Direct' | 'Indirect' | 'General';
  language_detected: 'English' | 'Chichewa' | 'Mixed';
  status: 'New' | 'Under Review' | 'Verified' | 'Resolved' | 'False Alert';
  verified: boolean;
  source_url?: string;
  reported_by: string;
  created_at: string;
  updated_at: string;
}

export interface FilterOptions {
  district?: string[];
  platform?: string[];
  primary_category?: string[];
  specific_violation?: string[];
  severity_level?: ('Critical' | 'High' | 'Medium' | 'Low')[];
  perpetrator?: string[];
  victims_profile?: string[];
  election_relevance?: ('Direct' | 'Indirect' | 'General')[];
  language_detected?: ('English' | 'Chichewa' | 'Mixed')[];
  status?: string[];
  verified?: boolean;
  start_date?: string;
  end_date?: string;
}

export interface FilterPreset {
  id: string;
  name: string;
  filters: FilterOptions;
  created_at: string;
}

export interface DistrictData {
  name: string;
  incidents: number;
  highSeverity: number;
  critical: number;
  coordinates: [number, number];
}

export interface DashboardStats {
  total_incidents: number;
  new_incidents_today: number;
  high_severity_incidents: number;
  new_high_severity_today: number;
  districts_affected: number;
  districts_coverage_percentage: number;
  platforms_monitored: number[];
  platform_names: string[];
}

export interface CategoryData {
  name: string;
  count: number;
  percentage: number;
  color: string;
}

export interface SeverityByDistrict {
  district: string;
  critical: number;
  high: number;
  medium: number;
  low: number;
}

export interface PlatformAnalysis {
  platform: string;
  incidents: number;
  percentage: number;
  color: string;
}

export interface TrendData {
  date: string;
  incidents: number;
  critical: number;
  high: number;
  medium: number;
  low: number;
}

export interface ElectionMonitoringDashboardData {
  stats: DashboardStats;
  incidents: Incident[];
  categoryBreakdown: CategoryData[];
  severityByDistrict: SeverityByDistrict[];
  platformAnalysis: PlatformAnalysis[];
  trendData: TrendData[];
  districtData: DistrictData[];
  trendingAlerts: {
    id: string;
    title: string;
    severity: string;
    time: string;
    location: string;
  }[];
}