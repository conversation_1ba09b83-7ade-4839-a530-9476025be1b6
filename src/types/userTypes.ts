// User management types and interfaces

export type UserRole = 'admin' | 'moderator' | 'viewer';

export interface User {
  id: number;  // API returns number, not string
  full_name: string;
  email: string;
  role: UserRole;
  is_active: number;  // API returns 1/0, not boolean
  created_at: string;
  updated_at: string;
  last_login?: string | null;
}

export interface CreateUserRequest {
  full_name: string;
  email: string;
  password: string;
  role: UserRole;
}

export interface UpdateUserRequest {
  full_name?: string;
  email?: string;
  role?: UserRole;
  is_active?: boolean;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  data: {
    token: string;
    user: User;
  };
  message: string;
  timestamp: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface AdminChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface UsersListResponse {
  success: boolean;
  data: User[];  // users array directly in data
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  message?: string;
  timestamp?: string;
}

export interface UserStatsResponse {
  success: boolean;
  data: {
    total_users: number;
    active_users: number;
    inactive_users: number;
    users_by_role: {
      admin: number;
      moderator: number;
      viewer: number;
    };
    recent_logins: number;
  };
  message: string;
  timestamp: string;
}

export interface UserFilters {
  page?: number;
  limit?: number;
  role?: UserRole;
  search?: string;
  is_active?: boolean;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  timestamp: string;
}

// Form validation types
export interface UserFormData {
  full_name: string;
  email: string;
  password?: string;
  role: UserRole;
  is_active?: boolean;
}

export interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// Auth context types
export interface AuthUser extends User {
  permissions?: string[];
}

export interface AuthContextType {
  user: AuthUser | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  updateUser: (user: AuthUser) => void;
  hasRole: (role: UserRole) => boolean;
  hasPermission: (permission: string) => boolean;
}

// Error types
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

export class AuthenticationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AuthorizationError';
  }
}

// Validation constants
export const PASSWORD_REQUIREMENTS = {
  minLength: 8,
  requireLowercase: true,
  requireUppercase: true,
  requireNumber: true,
  requireSpecialChar: false,
} as const;

export const USER_ROLES: { value: UserRole; label: string; description: string }[] = [
  {
    value: 'admin',
    label: 'Administrator',
    description: 'Full system access and user management'
  },
  {
    value: 'moderator',
    label: 'Moderator',
    description: 'Content moderation and limited admin functions'
  },
  {
    value: 'viewer',
    label: 'Viewer',
    description: 'Read-only access to system data'
  }
];
