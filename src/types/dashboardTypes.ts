// Dashboard API response types

export interface DashboardSummary {
  total_incidents: number;
  platforms: number;
  categories: number;
  districts: number;
}

export interface PlatformCount {
  platform: string;
  count: number;
}

export interface CategoryCount {
  primary_category: string;
  count: number;
}

export interface SeverityCount {
  severity_level: string;
  count: number;
}

export interface DistrictCount {
  district: string;
  count: number;
}

export interface RecentIncident {
  id: number;
  post_summary: string;
  primary_category: string;
  district: string | null;
  severity_level: string;
  created_at: string;
}

export interface MonthlyTrend {
  month: string;
  count: number;
}

export interface DashboardData {
  summary: DashboardSummary;
  by_platform: PlatformCount[];
  by_category: CategoryCount[];
  by_severity: SeverityCount[];
  by_district: DistrictCount[];
  recent_incidents: RecentIncident[];
  monthly_trend: MonthlyTrend[];
}

export interface DashboardApiResponse {
  success: boolean;
  dashboard: DashboardData;
  timestamp: string;
}

// Color mappings for charts
export const PLATFORM_COLORS: { [key: string]: string } = {
  Website: "#3b82f6",
  Twitter: "#1DA1F2", 
  Facebook: "#1877F2",
  Other: "#6b7280"
};

export const SEVERITY_COLORS: { [key: string]: string } = {
  Critical: "#dc2626",
  High: "#ea580c",
  Medium: "#d97706",
  Low: "#65a30d"
};
