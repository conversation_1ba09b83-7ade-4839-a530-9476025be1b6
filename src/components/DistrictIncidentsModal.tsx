import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  AlertTriangle, 
  MapPin, 
  Calendar, 
  User, 
  FileText,
  ExternalLink,
  TrendingUp,
  Activity
} from 'lucide-react';

interface DistrictIncidentsModalProps {
  isOpen: boolean;
  onClose: () => void;
  districtName: string;
  incidentCount: number;
}

// Mock incident data following the structure from other pages
const generateMockIncidents = (districtName: string, count: number) => {
  const platforms = ['Facebook', 'Twitter', 'WhatsApp', 'TikTok', 'Instagram', 'Website'];
  const categories = [
    'Political/electoral misconduct',
    'Violation of right to physical integrity (violent attacks)',
    'Law enforcement misconduct (Violation of right to liberty and security of persons)',
    'Restrictions on civil and political rights',
    'Politically motivated attacks/harassment/intimidation/incitement',
    'Gender-based violence',
    'Discrimination against disadvantaged groups'
  ];
  const severities = ['Critical', 'High', 'Medium', 'Low'];
  const sentiments = ['Negative', 'Neutral', 'Positive'];

  const mockIncidents = [];
  for (let i = 1; i <= count; i++) {
    mockIncidents.push({
      id: `${districtName}-${i}`,
      post_summary: `Incident ${i} reported in ${districtName} district involving ${categories[Math.floor(Math.random() * categories.length)].toLowerCase()}`,
      primary_category: categories[Math.floor(Math.random() * categories.length)],
      severity_level: severities[Math.floor(Math.random() * severities.length)],
      district: districtName,
      platform: platforms[Math.floor(Math.random() * platforms.length)],
      perpetrator: Math.random() > 0.5 ? 'Political activist' : 'Unknown individual',
      victims_profile: Math.random() > 0.5 ? 'Opposition supporters' : 'General public',
      details: `Detailed description of incident ${i} that occurred in ${districtName}. This incident involves various stakeholders and has been reported through social media monitoring.`,
      date_of_incident: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      created_at: new Date(Date.now() - Math.floor(Math.random() * 7) * 24 * 60 * 60 * 1000).toISOString(),
      sentiment: sentiments[Math.floor(Math.random() * sentiments.length)]
    });
  }
  return mockIncidents;
};

const getSeverityColor = (severity: string) => {
  switch (severity.toLowerCase()) {
    case 'critical': return 'bg-red-100 text-red-800 border-red-200';
    case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'low': return 'bg-green-100 text-green-800 border-green-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getCategoryColor = (category: string) => {
  switch (category) {
    case 'Political/electoral misconduct': return 'bg-purple-100 text-purple-800';
    case 'Violation of right to physical integrity (violent attacks)': return 'bg-red-100 text-red-800';
    case 'Law enforcement misconduct (Violation of right to liberty and security of persons)': return 'bg-orange-100 text-orange-800';
    case 'Restrictions on civil and political rights': return 'bg-blue-100 text-blue-800';
    case 'Politically motivated attacks/harassment/intimidation/incitement': return 'bg-pink-100 text-pink-800';
    case 'Gender-based violence': return 'bg-rose-100 text-rose-800';
    case 'Discrimination against disadvantaged groups': return 'bg-indigo-100 text-indigo-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getPlatformIcon = (platform: string) => {
  switch (platform) {
    case 'Facebook': return '📘';
    case 'Twitter': return '🐦';
    case 'WhatsApp': return '💬';
    case 'TikTok': return '🎵';
    case 'Instagram': return '📸';
    case 'Website': return '🌐';
    default: return '📱';
  }
};

const getSentimentColor = (sentiment: string) => {
  switch (sentiment) {
    case 'Positive': return 'bg-green-50 text-green-700 border-green-200';
    case 'Negative': return 'bg-red-50 text-red-700 border-red-200';
    case 'Neutral': return 'bg-gray-50 text-gray-700 border-gray-200';
    default: return 'bg-gray-50 text-gray-700 border-gray-200';
  }
};

const DistrictIncidentsModal: React.FC<DistrictIncidentsModalProps> = ({
  isOpen,
  onClose,
  districtName,
  incidentCount
}) => {
  const mockIncidents = generateMockIncidents(districtName, incidentCount);
  
  // Calculate statistics
  const severityStats = mockIncidents.reduce((acc, incident) => {
    acc[incident.severity_level] = (acc[incident.severity_level] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const categoryStats = mockIncidents.reduce((acc, incident) => {
    acc[incident.primary_category] = (acc[incident.primary_category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <MapPin className="w-5 h-5 text-blue-600" />
            {districtName} District Incidents
            <Badge variant="secondary" className="ml-2">
              {incidentCount} Total
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Statistics Sidebar */}
          <div className="lg:col-span-1 space-y-4">
            {/* Severity Breakdown */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <AlertTriangle className="w-4 h-4" />
                  By Severity
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {Object.entries(severityStats).map(([severity, count]) => (
                  <div key={severity} className="flex items-center justify-between">
                    <Badge className={getSeverityColor(severity)} variant="outline">
                      {severity}
                    </Badge>
                    <span className="text-sm font-semibold">{count}</span>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Category Breakdown */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Activity className="w-4 h-4" />
                  Top Categories
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {Object.entries(categoryStats)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 3)
                  .map(([category, count]) => (
                  <div key={category} className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-xs font-medium text-gray-700 truncate">
                        {category.length > 20 ? `${category.substring(0, 20)}...` : category}
                      </span>
                      <span className="text-xs font-bold">{count}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1">
                      <div 
                        className="bg-blue-600 h-1 rounded-full" 
                        style={{ width: `${(count / incidentCount) * 100}%` }}
                      />
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  Quick Stats
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-center">
                  <div className="text-lg font-bold text-red-600">
                    {mockIncidents.filter(i => ['Critical', 'High'].includes(i.severity_level)).length}
                  </div>
                  <p className="text-xs text-gray-600">High Priority</p>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">
                    {new Set(mockIncidents.map(i => i.platform)).size}
                  </div>
                  <p className="text-xs text-gray-600">Platforms</p>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600">
                    {mockIncidents.filter(i => 
                      new Date(i.date_of_incident) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                    ).length}
                  </div>
                  <p className="text-xs text-gray-600">Last 7 Days</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Incidents List */}
          <div className="lg:col-span-3">
            <ScrollArea className="h-[500px] pr-4">
              <div className="space-y-4">
                {mockIncidents.map((incident) => (
                  <Card key={incident.id} className="border border-gray-200 hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      {/* Header */}
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex gap-2 flex-wrap">
                          <Badge className={getSeverityColor(incident.severity_level)}>
                            {incident.severity_level}
                          </Badge>
                          <Badge className={getCategoryColor(incident.primary_category)} variant="outline">
                            {incident.primary_category.length > 30 
                              ? `${incident.primary_category.substring(0, 30)}...` 
                              : incident.primary_category}
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {getPlatformIcon(incident.platform)} {incident.platform}
                          </Badge>
                          <Badge className={getSentimentColor(incident.sentiment)} variant="outline">
                            {incident.sentiment}
                          </Badge>
                        </div>
                        <span className="text-xs text-gray-500">#{incident.id}</span>
                      </div>

                      {/* Content */}
                      <div className="space-y-2 mb-3">
                        <p className="text-sm text-gray-800 leading-relaxed">
                          {incident.post_summary}
                        </p>
                        <p className="text-xs text-gray-600">
                          {incident.details}
                        </p>
                      </div>

                      {/* Metadata */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-600">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          <span>Incident: {new Date(incident.date_of_incident).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <FileText className="w-3 h-3" />
                          <span>Reported: {new Date(incident.created_at).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <User className="w-3 h-3" />
                          <span>Perpetrator: {incident.perpetrator}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <User className="w-3 h-3" />
                          <span>Victims: {incident.victims_profile}</span>
                        </div>
                      </div>

                      {/* Action */}
                      <div className="mt-3 pt-3 border-t border-gray-100">
                        <button className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1">
                          <ExternalLink className="w-3 h-3" />
                          View Full Details
                        </button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DistrictIncidentsModal;