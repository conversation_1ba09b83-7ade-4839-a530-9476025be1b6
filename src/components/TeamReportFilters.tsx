import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TeamReportFilters, TeamName, MESP_FILTER_OPTIONS, PEACE_BUILDING_FILTER_OPTIONS, COMMUNICATIONS_FILTER_OPTIONS } from '../types/teamReportsTypes';

interface TeamReportFiltersProps {
  filters: TeamReportFilters;
  onFiltersChange: (filters: TeamReportFilters) => void;
  team: TeamName;
}

export const TeamReportFiltersComponent: React.FC<TeamReportFiltersProps> = ({
  filters,
  onFiltersChange,
  team
}) => {  // Get filter options based on team type
  const getFilterOptions = () => {
    if (team === 'mesp') {
      return MESP_FILTER_OPTIONS;
    }
    if (team === 'peacebuilding') {
      return PEACE_BUILDING_FILTER_OPTIONS;
    }
    if (team === 'communications') {
      return COMMUNICATIONS_FILTER_OPTIONS;
    }
    // Default options for other teams
    return {
      days: MESP_FILTER_OPTIONS.days,
      district: MESP_FILTER_OPTIONS.district,
      severity: MESP_FILTER_OPTIONS.severity
    };
  };

  const filterOptions = getFilterOptions();

  return (
    <Card className="mb-6">
      <CardContent className="pt-6">
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Time Period</label>
            <Select
              value={filters.days.toString()}
              onValueChange={(value) =>
                onFiltersChange({ ...filters, days: parseInt(value) })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {filterOptions.days.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">District</label>
            <Select
              value={filters.district}
              onValueChange={(value) =>
                onFiltersChange({ ...filters, district: value })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {filterOptions.district.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Severity Level</label>
            <Select
              value={filters.severity}
              onValueChange={(value) =>
                onFiltersChange({ ...filters, severity: value })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {filterOptions.severity.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      {option.color && (
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: option.color }}
                        />
                      )}
                      {option.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Communications-specific filters */}
          {team === 'communications' && (
            <>
              <div className="space-y-2">
                <label className="text-sm font-medium">Platform</label>
                <Select
                  value={filters.platform || 'all'}
                  onValueChange={(value) =>
                    onFiltersChange({ ...filters, platform: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {filterOptions.platform?.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Sentiment</label>
                <Select
                  value={filters.sentiment || 'all'}
                  onValueChange={(value) =>
                    onFiltersChange({ ...filters, sentiment: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {filterOptions.sentiment?.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          {option.color && (
                            <div 
                              className="w-3 h-3 rounded-full" 
                              style={{ backgroundColor: option.color }}
                            />
                          )}
                          <div>
                            <div className="font-medium">{option.label}</div>
                            {option.description && (
                              <div className="text-xs text-gray-500">{option.description}</div>
                            )}
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </>
          )}

          {/* MESP-specific filters */}
          {team === 'mesp' && (
            <>
              <div className="space-y-2">
                <label className="text-sm font-medium">Incident Type</label>
                <Select
                  value={filters.incidentType || 'all'}
                  onValueChange={(value) =>
                    onFiltersChange({ ...filters, incidentType: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {filterOptions.incidentType?.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div>
                          <div className="font-medium">{option.label}</div>
                          {option.category && (
                            <div className="text-xs text-gray-500">{option.category}</div>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Platform</label>
                <Select
                  value={filters.platform || 'all'}
                  onValueChange={(value) =>
                    onFiltersChange({ ...filters, platform: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {filterOptions.platform?.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Perpetrator</label>
                <Select
                  value={filters.perpetrator || 'all'}
                  onValueChange={(value) =>
                    onFiltersChange({ ...filters, perpetrator: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {filterOptions.perpetrator?.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div>
                          <div className="font-medium">{option.label}</div>
                          {option.category && (
                            <div className="text-xs text-gray-500">{option.category}</div>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
