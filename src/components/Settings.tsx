import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Edit, Trash2, Power, PowerOff, Eye, Download, Calendar, User, MessageSquare } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

type WebsiteLink = {
  website_id: number;
  name: string;
  url: string;
  date_added: string;
  sublinks?: string | null; // API returns as JSON string or null
  status?: string;
  articles_count?: number;
  processed_count?: number;
};

type WebsiteFormData = {
  name: string;
  url: string;
  sublinks: { id: string; key: string; value: string }[]; // Array with stable IDs
};

type ArticleData = {
  id: number;
  url: string;
  title: string;
  url_hash: string;
  title_hash: string;
  content_hash: string;
  excerpt?: string | null;
  full_content: string;
  content_length: number;
  word_count: number;
  reading_time: string;
  author: string;
  published_date?: string | null;
  category: string;
  tags: string[];
  featured_image: string;
  description: string;
  source_domain: string;
  source_url: string;
  source_page_type: string;
  scraped_at: string;
  scraping_method: string;
  extraction_method: string;
  best_selector: string;
  scraping_success: number;
  scraping_error?: string | null;
  cache_used: number;
  ai_confidence: string;
  is_duplicate: number;
  duplicate_of?: string | null;
  is_active: number;
  quality_score: string;
  created_at: string;
  updated_at: string;
  processed: string;
};

type ProcessedPostData = {
  id: number;
  original_language: string;
  english_translation?: string | null;
  post_summary: string;
  raw_content: string;
  source_url: string;
  timestamp_provided: string;
  platform: string;
  primary_category: string;
  civil_rights_restriction?: string | null;
  electoral_misconduct?: string | null;
  discrimination_type?: string | null;
  law_enforcement_misconduct?: string | null;
  physical_integrity_violation?: string | null;
  gender_based_violence?: string | null;
  political_attacks?: string | null;
  pwa_attacks?: string | null;
  protest_type?: string | null;
  perpetrator: string;
  victims_profile: string;
  gender_of_victim: string;
  district: string | null;
  incident_year: number;
  incident_month: number;
  incident_day: number;
  date_of_incident: string;
  details: string;
  sentiment: string;
  severity_level: string;
  election_relevance: string;
  total_incidents: number;
  main_categories_identified: string[];
  districts_affected: string[];
  time_period: string;
  overall_sentiment: string;
  key_themes: string[];
  sources_mentioned: string[];
  language_detected: string;
  full_analysis_json: any;
  created_at: string;
  updated_at: string;
};

type ArticlesResponse = {
  success: boolean;
  message: string;
  data: {
    articles: ArticleData[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  };  timestamp: string;
};

type ProcessedPostsResponse = {
  success: boolean;
  message: string;
  data: {
    posts: ProcessedPostData[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  };
  timestamp: string;
};

type ApiResponse = {
  success: boolean;
  message: string;
  data: {
    links: WebsiteLink[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  };
  timestamp: string;
};

type FacebookPage = {
  page_id: number;
  page_username: string;
  date_added: string;
  added_by: number;
  posts_count: number;
  election_count: number;
};

type FacebookPageFormData = {
  page_username: string;
};

type FacebookPagesResponse = {
  success: boolean;
  message: string;
  data: {
    pages: FacebookPage[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  };
  timestamp: string;
};

type FacebookPageResponse = {
  success: boolean;
  data: FacebookPage;
  message?: string;
};

type TwitterPage = {
  twitter_page_id: number;
  page_username: string;
  date_added: string;
  posts_count: number;
  election_count: number;
};

type TwitterPageFormData = {
  page_username: string;
};

type TwitterPagesResponse = {
  success: boolean;
  message: string;
  data: {
    pages: TwitterPage[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  };
  timestamp: string;
};

type TwitterPageResponse = {
  success: boolean;
  message: string;
  data: TwitterPage;
  timestamp: string;
};

type FacebookPostData = {
  id: number;
  post_id: string;
  author_name: string;
  author_username: string;
  content: string;
  created_at: string;
  likes_count: number;
  comments_count: number;
  shares_count: number;
  media_urls: string;
  urls: string;
  hashtags: string;
  mentions: string;
  post_url: string;
  post_type: string;
  sentiment?: string | null;
  scraped_at: string;
  raw_json: string;
  processed: string;
};

type FacebookComment = {
  author: string;
  text: string;
  timestamp: string;
  likes: string;
  postUrl: string;
};

type FacebookPostsResponse = {
  success: boolean;
  message: string;
  data: {
    posts: FacebookPostData[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
    page: {
      page_id: number;
      page_username: string;
      date_added: string;
      added_by: number;
      posts_count: number;      election_count: number;
    };
  };
  timestamp: string;
};

type FacebookProcessedPost = {
  id: number;
  original_language: string;
  english_translation: string;
  post_summary: string;
  raw_content: string;
  source_url: string;
  timestamp_provided: string;
  platform: string;
  primary_category: string;
  civil_rights_restriction: string | null;
  electoral_misconduct: string | null;
  discrimination_type: string | null;
  law_enforcement_misconduct: string | null;
  physical_integrity_violation: string | null;
  gender_based_violence: string | null;
  political_attacks: string | null;
  pwa_attacks: string | null;
  protest_type: string | null;
  perpetrator: string;
  victims_profile: string;
  gender_of_victim: string;
  district: string;
  incident_year: number;
  incident_month: number;
  incident_day: number;
  date_of_incident: string;
  details: string;
  sentiment: string;
  severity_level: string;
  election_relevance: string;
  total_incidents: number;
  main_categories_identified: string[];
  districts_affected: string[];
  time_period: string;
  overall_sentiment: string;
  key_themes: string[];
  sources_mentioned: string[];
  language_detected: string;
  full_analysis_json: any;
  created_at: string;
  updated_at: string;
};

type FacebookProcessedPostsResponse = {
  success: boolean;
  message: string;
  data: {
    posts: FacebookProcessedPost[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
    page: {
      page_id: number;
      page_username: string;
      date_added: string;
      added_by: number;
      posts_count: number;
      election_count: number;
    };
  };  timestamp: string;
};

// Manual Story Types
type ManualStory = {
  id: number;
  content: string;
  date: string;
  source: 'whatsapp' | 'radio' | 'tv' | 'eyewitness' | 'other';
  author: string;
  created_at: string;
  updated_at: string;
};

type ManualStoryFormData = {
  content: string;
  date: string;
  source: 'whatsapp' | 'radio' | 'tv' | 'eyewitness' | 'other';
  author: string;
};

export const Settings = () => {
  const [activeTab, setActiveTab] = useState('websites');
  const [websiteLinks, setWebsiteLinks] = useState<WebsiteLink[]>([]);  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'add' | 'edit'>('add');
  const [editingWebsite, setEditingWebsite] = useState<WebsiteLink | null>(null);
    // Scraping modal state
  const [isScrapeModalOpen, setIsScrapeModalOpen] = useState(false);
  const [scrapingWebsite, setScrapingWebsite] = useState<WebsiteLink | null>(null);
  const [scrapingUrls, setScrapingUrls] = useState<string[]>([]);
  const [scrapingStates, setScrapingStates] = useState<Record<string, 'idle' | 'scraping' | 'success' | 'error'>>({});
  const [maxArticlesToFetch, setMaxArticlesToFetch] = useState(100);
  const [fetchFullContent, setFetchFullContent] = useState(true);
    const [isViewArticlesModalOpen, setIsViewArticlesModalOpen] = useState(false);
  const [viewingWebsite, setViewingWebsite] = useState<WebsiteLink | null>(null);
  // Facebook Posts Modal state
  const [isViewFacebookPostsModalOpen, setIsViewFacebookPostsModalOpen] = useState(false);
  const [viewingFacebookPage, setViewingFacebookPage] = useState<FacebookPage | null>(null);
  const [facebookPosts, setFacebookPosts] = useState<FacebookPostData[]>([]);
  const [facebookPostsLoading, setFacebookPostsLoading] = useState(false);
  const [expandedComments, setExpandedComments] = useState<Set<number>>(new Set());
  const [facebookProcessedPosts, setFacebookProcessedPosts] = useState<FacebookProcessedPost[]>([]);
  const [facebookProcessedPostsLoading, setFacebookProcessedPostsLoading] = useState(false);
  const [facebookModalActiveTab, setFacebookModalActiveTab] = useState('posts'); // For Facebook modal tabs
  
  const [articles, setArticles] = useState<ArticleData[]>([]);  const [articlesLoading, setArticlesLoading] = useState(false);
  const [processedPosts, setProcessedPosts] = useState<ProcessedPostData[]>([]);
  const [processedPostsLoading, setProcessedPostsLoading] = useState(false);
  const [modalActiveTab, setModalActiveTab] = useState('articles'); // For modal tabs
  
  const [websiteForm, setWebsiteForm] = useState<WebsiteFormData>({
    name: '', 
    url: '', 
    sublinks: [] 
  });

  // Facebook Pages state
  const [facebookPages, setFacebookPages] = useState<FacebookPage[]>([]);
  const [facebookLoading, setFacebookLoading] = useState(false);
  const [isFacebookModalOpen, setIsFacebookModalOpen] = useState(false);
  const [facebookModalMode, setFacebookModalMode] = useState<'add' | 'edit'>('add');
  const [editingFacebookPage, setEditingFacebookPage] = useState<FacebookPage | null>(null);
  const [facebookForm, setFacebookForm] = useState<FacebookPageFormData>({
    page_username: ''
  });
  const [facebookSearchTerm, setFacebookSearchTerm] = useState('');
  const [facebookStats, setFacebookStats] = useState<{
    totalPages: number;
    recentlyAdded: number;
  }>({ totalPages: 0, recentlyAdded: 0 });

  // Twitter Pages state
  const [twitterPages, setTwitterPages] = useState<TwitterPage[]>([]);
  const [twitterLoading, setTwitterLoading] = useState(false);
  const [isTwitterModalOpen, setIsTwitterModalOpen] = useState(false);
  const [twitterModalMode, setTwitterModalMode] = useState<'add' | 'edit'>('add');
  const [editingTwitterPage, setEditingTwitterPage] = useState<TwitterPage | null>(null);
  const [twitterForm, setTwitterForm] = useState<TwitterPageFormData>({
    page_username: ''
  });
  const [twitterSearchTerm, setTwitterSearchTerm] = useState('');
  const [twitterStats, setTwitterStats] = useState<{
    totalPages: number;
    recentlyAdded: number;
    topUsernamePatterns: string[];
  }>({ totalPages: 0, recentlyAdded: 0, topUsernamePatterns: [] });
  const [submitting, setSubmitting] = useState(false);
  const [sublinkKeys, setSublinkKeys] = useState<string[]>([]); // Track keys separately
  const { toast } = useToast();

  // Manual Story state
  const [manualStories, setManualStories] = useState<ManualStory[]>([]);
  const [manualStoryForm, setManualStoryForm] = useState<ManualStoryFormData>({
    content: '',
    date: new Date().toISOString().split('T')[0],
    source: 'whatsapp',
    author: ''
  });
  const [isManualStorySubmitting, setIsManualStorySubmitting] = useState(false);

  // Helper function to parse sublinks from API response
  const parseSublinks = (sublinksString: string | null): { [key: string]: string } => {
    if (!sublinksString) return {};
    try {
      return JSON.parse(sublinksString);
    } catch (error) {
      console.error('Error parsing sublinks:', error);
      return {};
    }
  };

  // Helper function to format sublinks for display
  const getFormattedSublinks = (website: WebsiteLink): { [key: string]: string } => {
    return parseSublinks(website.sublinks);
  };

  // Convert object to array format for form
  const objectToArray = (sublinksObj: { [key: string]: string }): { id: string; key: string; value: string }[] => {
    return Object.entries(sublinksObj).map(([key, value], index) => ({
      id: `sublink-${Date.now()}-${index}`,
      key,
      value
    }));
  };

  // Convert array format back to object for API
  const arrayToObject = (sublinksArray: { id: string; key: string; value: string }[]): { [key: string]: string } => {
    return sublinksArray.reduce((acc, item) => {
      if (item.key.trim()) {
        acc[item.key.trim()] = item.value;
      }
      return acc;
    }, {} as { [key: string]: string });  };

  // Auto-fetch website links when component mounts
  useEffect(() => {
    fetchWebsiteLinks();
  }, []);

  const fetchWebsiteLinks = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:3200/api/website-links');
      const data: ApiResponse = await response.json();
      if (data.success) {
        setWebsiteLinks(data.data.links);
      }
    } catch (error) {
      console.error('Error fetching website links:', error);
      toast({
        title: "Error",
        description: "Failed to fetch website links",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };
  const addWebsiteLink = async () => {
    if (!websiteForm.name.trim() || !websiteForm.url.trim()) {
      toast({
        title: "Validation Error",
        description: "Please fill in both name and URL fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setSubmitting(true);      const payload = {
        name: websiteForm.name.trim(),
        url: websiteForm.url.trim(),
        sublinks: arrayToObject(websiteForm.sublinks)
      };

      const response = await fetch('http://localhost:3200/api/website-links', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        toast({
          title: "Success",
          description: "Website link added successfully",
        });
        resetForm();
        setIsModalOpen(false);
        fetchWebsiteLinks();
      } else {
        throw new Error(data.message || 'Failed to add website link');
      }
    } catch (error) {
      console.error('Error adding website link:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add website link",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const updateWebsiteLink = async () => {
    if (!editingWebsite || !websiteForm.name.trim() || !websiteForm.url.trim()) {
      toast({
        title: "Validation Error",
        description: "Please fill in both name and URL fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setSubmitting(true);      const payload = {
        name: websiteForm.name.trim(),
        url: websiteForm.url.trim(),
        sublinks: arrayToObject(websiteForm.sublinks)
      };

      const response = await fetch(`http://localhost:3200/api/website-links/${editingWebsite.website_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        toast({
          title: "Success",
          description: "Website link updated successfully",
        });
        resetForm();
        setIsModalOpen(false);
        fetchWebsiteLinks();
      } else {
        throw new Error(data.message || 'Failed to update website link');
      }
    } catch (error) {
      console.error('Error updating website link:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update website link",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };  const resetForm = () => {
    setWebsiteForm({ name: '', url: '', sublinks: [] });
    setSublinkKeys([]);
    setEditingWebsite(null);
  };  const addSublink = () => {
    const newId = `sublink-${Date.now()}`;
    setWebsiteForm(prev => ({
      ...prev,
      sublinks: [...prev.sublinks, { id: newId, key: '', value: '' }]
    }));
  };  const removeSublink = (idToRemove: string) => {
    setWebsiteForm(prev => ({
      ...prev,
      sublinks: prev.sublinks.filter(item => item.id !== idToRemove)
    }));
  };
  const updateSublink = (id: string, field: 'key' | 'value', newValue: string) => {
    setWebsiteForm(prev => ({
      ...prev,
      sublinks: prev.sublinks.map(item => 
        item.id === id ? { ...item, [field]: newValue } : item
      )
    }));
  };  const openAddModal = () => {
    setModalMode('add');
    resetForm();
    setIsModalOpen(true);
  };  const openEditModal = (website: WebsiteLink) => {
    setModalMode('edit');
    setEditingWebsite(website);
    const parsedSublinks = parseSublinks(website.sublinks);
    setWebsiteForm({
      name: website.name,
      url: website.url,
      sublinks: objectToArray(parsedSublinks)
    });
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    resetForm();
  };
  const handleSubmit = () => {
    if (modalMode === 'add') {
      addWebsiteLink();
    } else {
      updateWebsiteLink();
    }
  };

  const activateWebsite = async (websiteId: number) => {
    try {
      setSubmitting(true);
      const response = await fetch(`http://localhost:3200/api/website-links/${websiteId}/activate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        toast({
          title: "Success",
          description: "Website activated successfully",
        });
        fetchWebsiteLinks();
      } else {
        throw new Error(data.message || 'Failed to activate website');
      }
    } catch (error) {
      console.error('Error activating website:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to activate website",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const deactivateWebsite = async (websiteId: number) => {
    try {
      setSubmitting(true);
      const response = await fetch(`http://localhost:3200/api/website-links/${websiteId}/deactivate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        toast({
          title: "Success",
          description: "Website deactivated successfully",
        });
        fetchWebsiteLinks();
      } else {
        throw new Error(data.message || 'Failed to deactivate website');
      }
    } catch (error) {
      console.error('Error deactivating website:', error);      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to deactivate website",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const fetchArticlesForWebsite = async (websiteId: number) => {
    try {
      setArticlesLoading(true);
      const response = await fetch(`http://localhost:3200/api/website-links/${websiteId}/articles`);
      const data: ArticlesResponse = await response.json();
      
      if (response.ok && data.success) {
        setArticles(data.data.articles);
      } else {
        throw new Error(data.message || 'Failed to fetch articles');
      }
    } catch (error) {
      console.error('Error fetching articles:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch articles",
        variant: "destructive",
      });
      setArticles([]);    } finally {
      setArticlesLoading(false);
    }
  };

  const fetchProcessedPostsForWebsite = async (websiteId: number) => {
    try {
      setProcessedPostsLoading(true);
      const response = await fetch(`http://localhost:3200/api/website-links/${websiteId}/processed`);
      const data: ProcessedPostsResponse = await response.json();
      
      if (response.ok && data.success) {
        setProcessedPosts(data.data.posts);
      } else {
        throw new Error(data.message || 'Failed to fetch processed posts');
      }
    } catch (error) {
      console.error('Error fetching processed posts:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch processed posts",
        variant: "destructive",
      });
      setProcessedPosts([]);
    } finally {
      setProcessedPostsLoading(false);
    }
  };  const openViewArticlesModal = (website: WebsiteLink) => {
    setViewingWebsite(website);
    setIsViewArticlesModalOpen(true);
    setModalActiveTab('articles'); // Reset to articles tab
    fetchArticlesForWebsite(website.website_id);
    fetchProcessedPostsForWebsite(website.website_id);
  };

  // Facebook Posts Functions
  const fetchFacebookPostsForPage = async (pageId: number) => {
    try {
      setFacebookPostsLoading(true);
      const response = await fetch(`http://localhost:3200/api/facebook-pages/${pageId}/posts`);
      const data: FacebookPostsResponse = await response.json();
      
      if (response.ok && data.success) {
        setFacebookPosts(data.data.posts);
      } else {
        throw new Error(data.message || 'Failed to fetch Facebook posts');
      }
    } catch (error) {
      console.error('Error fetching Facebook posts:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch Facebook posts",
        variant: "destructive",
      });
      setFacebookPosts([]);    } finally {
      setFacebookPostsLoading(false);
    }
  };

  const fetchFacebookProcessedPostsForPage = async (pageId: number) => {
    try {
      setFacebookProcessedPostsLoading(true);
      const response = await fetch(`http://localhost:3200/api/facebook-pages/${pageId}/processed`);
      const data: FacebookProcessedPostsResponse = await response.json();
      
      if (response.ok && data.success) {
        setFacebookProcessedPosts(data.data.posts);
      } else {
        throw new Error(data.message || 'Failed to fetch Facebook processed posts');
      }
    } catch (error) {
      console.error('Error fetching Facebook processed posts:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch Facebook processed posts",
        variant: "destructive",
      });
      setFacebookProcessedPosts([]);
    } finally {
      setFacebookProcessedPostsLoading(false);
    }
  };

  const openViewFacebookPostsModal = (page: FacebookPage) => {
    setViewingFacebookPage(page);
    setIsViewFacebookPostsModalOpen(true);
    setExpandedComments(new Set()); // Reset expanded comments
    setFacebookModalActiveTab('posts'); // Reset to posts tab
    fetchFacebookPostsForPage(page.page_id);
    fetchFacebookProcessedPostsForPage(page.page_id);
  };

  const parseComments = (rawJson: string): FacebookComment[] => {
    try {
      const parsed = JSON.parse(rawJson);
      return parsed.comments || [];
    } catch (error) {
      console.error('Error parsing comments:', error);
      return [];
    }
  };
  const toggleCommentsForPost = (postId: number) => {
    setExpandedComments(prev => {
      const newSet = new Set(prev);
      if (newSet.has(postId)) {
        newSet.delete(postId);
      } else {
        newSet.add(postId);
      }
      return newSet;
    });
  };
  // Scraping Functions
  const openScrapeModal = (website: WebsiteLink) => {
    setScrapingWebsite(website);
    // Prepare all URLs (main URL + sublinks)
    const urls = [website.url];
    
    // Parse sublinks using the same helper function as the main table
    const formattedSublinks = getFormattedSublinks(website);
    Object.values(formattedSublinks).forEach(sublinkUrl => {
      if (sublinkUrl.trim()) {
        urls.push(sublinkUrl.trim());
      }
    });
    
    setScrapingUrls(urls);
    
    // Initialize scraping states
    const initialStates: Record<string, 'idle' | 'scraping' | 'success' | 'error'> = {};
    urls.forEach(url => {
      initialStates[url] = 'idle';
    });
    setScrapingStates(initialStates);
    
    setIsScrapeModalOpen(true);
  };
  const scrapeUrl = async (url: string) => {
    try {
      setScrapingStates(prev => ({ ...prev, [url]: 'scraping' }));

      const payload = {
        url: url,
        fetchFullContent: fetchFullContent,
        maxArticlesToFetch: maxArticlesToFetch,
        saveToDatabase: true
      };

      // Create AbortController for timeout handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 300000); // 5 minutes timeout

      const response = await fetch('http://localhost:3200/api/ai/deep-scrape', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      const data = await response.json();

      if (response.ok && data.success) {
        setScrapingStates(prev => ({ ...prev, [url]: 'success' }));
        toast({ 
          title: "Success", 
          description: `Successfully scraped ${url}. Found ${data.articlesProcessed || 'unknown'} articles.` 
        });
      } else {
        throw new Error(data.message || 'Scraping failed');
      }
    } catch (error) {
      setScrapingStates(prev => ({ ...prev, [url]: 'error' }));
      
      if (error.name === 'AbortError') {
        toast({
          title: "Timeout",
          description: `Scraping ${url} timed out after 5 minutes. The process may continue in the background.`,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : `Failed to scrape ${url}`,
          variant: "destructive",
        });
      }
    }
  };

  // Facebook Pages Functions
  const fetchFacebookPages = async (searchTerm: string = '') => {
    try {
      setFacebookLoading(true);
      const url = searchTerm 
        ? `http://localhost:3200/api/facebook-pages/search?q=${encodeURIComponent(searchTerm)}&limit=50`
        : 'http://localhost:3200/api/facebook-pages?limit=50&offset=0';
      
      const response = await fetch(url);
      const data: FacebookPagesResponse = await response.json();
      
      if (response.ok && data.success) {
        setFacebookPages(data.data.pages);
      } else {
        throw new Error('Failed to fetch Facebook pages');
      }
    } catch (error) {
      console.error('Error fetching Facebook pages:', error);
      toast({
        title: "Error",
        description: "Failed to fetch Facebook pages",
        variant: "destructive",
      });
    } finally {
      setFacebookLoading(false);
    }
  };

  const fetchFacebookStats = async () => {
    try {
      const response = await fetch('http://localhost:3200/api/facebook-pages/stats');
      const data = await response.json();
      
      if (response.ok && data.success) {
        setFacebookStats(data.data);
      }
    } catch (error) {
      console.error('Error fetching Facebook stats:', error);
    }
  };
  const addFacebookPage = async () => {
    const validation = validateFacebookUsername(facebookForm.page_username);
    if (!validation.isValid) {
      toast({
        title: "Validation Error",
        description: validation.message,
        variant: "destructive",
      });
      return;
    }

    try {
      setSubmitting(true);
      const payload = {
        page_username: facebookForm.page_username.trim(),
        added_by: 1 // TODO: Get from auth context
      };

      const response = await fetch('http://localhost:3200/api/facebook-pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data: FacebookPageResponse = await response.json();

      if (response.ok && data.success) {
        toast({
          title: "Success",
          description: "Facebook page added successfully",
        });
        setIsFacebookModalOpen(false);
        setFacebookForm({ page_username: '' });
        fetchFacebookPages();
        fetchFacebookStats();
      } else {
        throw new Error(data.message || 'Failed to add Facebook page');
      }
    } catch (error) {
      console.error('Error adding Facebook page:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add Facebook page",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };
  const updateFacebookPage = async () => {
    if (!editingFacebookPage) {
      return;
    }
    
    const validation = validateFacebookUsername(facebookForm.page_username);
    if (!validation.isValid) {
      toast({
        title: "Validation Error",
        description: validation.message,
        variant: "destructive",
      });
      return;
    }

    try {
      setSubmitting(true);
      const payload = {
        page_username: facebookForm.page_username.trim()
      };

      const response = await fetch(`http://localhost:3200/api/facebook-pages/${editingFacebookPage.page_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data: FacebookPageResponse = await response.json();

      if (response.ok && data.success) {
        toast({
          title: "Success",
          description: "Facebook page updated successfully",
        });
        setIsFacebookModalOpen(false);
        setFacebookForm({ page_username: '' });
        setEditingFacebookPage(null);
        fetchFacebookPages();
      } else {
        throw new Error(data.message || 'Failed to update Facebook page');
      }
    } catch (error) {
      console.error('Error updating Facebook page:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update Facebook page",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const deleteFacebookPage = async (pageId: number) => {
    if (!confirm('Are you sure you want to delete this Facebook page?')) {
      return;
    }

    try {
      setSubmitting(true);
      const response = await fetch(`http://localhost:3200/api/facebook-pages/${pageId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (response.ok && data.success) {
        toast({
          title: "Success",
          description: "Facebook page deleted successfully",
        });
        fetchFacebookPages();
        fetchFacebookStats();
      } else {
        throw new Error(data.message || 'Failed to delete Facebook page');
      }
    } catch (error) {
      console.error('Error deleting Facebook page:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete Facebook page",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const openFacebookAddModal = () => {
    setFacebookModalMode('add');
    setFacebookForm({ page_username: '' });
    setEditingFacebookPage(null);
    setIsFacebookModalOpen(true);
  };

  const openFacebookEditModal = (page: FacebookPage) => {
    setFacebookModalMode('edit');
    setFacebookForm({ page_username: page.page_username });
    setEditingFacebookPage(page);
    setIsFacebookModalOpen(true);
  };

  const handleFacebookSearch = (searchTerm: string) => {
    setFacebookSearchTerm(searchTerm);
    if (searchTerm.length >= 2) {
      fetchFacebookPages(searchTerm);
    } else if (searchTerm.length === 0) {
      fetchFacebookPages();
    }
  };

  // Auto-fetch Facebook pages when switching to Facebook tab
  useEffect(() => {
    if (activeTab === 'facebook') {
      fetchFacebookPages();
      fetchFacebookStats();
    }
  }, [activeTab]);
  // Twitter Pages Functions
  const fetchTwitterPages = async (searchTerm: string = '') => {
    try {
      setTwitterLoading(true);
      const url = searchTerm 
        ? `http://localhost:3200/api/twitter-pages/search?q=${encodeURIComponent(searchTerm)}&limit=100`
        : 'http://localhost:3200/api/twitter-pages?limit=100&offset=0';
        console.log('Fetching Twitter pages from:', url);
      const response = await fetch(url);
      const data = await response.json();
      
      console.log('Twitter API Response:', data);
      console.log('Response OK:', response.ok);
      console.log('Data success:', data.success);
      console.log('Data structure:', Object.keys(data));
      
      if (response.ok && data.success) {
        // Extract pages from the correct nested structure
        const pages = data.data && data.data.pages ? data.data.pages : [];
        console.log('Extracted pages:', pages);
        console.log('Pages length:', pages.length);
        console.log('Total pages count:', data.data?.pagination?.total || 0);
        setTwitterPages(pages);
      } else {
        console.error('Twitter API Error:', data);
        console.error('Response status:', response.status);
        console.error('Response statusText:', response.statusText);
        throw new Error(data.message || 'Failed to fetch Twitter pages');
      }
    } catch (error) {
      console.error('Error fetching Twitter pages:', error);
      toast({
        title: "Error",
        description: "Failed to fetch Twitter pages",
        variant: "destructive",
      });
    } finally {
      setTwitterLoading(false);
    }
  };

  const fetchTwitterStats = async () => {
    try {
      const response = await fetch('http://localhost:3200/api/twitter-pages/stats');
      const data = await response.json();
      
      if (response.ok && data.success) {
        setTwitterStats(data.data);
      }
    } catch (error) {
      console.error('Error fetching Twitter stats:', error);
    }
  };
  const addTwitterPage = async () => {
    const validation = validateTwitterUsername(twitterForm.page_username);
    if (!validation.isValid) {
      toast({
        title: "Validation Error",
        description: validation.message,
        variant: "destructive",
      });
      return;
    }

    try {
      setSubmitting(true);
      const payload = {
        page_username: twitterForm.page_username.trim(),
        added_by: 1 // TODO: Get from auth context
      };

      const response = await fetch('http://localhost:3200/api/twitter-pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data: TwitterPageResponse = await response.json();

      if (response.ok && data.success) {
        toast({
          title: "Success",
          description: "Twitter page added successfully",
        });
        setIsTwitterModalOpen(false);
        setTwitterForm({ page_username: '' });
        fetchTwitterPages();
        fetchTwitterStats();
      } else {
        throw new Error(data.message || 'Failed to add Twitter page');
      }
    } catch (error) {
      console.error('Error adding Twitter page:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add Twitter page",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };
  const updateTwitterPage = async () => {
    if (!editingTwitterPage) {
      return;
    }
    
    const validation = validateTwitterUsername(twitterForm.page_username);
    if (!validation.isValid) {
      toast({
        title: "Validation Error",
        description: validation.message,
        variant: "destructive",
      });
      return;
    }

    try {
      setSubmitting(true);
      const payload = {
        page_username: twitterForm.page_username.trim()
      };

      const response = await fetch(`http://localhost:3200/api/twitter-pages/${editingTwitterPage.twitter_page_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data: TwitterPageResponse = await response.json();

      if (response.ok && data.success) {
        toast({
          title: "Success",
          description: "Twitter page updated successfully",
        });
        setIsTwitterModalOpen(false);
        setTwitterForm({ page_username: '' });
        setEditingTwitterPage(null);
        fetchTwitterPages();
      } else {
        throw new Error(data.message || 'Failed to update Twitter page');
      }
    } catch (error) {
      console.error('Error updating Twitter page:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update Twitter page",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const deleteTwitterPage = async (pageId: number) => {
    if (!confirm('Are you sure you want to delete this Twitter page?')) {
      return;
    }

    try {
      setSubmitting(true);
      const response = await fetch(`http://localhost:3200/api/twitter-pages/${pageId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (response.ok && data.success) {
        toast({
          title: "Success",
          description: "Twitter page deleted successfully",
        });
        fetchTwitterPages();
        fetchTwitterStats();
      } else {
        throw new Error(data.message || 'Failed to delete Twitter page');
      }
    } catch (error) {
      console.error('Error deleting Twitter page:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete Twitter page",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const openTwitterAddModal = () => {
    setTwitterModalMode('add');
    setTwitterForm({ page_username: '' });
    setEditingTwitterPage(null);
    setIsTwitterModalOpen(true);
  };

  const openTwitterEditModal = (page: TwitterPage) => {
    setTwitterModalMode('edit');
    setTwitterForm({ page_username: page.page_username });
    setEditingTwitterPage(page);
    setIsTwitterModalOpen(true);
  };

  const handleTwitterSearch = (searchTerm: string) => {
    setTwitterSearchTerm(searchTerm);
    if (searchTerm.length >= 2) {
      fetchTwitterPages(searchTerm);
    } else if (searchTerm.length === 0) {
      fetchTwitterPages();
    }
  };

  // Auto-fetch Twitter pages when switching to Twitter tab
  useEffect(() => {
    if (activeTab === 'twitter') {
      fetchTwitterPages();
      fetchTwitterStats();
    }
  }, [activeTab]);

  // Facebook username validation
  const validateFacebookUsername = (username: string): { isValid: boolean; message?: string } => {
    if (!username.trim()) {
      return { isValid: false, message: "Username is required" };
    }
    
    const trimmed = username.trim();
    
    // Check for valid characters (alphanumeric, dots, periods)
    if (!/^[a-zA-Z0-9._-]+$/.test(trimmed)) {
      return { isValid: false, message: "Username can only contain letters, numbers, dots, and hyphens" };
    }
    
    // Check length (Facebook usernames are typically 5-50 characters)
    if (trimmed.length < 3 || trimmed.length > 50) {
      return { isValid: false, message: "Username must be between 3 and 50 characters" };
    }
    
    return { isValid: true };
  };

  // Twitter username validation
  const validateTwitterUsername = (username: string): { isValid: boolean; message?: string } => {
    if (!username.trim()) {
      return { isValid: false, message: "Username is required" };
    }
    
    const trimmed = username.trim();
    
    // Check for valid characters (alphanumeric, underscores)
    if (!/^[a-zA-Z0-9_]+$/.test(trimmed)) {
      return { isValid: false, message: "Username can only contain letters, numbers, and underscores" };
    }
    
    // Check length (Twitter usernames are typically 1-15 characters)
    if (trimmed.length < 1 || trimmed.length > 15) {
      return { isValid: false, message: "Username must be between 1 and 15 characters" };    }
      return { isValid: true };
  };

  // Manual Story Functions
  const handleSubmitManualStory = async () => {
    if (!manualStoryForm.content.trim() || !manualStoryForm.author.trim()) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    setIsManualStorySubmitting(true);
    try {
      // TODO: Replace with actual API call
      const newStory: ManualStory = {
        id: Date.now(),
        content: manualStoryForm.content,
        date: manualStoryForm.date,
        source: manualStoryForm.source,
        author: manualStoryForm.author,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      setManualStories(prev => [newStory, ...prev]);
      setManualStoryForm({
        content: '',
        date: new Date().toISOString().split('T')[0],
        source: 'whatsapp',
        author: ''
      });

      toast({
        title: "Success",
        description: "Manual story added successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add manual story. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsManualStorySubmitting(false);
    }
  };

  const deleteManualStory = (id: number) => {
    setManualStories(prev => prev.filter(story => story.id !== id));
    toast({
      title: "Success",
      description: "Manual story deleted successfully.",
    });
  };

  const getSourceBadgeColor = (source: string) => {
    switch (source) {
      case 'whatsapp': return 'bg-green-100 text-green-800';
      case 'radio': return 'bg-blue-100 text-blue-800';
      case 'tv': return 'bg-purple-100 text-purple-800';
      case 'eyewitness': return 'bg-orange-100 text-orange-800';
      case 'other': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSourceLabel = (source: string) => {
    switch (source) {
      case 'whatsapp': return 'WhatsApp';
      case 'radio': return 'Radio';
      case 'tv': return 'TV';
      case 'eyewitness': return 'Eyewitness';
      case 'other': return 'Other';
      default: return 'Unknown';
    }
  };

  // Manual Story Form Component
  const ManualStoryForm = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="story-content">Story Content *</Label>
        <Textarea
          id="story-content"
          placeholder="Enter the story content..."
          value={manualStoryForm.content}
          onChange={(e) => setManualStoryForm(prev => ({ ...prev, content: e.target.value }))}
          rows={4}
          className="min-h-[100px]"
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="story-date">Date of Post *</Label>
          <Input
            id="story-date"
            type="date"
            value={manualStoryForm.date}
            onChange={(e) => setManualStoryForm(prev => ({ ...prev, date: e.target.value }))}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="story-source">Source *</Label>
          <Select
            value={manualStoryForm.source}
            onValueChange={(value: 'whatsapp' | 'radio' | 'tv' | 'eyewitness' | 'other') =>
              setManualStoryForm(prev => ({ ...prev, source: value }))
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select source" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="whatsapp">WhatsApp</SelectItem>
              <SelectItem value="radio">Radio</SelectItem>
              <SelectItem value="tv">TV</SelectItem>
              <SelectItem value="eyewitness">Eyewitness</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="story-author">Author Name *</Label>
        <Input
          id="story-author"
          placeholder="Enter author name..."
          value={manualStoryForm.author}
          onChange={(e) => setManualStoryForm(prev => ({ ...prev, author: e.target.value }))}
        />
      </div>

      <DialogFooter>
        <Button
          onClick={handleSubmitManualStory}
          disabled={isManualStorySubmitting}
          className="w-full"
        >
          {isManualStorySubmitting ? 'Adding Story...' : 'Add Story'}
        </Button>
      </DialogFooter>
    </div>
  );

  // Manual Story List Component
  const ManualStoryList = () => (
    <div className="p-6">
      {manualStories.length === 0 ? (
        <div className="text-center py-12">
          <MessageSquare className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No manual stories yet</h3>
          <p className="text-gray-500 mb-4">Start adding stories from various sources like WhatsApp, radio, TV, or eyewitness reports.</p>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Manual Stories ({manualStories.length})</h3>
          </div>
          
          <div className="space-y-4">
            {manualStories.map((story) => (
              <div key={story.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <Badge className={getSourceBadgeColor(story.source)}>
                      {getSourceLabel(story.source)}
                    </Badge>
                    <span className="text-sm text-gray-500">
                      {new Date(story.date).toLocaleDateString()}
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteManualStory(story.id)}
                    className="text-red-600 hover:text-red-800 hover:bg-red-50"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
                
                <p className="text-gray-900 whitespace-pre-wrap">{story.content}</p>
                
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <div className="flex items-center gap-1">
                    <User className="w-4 h-4" />
                    <span>{story.author}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    <span>Added {new Date(story.created_at).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className="w-full">
      <Tabs defaultValue="websites" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="websites" onClick={() => setActiveTab('websites')}>
            Websites
          </TabsTrigger>
          <TabsTrigger value="facebook" onClick={() => setActiveTab('facebook')}>
            Facebook
          </TabsTrigger>
          <TabsTrigger value="twitter" onClick={() => setActiveTab('twitter')}>
            Twitter
          </TabsTrigger>
          <TabsTrigger value="tiktok" onClick={() => setActiveTab('tiktok')}>
            TikTok
          </TabsTrigger>          <TabsTrigger value="youtube" onClick={() => setActiveTab('youtube')}>
            Manual Source
          </TabsTrigger>
          <TabsTrigger value="hootsuite" onClick={() => setActiveTab('hootsuite')}>
            Hootsuite
          </TabsTrigger>
        </TabsList><TabsContent value="websites">
          <div className="mt-4">
            <div className="flex justify-between items-center mb-4">
              <Button 
                onClick={fetchWebsiteLinks}
                variant="outline"
                disabled={loading}
              >
                {loading ? 'Loading...' : 'Refresh Website Links'}
              </Button>
                <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                <DialogTrigger asChild>
                  <Button onClick={openAddModal}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add New Website
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>
                      {modalMode === 'add' ? 'Add New Website' : 'Edit Website'}
                    </DialogTitle>
                    <DialogDescription>
                      {modalMode === 'add' 
                        ? 'Add a new website link to monitor for social media content.'
                        : 'Update the website information and sublinks.'
                      }
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="name" className="text-right">
                        Name
                      </Label>
                      <Input
                        id="name"
                        value={websiteForm.name}
                        onChange={(e) => setWebsiteForm(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="e.g., Nyasa Times"
                        className="col-span-3"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="url" className="text-right">
                        URL
                      </Label>
                      <Input
                        id="url"
                        type="url"
                        value={websiteForm.url}
                        onChange={(e) => setWebsiteForm(prev => ({ ...prev, url: e.target.value }))}
                        placeholder="https://www.nyasatimes.com"
                        className="col-span-3"
                      />
                    </div>
                    
                    {/* Sublinks Section */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">Sublinks</Label>
                        <Button type="button" size="sm" onClick={addSublink}>
                          <Plus className="w-4 h-4 mr-1" />
                          Add Sublink
                        </Button>
                      </div>
                        {websiteForm.sublinks.map((item) => (
                        <div key={item.id} className="grid grid-cols-12 items-center gap-2">
                          <Input
                            placeholder="Key (e.g., politics)"
                            value={item.key}
                            onChange={(e) => updateSublink(item.id, 'key', e.target.value)}
                            className="col-span-3"
                          />
                          <Input
                            placeholder="URL"
                            value={item.value}
                            onChange={(e) => updateSublink(item.id, 'value', e.target.value)}
                            className="col-span-8"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeSublink(item.id)}
                            className="col-span-1"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}
                        {websiteForm.sublinks.length === 0 && (
                        <p className="text-sm text-muted-foreground">
                          No sublinks added yet. Click "Add Sublink" to add one.
                        </p>
                      )}
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={handleModalClose}>
                      Cancel
                    </Button>
                    <Button type="submit" onClick={handleSubmit} disabled={submitting}>
                      {submitting 
                        ? (modalMode === 'add' ? 'Adding...' : 'Updating...') 
                        : (modalMode === 'add' ? 'Add Website' : 'Update Website')
                      }
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>            </div>
            
            {loading ? (
              <div className="mt-8 text-center py-8">
                <div className="inline-flex items-center gap-2">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  <p className="text-muted-foreground">Loading websites...</p>
                </div>
              </div>
            ) : websiteLinks.length > 0 ? (
              <Table className="mt-4">
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>URL</TableHead>
                    <TableHead>Sublinks</TableHead>
                    <TableHead>Articles</TableHead>
                    <TableHead>Processed</TableHead>
                    <TableHead>Unprocessed</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Date Added</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>                <TableBody>
                  {websiteLinks.map((link) => (
                    <TableRow key={link.website_id}>
                      <TableCell>{link.website_id}</TableCell>
                      <TableCell>{link.name}</TableCell>
                      <TableCell>
                        <a href={link.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                          {link.url}
                        </a>
                      </TableCell>
                      <TableCell>
                        {(() => {
                          const sublinks = getFormattedSublinks(link);
                          return Object.keys(sublinks).length > 0 ? (
                            <div className="space-y-1">
                              {Object.entries(sublinks).map(([key, value]) => (
                                <div key={key} className="text-xs">
                                  <span className="font-medium">{key}:</span>{' '}
                                  <a href={value} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                                    {value}
                                  </a>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <span className="text-muted-foreground text-sm">No sublinks</span>
                          );
                        })()}
                      </TableCell>
                      <TableCell>{link.articles_count || 0}</TableCell>
                      <TableCell>{link.processed_count || 0}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          (link.articles_count || 0) - (link.processed_count || 0) > 0
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {(link.articles_count || 0) - (link.processed_count || 0)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          link.status === 'active' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {link.status || 'unknown'}
                        </span>
                      </TableCell>
                      <TableCell>{new Date(link.date_added).toLocaleDateString()}</TableCell>                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openViewArticlesModal(link)}
                            disabled={submitting}
                            title="View articles"
                            className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openScrapeModal(link)}
                            disabled={submitting}
                            title="Scrape website"
                            className="text-green-600 hover:text-green-700 hover:bg-green-50"
                          >
                            <Download className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditModal(link)}
                            disabled={submitting}
                            title="Edit website"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          {link.status === 'active' ? (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => deactivateWebsite(link.website_id)}
                              disabled={submitting}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              title="Deactivate website"
                            >
                              <PowerOff className="w-4 h-4" />
                            </Button>
                          ) : (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => activateWebsite(link.website_id)}
                              disabled={submitting}
                              className="text-green-600 hover:text-green-700 hover:bg-green-50"
                              title="Activate website"
                            >
                              <Power className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="mt-8 text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                <div className="text-gray-500">
                  <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 48 48">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M34 40h10v-4a6 6 0 00-10.712-3.714M34 40H14m20 0v-4a9.971 9.971 0 00-.712-3.714M14 40H4v-4a6 6 0 0110.712-3.714M14 40v-4a9.971 9.971 0 01.712-3.714M18 20a6 6 0 1112 0v3.5M16 17.5a4 4 0 118 0v3.5" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No websites added yet</h3>
                  <p className="text-gray-500 mb-4">Start by adding your first website to monitor</p>
                  <Button onClick={openAddModal} className="inline-flex items-center gap-2">
                    <Plus className="w-4 h-4" />
                    Add Your First Website
                  </Button>                </div>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="facebook">
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Facebook Pages</h2>              <Button 
                onClick={() => fetchFacebookPages()}
                variant="outline"
                disabled={facebookLoading}
              >
                {facebookLoading ? 'Loading...' : 'Refresh Facebook Pages'}
              </Button>
            </div>
            
            <div className="mb-4">
              <Button onClick={openFacebookAddModal} className="inline-flex items-center gap-2">
                <Plus className="w-4 h-4" />
                Add New Facebook Page
              </Button>
            </div>
            
            <div className="grid gap-4 sm:grid-cols-2">
              <Input
                placeholder="Search Facebook pages..."
                value={facebookSearchTerm}
                onChange={(e) => handleFacebookSearch(e.target.value)}
                className="sm:max-w-[400px]"
              />
              
              <Button
                onClick={() => fetchFacebookPages(facebookSearchTerm)}
                variant="outline"
                disabled={facebookLoading}
                className="whitespace-nowrap"
              >
                {facebookLoading ? 'Searching...' : 'Search'}
              </Button>
            </div>
            
            <div className="mt-4">
              {facebookLoading ? (
                <div className="text-center py-8">
                  <p>Loading Facebook pages...</p>
                </div>
              ) : facebookPages.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No Facebook pages found. Add a new page to get started.</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <p className="text-sm text-muted-foreground mb-4">
                    Showing {facebookPages.length} Facebook page{facebookPages.length !== 1 ? 's' : ''}
                  </p>                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Page Username</TableHead>
                        <TableHead>Posts</TableHead>
                        <TableHead>Processed</TableHead>
                        <TableHead>Unprocessed</TableHead>
                        <TableHead>Date Added</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {facebookPages.map((page) => (
                        <TableRow key={page.page_id}>
                          <TableCell>{page.page_id}</TableCell>
                          <TableCell>
                            <a 
                              href={`https://www.facebook.com/${page.page_username}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:underline"
                            >
                              {page.page_username}
                            </a>
                          </TableCell>
                          <TableCell>
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {page.posts_count}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              {page.election_count}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                              {page.posts_count - page.election_count}
                            </span>
                          </TableCell>
                          <TableCell>{new Date(page.date_added).toLocaleDateString()}</TableCell>                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openViewFacebookPostsModal(page)}
                                disabled={submitting}
                                title="View Facebook posts"
                                className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                              >
                                <Eye className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openFacebookEditModal(page)}
                                disabled={submitting}
                                title="Edit Facebook page"
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => deleteFacebookPage(page.page_id)}
                                disabled={submitting}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                title="Delete Facebook page"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </div>
            
            {/* Facebook Stats */}
            <div className="mt-8 p-4 bg-white rounded shadow">
              <h3 className="text-md font-medium mb-2">Facebook Pages Stats</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Total Pages Monitored</p>
                  <p className="text-lg font-semibold">{facebookStats.totalPages}</p>
                </div>
                <div>                  <p className="text-sm text-muted-foreground">Recently Added</p>
                  <p className="text-lg font-semibold">{facebookStats.recentlyAdded}</p>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="twitter">
          <div className="mt-4">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Twitter Pages</h2>
                <p className="text-gray-600 mt-1">Manage Twitter pages for social media monitoring</p>
              </div>
              <Button 
                onClick={() => fetchTwitterPages()}
                variant="outline"
                disabled={twitterLoading}
              >
                {twitterLoading ? 'Loading...' : 'Refresh'}
              </Button>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-white p-4 rounded-lg border shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Pages</p>
                    <p className="text-2xl font-bold text-blue-600">{twitterStats.totalPages}</p>
                  </div>
                  <div className="text-blue-500">🐦</div>
                </div>
              </div>
              <div className="bg-white p-4 rounded-lg border shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Recently Added</p>
                    <p className="text-2xl font-bold text-green-600">{twitterStats.recentlyAdded}</p>
                  </div>
                  <div className="text-green-500">📈</div>
                </div>
              </div>
              <div className="bg-white p-4 rounded-lg border shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active</p>
                    <p className="text-2xl font-bold text-purple-600">{twitterPages.length}</p>
                  </div>
                  <div className="text-purple-500">⚡</div>
                </div>
              </div>
            </div>            {/* Top Username Patterns */}
            {twitterStats.topUsernamePatterns.length > 0 && (
              <div className="bg-white p-4 rounded-lg border shadow-sm mb-6">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Popular Username Patterns</h3>
                <div className="flex flex-wrap gap-2">
                  {twitterStats.topUsernamePatterns.map((pattern, index) => (
                    <span key={index} className="px-2 py-1 text-xs rounded bg-blue-100 text-blue-800">
                      {pattern}
                    </span>
                  ))}
                </div>
              </div>
            )}
            
            <Input
              placeholder="Search Twitter pages..."
              value={twitterSearchTerm}
              onChange={(e) => handleTwitterSearch(e.target.value)}
              className="w-full"
            />
            <Button onClick={openTwitterAddModal} className="inline-flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Add Twitter Page
            </Button>

            {/* Loading State */}
            {twitterLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-gray-600">Loading Twitter pages...</span>
              </div>            ) : twitterPages.length > 0 ? (
              /* Twitter Pages Table */
              <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
                <div className="p-4 border-b">
                  <p className="text-sm text-gray-600">
                    Showing {twitterPages.length} Twitter page{twitterPages.length !== 1 ? 's' : ''}
                  </p>
                </div>                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Page Username</TableHead>
                      <TableHead>Posts</TableHead>
                      <TableHead>Processed</TableHead>
                      <TableHead>Unprocessed</TableHead>
                      <TableHead>Date Added</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {twitterPages.map((page) => (
                      <TableRow key={page.twitter_page_id}>
                        <TableCell>{page.twitter_page_id}</TableCell>
                        <TableCell>
                          <a 
                            href={`https://twitter.com/${page.page_username}`} 
                            target="_blank" 
                            rel="noopener noreferrer" 
                            className="text-blue-600 hover:underline"
                          >
                            @{page.page_username}
                          </a>
                        </TableCell>
                        <TableCell>
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {page.posts_count}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {page.election_count}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                            {page.posts_count - page.election_count}
                          </span>
                        </TableCell>
                        <TableCell>{new Date(page.date_added).toLocaleDateString()}</TableCell>                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                             // onClick={() => openViewTwitterPostsModal(page)}
                              disabled={submitting}
                              title="View Twitter posts"
                              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openTwitterEditModal(page)}
                              disabled={submitting}
                              title="Edit Twitter page"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => deleteTwitterPage(page.twitter_page_id)}
                              disabled={submitting}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              title="Delete Twitter page"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              /* Empty State */
              <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                <div className="text-gray-500">
                  <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 48 48">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M34 40h10v-4a6 6 0 00-10.712-3.714M34 40H14m20 0v-4a9.971 9.971 0 00-.712-3.714M14 40H4v-4a6 6 0 0110.712-3.714M14 40v-4a9.971 9.971 0 01.712-3.714M18 20a6 6 0 1112 0v3.5M16 17.5a4 4 0 118 0v3.5" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Twitter pages added yet</h3>
                  <p className="text-gray-500 mb-4">Start by adding your first Twitter page to monitor</p>
                  <Button onClick={openTwitterAddModal} className="inline-flex items-center gap-2">
                    <Plus className="w-4 h-4" />
                    Add Your First Twitter Page
                  </Button>
                </div>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="tiktok">
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <p>TikTok links will be displayed here.</p>
          </div>
        </TabsContent>        <TabsContent value="youtube">
          <div className="mt-4">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Manual Source</h2>
                <p className="text-gray-600 mt-1">Add stories manually from various sources like WhatsApp, Radio, TV, and eyewitness reports</p>
              </div>
              <Dialog>
                <DialogTrigger asChild>
                  <Button className="flex items-center gap-2">
                    <Plus className="w-4 h-4" />
                    Add Story
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Add Manual Story</DialogTitle>
                    <DialogDescription>
                      Add a story from manual sources like WhatsApp, radio, TV, or eyewitness reports
                    </DialogDescription>
                  </DialogHeader>
                  <ManualStoryForm />
                </DialogContent>
              </Dialog>
            </div>
            
            <div className="bg-white rounded-lg border shadow-sm">
              <ManualStoryList />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="hootsuite">
          <div className="mt-4">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Hootsuite Integration</h2>
                <p className="text-gray-600 mt-1">Manage Hootsuite data integration for social media monitoring</p>
              </div>
            </div>
            
            <div className="bg-white rounded-lg border shadow-sm p-6">
              <div className="text-center py-12">
                <div className="text-gray-500">
                  <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 48 48">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4a2 2 0 012-2h8a2 2 0 012 2v2m-6 12v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Hootsuite Integration</h3>
                  <p className="text-gray-500 mb-4">Hootsuite data integration will be configured here</p>
                  <p className="text-sm text-gray-400">Coming soon...</p>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>{/* Articles and Processed Posts View Modal */}
      <Dialog open={isViewArticlesModalOpen} onOpenChange={setIsViewArticlesModalOpen}>
        <DialogContent className="sm:max-w-[95vw] max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>
              Content for {viewingWebsite?.name}
            </DialogTitle>
            <DialogDescription>
              View articles and processed posts from {viewingWebsite?.url}
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex-1 overflow-hidden min-h-0">
            <Tabs value={modalActiveTab} onValueChange={setModalActiveTab} className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-2 mb-4">
                <TabsTrigger value="articles">Articles</TabsTrigger>
                <TabsTrigger value="processed-posts">Processed Posts</TabsTrigger>
              </TabsList>
              
              <TabsContent value="articles" className="flex-1 overflow-hidden min-h-0">
                <div className="h-full overflow-y-auto max-h-[60vh]">
                  {articlesLoading ? (
                    <div className="text-center py-8">
                      <p>Loading articles...</p>
                    </div>
                  ) : articles.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">No articles found for this website.</p>
                    </div>                  ) : (
                    <div className="overflow-x-auto">
                      <p className="text-sm text-muted-foreground mb-4">
                        Showing {articles.length} articles
                      </p>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>ID</TableHead>
                            <TableHead>Title</TableHead>
                            <TableHead>Author</TableHead>
                            <TableHead>Category</TableHead>
                            <TableHead>Reading Time</TableHead>
                            <TableHead>Published Date</TableHead>
                            <TableHead>Date Scraped</TableHead>
                            <TableHead>Processed</TableHead>
                            <TableHead>URL</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {articles.map((article) => (
                            <TableRow key={article.id}>
                              <TableCell>{article.id}</TableCell>
                              <TableCell className="max-w-xs">
                                <div className="truncate" title={article.title}>
                                  {article.title}
                                </div>
                                {article.excerpt && (
                                  <div className="text-xs text-muted-foreground mt-1 truncate" title={article.excerpt}>
                                    {article.excerpt}
                                  </div>
                                )}
                              </TableCell>
                              <TableCell>{article.author || 'Unknown'}</TableCell>
                              <TableCell>
                                <span className="px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                                  {article.category}
                                </span>
                              </TableCell>
                              <TableCell>{article.reading_time}</TableCell>
                              <TableCell>
                                {article.published_date ? new Date(article.published_date).toLocaleDateString() : 'N/A'}
                              </TableCell>
                              <TableCell>
                                {new Date(article.scraped_at).toLocaleDateString()}
                              </TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded-full text-xs ${
                                  article.processed === 'yes' 
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-yellow-100 text-yellow-800'
                                }`}>
                                  {article.processed === 'yes' ? 'Processed' : 'Pending'}
                                </span>
                              </TableCell>
                              <TableCell>
                                <a 
                                  href={article.url} 
                                  target="_blank" 
                                  rel="noopener noreferrer" 
                                  className="text-blue-600 hover:underline text-sm"
                                  title={article.url}
                                >
                                  View Article
                                </a>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </div>
              </TabsContent>
              
              <TabsContent value="processed-posts" className="flex-1 overflow-hidden min-h-0">
                <div className="h-full overflow-y-auto max-h-[60vh]">
                  {processedPostsLoading ? (
                    <div className="text-center py-8">
                      <p>Loading processed posts...</p>
                    </div>
                  ) : processedPosts.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">No processed posts found for this website.</p>
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <p className="text-sm text-muted-foreground mb-4">
                        Showing {processedPosts.length} processed post{processedPosts.length !== 1 ? 's' : ''}
                      </p>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>ID</TableHead>
                            <TableHead>Summary</TableHead>
                            <TableHead>Platform</TableHead>
                            <TableHead>Category</TableHead>
                            <TableHead>Severity</TableHead>
                            <TableHead>Electoral Misconduct</TableHead>
                            <TableHead>Political Attacks</TableHead>
                            <TableHead>Law Enforcement Misconduct</TableHead>
                            <TableHead>Perpetrator</TableHead>
                            <TableHead>Victims Profile</TableHead>
                            <TableHead>District</TableHead>
                            <TableHead>Date of Incident</TableHead>
                            <TableHead>Created At</TableHead>
                            <TableHead>Sentiment</TableHead>
                            <TableHead>Election Relevance</TableHead>
                            <TableHead>Key Themes</TableHead>
                            <TableHead>Language Detected</TableHead>
                            <TableHead>Source URL</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {processedPosts.map((post) => (
                            <TableRow key={post.id}>
                              <TableCell className="font-mono text-xs">{post.id}</TableCell>
                              <TableCell className="max-w-sm">
                                <div className="truncate" title={post.post_summary}>
                                  {post.post_summary}
                                </div>
                                <div className="text-xs text-muted-foreground mt-1">
                                  {post.time_period} • {post.total_incidents} incident{post.total_incidents !== 1 ? 's' : ''}
                                </div>
                              </TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded-full text-xs ${
                                  post.platform.toLowerCase() === 'website' 
                                    ? 'bg-purple-100 text-purple-800'
                                    : post.platform.toLowerCase() === 'facebook'
                                    ? 'bg-blue-100 text-blue-800'
                                    : post.platform.toLowerCase() === 'twitter'
                                    ? 'bg-sky-100 text-sky-800'
                                    : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {post.platform}
                                </span>
                              </TableCell>
                              <TableCell className="max-w-xs">
                                <div className="truncate" title={post.primary_category}>
                                  {post.primary_category}
                                </div>
                              </TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded-full text-xs ${
                                  post.severity_level === 'High'
                                    ? 'bg-red-100 text-red-800'
                                    : post.severity_level === 'Medium'
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : 'bg-green-100 text-green-800'
                                }`}>
                                  {post.severity_level}
                                </span>
                              </TableCell>
                              <TableCell>
                                {post.electoral_misconduct ? (
                                  <div className="truncate text-xs" title={post.electoral_misconduct}>
                                    {post.electoral_misconduct}
                                  </div>
                                ) : 'N/A'}
                              </TableCell>
                              <TableCell>
                                {post.political_attacks ? (
                                  <div className="truncate text-xs" title={post.political_attacks}>
                                    {post.political_attacks}
                                  </div>
                                ) : 'N/A'}
                              </TableCell>
                              <TableCell>
                                {post.law_enforcement_misconduct ? (
                                  <div className="truncate text-xs" title={post.law_enforcement_misconduct}>
                                    {post.law_enforcement_misconduct}
                                  </div>
                                ) : 'N/A'}
                              </TableCell>
                              <TableCell>
                                <div className="truncate text-xs" title={post.perpetrator}>
                                  {post.perpetrator || 'N/A'}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="truncate text-xs" title={post.victims_profile}>
                                  {post.victims_profile || 'N/A'}
                                </div>
                              </TableCell>
                              <TableCell>
                                {post.district ? (
                                  <span className="px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                                    {post.district}
                                  </span>
                                ) : 'N/A'}
                              </TableCell>
                              <TableCell className="text-xs">
                                {post.date_of_incident ? new Date(post.date_of_incident).toLocaleDateString() : 'N/A'}
                              </TableCell>
                              <TableCell className="text-xs">
                                {post.created_at ? new Date(post.created_at).toLocaleDateString() : 'N/A'}
                              </TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded-full text-xs ${
                                  post.sentiment === 'Positive' 
                                    ? 'bg-green-100 text-green-800'
                                    : post.sentiment === 'Negative'
                                    ? 'bg-red-100 text-red-800'
                                    : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {post.sentiment}
                                </span>
                              </TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded-full text-xs ${
                                  post.election_relevance === 'Direct'
                                    ? 'bg-orange-100 text-orange-800'
                                    : post.election_relevance === 'Indirect'
                                    ? 'bg-amber-100 text-amber-800'
                                    : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {post.election_relevance}
                                </span>
                              </TableCell>
                              <TableCell>
                                {post.key_themes && post.key_themes.length > 0 ? (
                                  <div className="flex flex-wrap gap-1 max-w-[150px]">
                                    {post.key_themes.slice(0, 2).map((theme, index) => (
                                      <span key={index} className="px-1 py-0.5 rounded text-xs bg-indigo-100 text-indigo-800 truncate">
                                        {theme}
                                      </span>
                                    ))}
                                    {post.key_themes.length > 2 && (
                                      <span className="text-xs text-muted-foreground">
                                        +{post.key_themes.length - 2}
                                      </span>
                                    )}
                                  </div>
                                ) : 'N/A'}
                              </TableCell>
                              <TableCell className="text-xs">
                                {post.language_detected || 'N/A'}
                              </TableCell>
                              <TableCell className="sticky right-0 bg-white z-10">
                                {post.source_url ? (
                                  <a 
                                    href={post.source_url} 
                                    target="_blank" 
                                    rel="noopener noreferrer" 
                                    className="inline-flex items-center px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
                                    title={post.source_url}
                                  >
                                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                    </svg>
                                    View
                                  </a>
                                ) : 'N/A'}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsViewArticlesModalOpen(false)}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Facebook Page Modal */}
      <Dialog open={isFacebookModalOpen} onOpenChange={setIsFacebookModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {facebookModalMode === 'add' ? 'Add Facebook Page' : 'Edit Facebook Page'}
            </DialogTitle>
            <DialogDescription>
              {facebookModalMode === 'add' 
                ? 'Add a new Facebook page to monitor for content analysis.' 
                : 'Update the Facebook page username.'}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="page_username" className="text-right">
                Username
              </Label>
              <Input
                id="page_username"
                placeholder="e.g., zuckerberg"
                value={facebookForm.page_username}
                onChange={(e) => setFacebookForm({ ...facebookForm, page_username: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <div className="text-right text-sm text-gray-500">
                Preview:
              </div>
              <div className="col-span-3 text-sm text-gray-700">
                {facebookForm.page_username ? (
                  <a 
                    href={`https://facebook.com/${facebookForm.page_username}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    facebook.com/{facebookForm.page_username}
                  </a>
                ) : (
                  'facebook.com/[username]'
                )}
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsFacebookModalOpen(false)}
              disabled={submitting}
            >
              Cancel
            </Button>
            <Button 
              onClick={facebookModalMode === 'add' ? addFacebookPage : updateFacebookPage}
              disabled={submitting || !facebookForm.page_username.trim()}
            >
              {submitting ? 'Processing...' : (facebookModalMode === 'add' ? 'Add Page' : 'Update Page')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>      {/* Twitter Page Modal */}
      <Dialog open={isTwitterModalOpen} onOpenChange={setIsTwitterModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {twitterModalMode === 'add' ? 'Add Twitter Page' : 'Edit Twitter Page'}
            </DialogTitle>
            <DialogDescription>
              {twitterModalMode === 'add' 
                ? 'Add a new Twitter page to monitor for content analysis. Enter just the username (e.g., "elonmusk" for twitter.com/elonmusk).' 
                : 'Update the Twitter page username.'}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="twitter_username" className="text-right">
                Username *
              </Label>
              <Input
                id="twitter_username"
                placeholder="e.g., elonmusk"
                value={twitterForm.page_username}
                onChange={(e) => setTwitterForm({ ...twitterForm, page_username: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <div className="text-right text-sm text-gray-500">
                Full URL:
              </div>
              <div className="col-span-3 text-sm text-gray-700">
                {twitterForm.page_username ? (
                  <a 
                    href={`https://twitter.com/${twitterForm.page_username}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    twitter.com/{twitterForm.page_username}
                  </a>
                ) : (
                  'twitter.com/[username]'
                )}
              </div>
            </div>
            {twitterModalMode === 'add' && (
              <div className="bg-blue-50 p-3 rounded-md">
                <p className="text-sm text-blue-800">
                  <strong>Tip:</strong> Enter only the username part of the Twitter URL. 
                  For example, if the page URL is "twitter.com/example", enter "example".
                </p>
              </div>
            )}
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsTwitterModalOpen(false)}
              disabled={submitting}
            >
              Cancel
            </Button>
            <Button 
              onClick={twitterModalMode === 'add' ? addTwitterPage : updateTwitterPage}
              disabled={submitting || !twitterForm.page_username.trim()}
            >
              {submitting ? 'Processing...' : (twitterModalMode === 'add' ? 'Add Page' : 'Update Page')}
            </Button>
          </DialogFooter>
        </DialogContent>      </Dialog>

      {/* Scraping Modal */}
      <Dialog open={isScrapeModalOpen} onOpenChange={setIsScrapeModalOpen}>        <DialogContent className="sm:max-w-[700px] max-h-[85vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>
              Scrape Website: {scrapingWebsite?.name}
            </DialogTitle>
            <DialogDescription>
              Configure scraping options and process each URL individually. Large scraping tasks may take several minutes.
            </DialogDescription>
          </DialogHeader>
          
          {/* Scraping Configuration */}
          <div className="border rounded-lg p-4 mb-4">
            <h4 className="font-medium mb-3">Scraping Configuration</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="maxArticles">Max Articles to Fetch</Label>
                <Input
                  id="maxArticles"
                  type="number"
                  min="1"
                  max="1000"
                  value={maxArticlesToFetch}
                  onChange={(e) => setMaxArticlesToFetch(parseInt(e.target.value) || 100)}
                  className="mt-1"
                />
                <p className="text-xs text-gray-500 mt-1">Recommended: 50-200 for faster processing</p>
              </div>
              <div className="flex items-center space-x-2 mt-6">
                <input
                  type="checkbox"
                  id="fetchFullContent"
                  checked={fetchFullContent}
                  onChange={(e) => setFetchFullContent(e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="fetchFullContent" className="text-sm">
                  Fetch full article content
                </Label>
              </div>
            </div>
          </div>
            <div className="flex-1 overflow-y-auto">
            <div className="space-y-4">
              {scrapingUrls.map((url, index) => {
                const state = scrapingStates[url] || 'idle';
                const isMainUrl = index === 0;
                
                // Find the sublink name (key) for this URL
                let sublinkName = '';
                if (!isMainUrl && scrapingWebsite) {
                  const formattedSublinks = getFormattedSublinks(scrapingWebsite);
                  const sublinkEntry = Object.entries(formattedSublinks).find(([key, value]) => value === url);
                  sublinkName = sublinkEntry ? sublinkEntry[0] : `Sublink ${index}`;
                }
                
                return (
                  <div key={url} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-sm font-medium text-gray-700">
                            {isMainUrl ? 'Main URL' : sublinkName}
                          </span>
                          {state === 'success' && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              ✓ Success
                            </span>
                          )}                          {state === 'error' && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              ✗ Error
                            </span>
                          )}
                          {state === 'scraping' && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              🔄 Processing...
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 break-all">{url}</p>
                        {state === 'scraping' && (
                          <p className="text-xs text-yellow-600 mt-1">
                            ⏳ This may take 1-5 minutes depending on content size...
                          </p>
                        )}
                      </div>
                      <Button
                        size="sm"
                        onClick={() => scrapeUrl(url)}
                        disabled={state === 'scraping'}                        className={`ml-4 ${
                          state === 'success' 
                            ? 'bg-green-600 hover:bg-green-700' 
                            : state === 'error'
                            ? 'bg-red-600 hover:bg-red-700'
                            : state === 'scraping'
                            ? 'bg-blue-600 cursor-not-allowed'
                            : ''
                        }`}
                      >
                        {state === 'scraping' ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Scraping...
                          </>
                        ) : state === 'success' ? (
                          'Scraped'
                        ) : state === 'error' ? (
                          'Retry'
                        ) : (
                          'Scrape'
                        )}
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
            <DialogFooter className="mt-4 flex justify-between">
            <div className="text-sm text-gray-500">
              Timeout: 5 minutes per URL
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setIsScrapeModalOpen(false)}
              >
                Close
              </Button>
              <Button
                onClick={async () => {
                  for (const url of scrapingUrls) {
                    if (scrapingStates[url] === 'idle' || scrapingStates[url] === 'error') {
                      await scrapeUrl(url);
                      // Small delay between requests
                      await new Promise(resolve => setTimeout(resolve, 2000));
                    }
                  }
                }}
                disabled={Object.values(scrapingStates).some(state => state === 'scraping')}
                className="bg-green-600 hover:bg-green-700"
              >
                Scrape All Remaining
              </Button>            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Facebook Posts Modal */}
      <Dialog open={isViewFacebookPostsModalOpen} onOpenChange={setIsViewFacebookPostsModalOpen}>
        <DialogContent className="sm:max-w-[95vw] max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>
              Facebook Posts for @{viewingFacebookPage?.page_username}
            </DialogTitle>
            <DialogDescription>
              View scraped Facebook posts and processed posts
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex-1 overflow-hidden min-h-0">
            <Tabs value={facebookModalActiveTab} onValueChange={setFacebookModalActiveTab} className="w-full h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="posts" className="flex items-center gap-2">
                  Posts
                  <Badge variant="secondary" className="ml-1">
                    {facebookPosts.length}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger value="processed" className="flex items-center gap-2">
                  Processed
                  <Badge variant="secondary" className="ml-1">
                    {facebookProcessedPosts.length}
                  </Badge>
                </TabsTrigger>
              </TabsList>
                <TabsContent value="posts" className="flex-1 overflow-hidden mt-4">
                {facebookPostsLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>                    <span className="ml-2 text-gray-600">Loading Facebook posts...</span>
                  </div>
                ) : facebookPosts.length > 0 ? (
                  <>
                    <style dangerouslySetInnerHTML={{
                      __html: `
                        .facebook-posts-scroll::-webkit-scrollbar {
                          height: 12px;
                          width: 12px;
                        }
                        .facebook-posts-scroll::-webkit-scrollbar-track {
                          background: #f3f4f6;
                          border-radius: 6px;
                        }
                        .facebook-posts-scroll::-webkit-scrollbar-thumb {
                          background: #9ca3af;
                          border-radius: 6px;
                        }
                        .facebook-posts-scroll::-webkit-scrollbar-thumb:hover {
                          background: #6b7280;
                        }
                        .facebook-posts-scroll {
                          scrollbar-width: thin;
                          scrollbar-color: #9ca3af #f3f4f6;
                        }
                      `
                    }} />
                    <div 
                      className="overflow-auto h-full facebook-posts-scroll" 
                      style={{ 
                        overflowX: 'scroll',
                        overflowY: 'auto'
                      }}
                    >
                      <Table className="min-w-[1200px]">
                      <TableHeader className="sticky top-0 bg-white z-10">
                        <TableRow>
                          <TableHead className="w-[80px]">ID</TableHead>
                          <TableHead className="w-[120px]">Author</TableHead>
                          <TableHead className="min-w-[300px]">Content</TableHead>
                          <TableHead className="w-[100px]">Likes</TableHead>
                          <TableHead className="w-[100px]">Shares</TableHead>
                          <TableHead className="w-[120px]">Date</TableHead>
                          <TableHead className="w-[100px]">Actions</TableHead>
                          <TableHead className="min-w-[400px]">Comments</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {facebookPosts.map((post) => {
                          const comments = parseComments(post.raw_json);
                          const isExpanded = expandedComments.has(post.id);
                          return (
                            <TableRow key={post.id}>
                              <TableCell className="font-mono text-xs">{post.id}</TableCell>
                              <TableCell>
                                <div className="text-sm">
                                  <div className="font-medium">{post.author_name}</div>
                                  <div className="text-gray-500">@{post.author_username}</div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="max-w-md">
                                  <p className="text-sm line-clamp-3">{post.content}</p>
                                  {post.media_urls && post.media_urls !== '[]' && (
                                    <span className="inline-block mt-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                                      📷 Has Media
                                    </span>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell>
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  👍 {post.likes_count}
                                </span>
                              </TableCell>
                              <TableCell>
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                  🔄 {post.shares_count}
                                </span>
                              </TableCell>
                              <TableCell className="text-xs">
                                {new Date(post.created_at).toLocaleDateString()}
                              </TableCell>
                              <TableCell>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => window.open(post.post_url, '_blank')}
                                  title="View original post"
                                  className="text-blue-600 hover:text-blue-700"
                                >
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                  </svg>
                                </Button>
                              </TableCell>
                              <TableCell>
                                <div className="max-w-md">
                                  {/* Comments Header with Toggle */}
                                  <div className="flex items-center justify-between mb-2">
                                    <button
                                      onClick={() => toggleCommentsForPost(post.id)}
                                      className="flex items-center space-x-1 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors"
                                      disabled={comments.length === 0}
                                    >
                                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        💬 {post.comments_count}
                                      </span>
                                      {comments.length > 0 && (
                                        <svg 
                                          className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                                          fill="none" 
                                          stroke="currentColor" 
                                          viewBox="0 0 24 24"
                                        >
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                        </svg>
                                      )}
                                    </button>
                                  </div>
                                  
                                  {/* Accordion Comments */}
                                  {isExpanded && comments.length > 0 && (
                                    <div className="max-h-60 overflow-y-auto border border-gray-200 rounded-md p-3 bg-gray-50">
                                      <div className="space-y-3">
                                        {comments.map((comment, index) => (
                                          <div key={index} className="border-l-2 border-blue-200 pl-3 py-2 bg-white rounded-r-md shadow-sm">
                                            <div className="flex items-center gap-2 mb-1">
                                              <span className="font-medium text-xs text-blue-600">{comment.author}</span>
                                              <span className="text-xs text-gray-500">👍 {comment.likes}</span>
                                              {comment.timestamp && (
                                                <span className="text-xs text-gray-400">
                                                  {new Date(comment.timestamp).toLocaleDateString()}
                                                </span>
                                              )}
                                            </div>
                                            <p className="text-sm text-gray-700 leading-relaxed">{comment.text}</p>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                  
                                  {/* No Comments State */}
                                  {comments.length === 0 && (
                                    <div className="text-xs text-gray-400 italic">No comments available</div>
                                  )}
                                </div>
                              </TableCell>
                            </TableRow>
                          );                        })}
                      </TableBody>
                    </Table>
                  </div>
                  </>
                ) : (                  <div className="flex items-center justify-center py-12">
                    <div className="text-center">
                      <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 48 48">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M34 40h10v-4a6 6 0 00-10.712-3.714M34 40H14m20 0v-4a9.971 9.971 0 00-.712-3.714M14 40H4v-4a6 6 0 0110.712-3.714M14 40v-4a9.971 9.971 0 01.712-3.714M18 20a6 6 0 1112 0v3.5M16 17.5a4 4 0 118 0v3.5" />
                      </svg>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No Facebook posts found</h3>
                      <p className="text-gray-500">This page doesn't have any scraped posts yet.</p>
                    </div>
                  </div>
                )}
              </TabsContent>
                <TabsContent value="processed" className="flex-1 overflow-hidden mt-4">
                <div className="h-full overflow-y-auto max-h-[60vh]">
                  {facebookProcessedPostsLoading ? (
                    <div className="flex items-center justify-center py-12">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      <span className="ml-2 text-gray-600">Loading processed posts...</span>
                    </div>
                  ) : facebookProcessedPosts.length > 0 ? (
                    <div className="overflow-x-auto">
                    <p className="text-sm text-muted-foreground mb-4">
                      Showing {facebookProcessedPosts.length} processed post{facebookProcessedPosts.length !== 1 ? 's' : ''}
                    </p>
                    <Table>
                    <TableHeader className="sticky top-0 bg-white z-10">
                        <TableRow>
                          <TableHead className="w-[80px]">ID</TableHead>
                          <TableHead className="min-w-[300px]">Summary</TableHead>
                          <TableHead className="w-[120px]">Platform</TableHead>
                          <TableHead className="w-[180px]">Primary Category</TableHead>
                          <TableHead className="w-[120px]">Severity</TableHead>
                          <TableHead className="w-[160px]">Electoral Misconduct</TableHead>
                          <TableHead className="w-[160px]">Political Attacks</TableHead>
                          <TableHead className="w-[180px]">Law Enforcement Misconduct</TableHead>
                          <TableHead className="w-[140px]">Civil Rights Restriction</TableHead>
                          <TableHead className="w-[140px]">Perpetrator</TableHead>
                          <TableHead className="w-[140px]">Victims Profile</TableHead>
                          <TableHead className="w-[120px]">District</TableHead>
                          <TableHead className="w-[120px]">Date of Incident</TableHead>
                          <TableHead className="w-[120px]">Created At</TableHead>
                          <TableHead className="w-[120px]">Sentiment</TableHead>
                          <TableHead className="w-[140px]">Election Relevance</TableHead>
                          <TableHead className="w-[160px]">Key Themes</TableHead>
                          <TableHead className="w-[120px]">Language</TableHead>
                          <TableHead className="w-[150px]">Original Language</TableHead>
                          <TableHead className="min-w-[300px]">English Translation</TableHead>
                          <TableHead className="w-[100px] sticky right-0 bg-white">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {facebookProcessedPosts.map((post) => (
                          <TableRow key={post.id}>
                            <TableCell className="font-mono text-xs">{post.id}</TableCell>
                            <TableCell className="max-w-sm">
                              <div className="truncate" title={post.post_summary}>
                                {post.post_summary}
                              </div>
                              <div className="text-xs text-muted-foreground mt-1">
                                {post.time_period} • {post.total_incidents} incident{post.total_incidents !== 1 ? 's' : ''}
                              </div>
                            </TableCell>
                            <TableCell>
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {post.platform}
                              </span>
                            </TableCell>
                            <TableCell className="max-w-xs">
                              <div className="truncate" title={post.primary_category}>
                                {post.primary_category}
                              </div>
                            </TableCell>
                            <TableCell>
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                post.severity_level === 'High'
                                  ? 'bg-red-100 text-red-800'
                                  : post.severity_level === 'Medium'
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : 'bg-green-100 text-green-800'
                              }`}>
                                {post.severity_level}
                              </span>
                            </TableCell>
                            <TableCell>
                              {post.electoral_misconduct ? (
                                <div className="truncate text-xs" title={post.electoral_misconduct}>
                                  {post.electoral_misconduct}
                                </div>
                              ) : 'N/A'}
                            </TableCell>
                            <TableCell>
                              {post.political_attacks ? (
                                <div className="truncate text-xs" title={post.political_attacks}>
                                  {post.political_attacks}
                                </div>
                              ) : 'N/A'}
                            </TableCell>
                            <TableCell>
                              {post.law_enforcement_misconduct ? (
                                <div className="truncate text-xs" title={post.law_enforcement_misconduct}>
                                  {post.law_enforcement_misconduct}
                                </div>
                              ) : 'N/A'}
                            </TableCell>
                            <TableCell>
                              {post.civil_rights_restriction ? (
                                <div className="truncate text-xs" title={post.civil_rights_restriction}>
                                  {post.civil_rights_restriction}
                                </div>
                              ) : 'N/A'}
                            </TableCell>
                            <TableCell>
                              <div className="truncate text-xs" title={post.perpetrator}>
                                {post.perpetrator || 'N/A'}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="truncate text-xs" title={post.victims_profile}>
                                {post.victims_profile || 'N/A'}
                              </div>
                            </TableCell>
                            <TableCell>
                              {post.district ? (
                                <span className="px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                                  {post.district}
                                </span>
                              ) : 'N/A'}
                            </TableCell>
                            <TableCell className="text-xs">
                              {post.date_of_incident ? new Date(post.date_of_incident).toLocaleDateString() : 'N/A'}
                            </TableCell>
                            <TableCell className="text-xs">
                              {post.created_at ? new Date(post.created_at).toLocaleDateString() : 'N/A'}
                            </TableCell>
                            <TableCell>
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                post.sentiment === 'Positive' 
                                  ? 'bg-green-100 text-green-800'
                                  : post.sentiment === 'Negative'
                                  ? 'bg-red-100 text-red-800'
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {post.sentiment}
                              </span>
                            </TableCell>
                            <TableCell>
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                post.election_relevance === 'Direct'
                                  ? 'bg-orange-100 text-orange-800'
                                  : post.election_relevance === 'Indirect'
                                  ? 'bg-amber-100 text-amber-800'
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {post.election_relevance}
                              </span>
                            </TableCell>
                            <TableCell>
                              {post.key_themes && post.key_themes.length > 0 ? (
                                <div className="flex flex-wrap gap-1 max-w-[150px]">
                                  {post.key_themes.slice(0, 2).map((theme, index) => (
                                    <span key={index} className="px-1 py-0.5 rounded text-xs bg-indigo-100 text-indigo-800 truncate">
                                      {theme}
                                    </span>
                                  ))}
                                  {post.key_themes.length > 2 && (
                                    <span className="text-xs text-muted-foreground">
                                      +{post.key_themes.length - 2}
                                    </span>
                                  )}
                                </div>
                              ) : 'N/A'}
                            </TableCell>
                            <TableCell className="text-xs">
                              {post.language_detected || 'N/A'}
                            </TableCell>
                            <TableCell className="text-xs">
                              {post.original_language || 'N/A'}
                            </TableCell>
                            <TableCell>
                              <div className="max-w-md">
                                <p className="text-sm line-clamp-3" title={post.english_translation}>
                                  {post.english_translation}
                                </p>
                              </div>
                            </TableCell>
                            <TableCell className="sticky right-0 bg-white z-10">
                              {post.source_url ? (
                                <a 
                                  href={post.source_url} 
                                  target="_blank" 
                                  rel="noopener noreferrer" 
                                  className="inline-flex items-center px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
                                  title={post.source_url}
                                >
                                  <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                  </svg>
                                  View                                </a>
                              ) : 'N/A'}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="flex items-center justify-center py-12">
                    <div className="text-center">
                      <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 48 48">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M34 40h10v-4a6 6 0 00-10.712-3.714M34 40H14m20 0v-4a9.971 9.971 0 00-.712-3.714M14 40H4v-4a6 6 0 0110.712-3.714M14 40v-4a9.971 9.971 0 01.712-3.714M18 20a6 6 0 1112 0v3.5M16 17.5a4 4 0 118 0v3.5" />
                      </svg>                      <h3 className="text-lg font-medium text-gray-900 mb-2">No processed posts found</h3>
                      <p className="text-gray-500">This page doesn't have any processed posts yet.</p>
                    </div>
                  </div>
                )}
                </div>
              </TabsContent>
            </Tabs>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsViewFacebookPostsModalOpen(false)}
            >
              Close
            </Button>          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};