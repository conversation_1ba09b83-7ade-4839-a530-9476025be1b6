import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Users, Target, CheckCircle, AlertCircle, Download, Calendar, Filter, MapPin, Star, TrendingUp } from 'lucide-react';

const CapacityNeedsAssessmentReport = () => {
  const [selectedRegion, setSelectedRegion] = useState("all");
  const [selectedSector, setSelectedSector] = useState("all");
  const [selectedCapacityArea, setSelectedCapacityArea] = useState("all");

  const regions = [
    { id: "all", label: "All Regions" },
    { id: "northern", label: "Northern Region" },
    { id: "central", label: "Central Region" },
    { id: "southern", label: "Southern Region" }
  ];

  const sectors = [
    { id: "all", label: "All Sectors" },
    { id: "government", label: "Government Agencies" },
    { id: "civil-society", label: "Civil Society Organizations" },
    { id: "traditional", label: "Traditional Authorities" },
    { id: "private", label: "Private Sector" },
    { id: "international", label: "International Partners" }
  ];

  const capacityAreas = [
    { id: "all", label: "All Capacity Areas" },
    { id: "peacebuilding", label: "Peacebuilding & Reconciliation" },
    { id: "conflict-prevention", label: "Conflict Prevention" },
    { id: "early-warning", label: "Early Warning Systems" },
    { id: "mediation", label: "Mediation & Dialogue" },
    { id: "community-engagement", label: "Community Engagement" }
  ];

  // Dummy capacity assessment data
  const capacityData = {
    overview: {
      totalOrganizations: 247,
      assessedCapacity: 3.2,
      capacityLevel: "Developing",
      criticalGaps: 8,
      strongCapacities: 12,
      lastAssessment: "2025-05-15"
    },
    capacityAreas: [
      {
        area: "Peacebuilding & Reconciliation",
        currentScore: 2.8,
        requiredScore: 4.5,
        capacityGap: 1.7,
        level: "Developing",
        description: "Community-level peacebuilding initiatives and reconciliation processes",
        organizations: [
          { name: "Centre for Human Rights", score: 4.2, role: "Lead Organization", location: "Lilongwe" },
          { name: "Malawi Peace Network", score: 3.8, role: "Coordination", location: "Blantyre" },
          { name: "Traditional Authority Councils", score: 2.1, role: "Community Level", location: "Various" },
          { name: "Church & Society Programme", score: 3.5, role: "Religious Mediation", location: "Nationwide" }
        ],
        keyGaps: [
          "Limited community-level mediation skills",
          "Insufficient funding for reconciliation programs",
          "Weak coordination between organizations",
          "Lack of trauma counseling capacity"
        ],
        strengthAreas: [
          "Strong traditional authority networks",
          "Active religious organization involvement",
          "Established NGO presence"
        ]
      },
      {
        area: "Early Warning Systems",
        currentScore: 3.5,
        requiredScore: 4.8,
        capacityGap: 1.3,
        level: "Developing",
        description: "Systems for detecting and responding to emerging conflicts",
        organizations: [
          { name: "National Intelligence Service", score: 4.1, role: "Intelligence Gathering", location: "Lilongwe" },
          { name: "District Peace Committees", score: 2.9, role: "Community Monitoring", location: "Various" },
          { name: "Media Monitoring Consortium", score: 3.8, role: "Information Analysis", location: "Blantyre" },
          { name: "Academic Research Institutes", score: 3.2, role: "Analysis & Research", location: "Various" }
        ],
        keyGaps: [
          "Limited community-level early warning capacity",
          "Inadequate technology infrastructure",
          "Poor information sharing protocols",
          "Insufficient rapid response mechanisms"
        ],
        strengthAreas: [
          "Government intelligence capability",
          "Media monitoring systems",
          "Academic research capacity"
        ]
      },
      {
        area: "Conflict Prevention",
        currentScore: 2.9,
        requiredScore: 4.2,
        capacityGap: 1.3,
        level: "Basic",
        description: "Proactive measures to prevent conflicts before they escalate",
        organizations: [
          { name: "Malawi Human Rights Commission", score: 3.7, role: "Rights Protection", location: "Lilongwe" },
          { name: "Public Affairs Committee", score: 2.8, role: "Advocacy", location: "Nationwide" },
          { name: "Youth Organizations", score: 2.3, role: "Youth Engagement", location: "Various" },
          { name: "Women's Rights Organizations", score: 3.1, role: "Gender Issues", location: "Various" }
        ],
        keyGaps: [
          "Weak preventive intervention mechanisms",
          "Limited youth engagement in prevention",
          "Insufficient women's participation",
          "Poor resource allocation for prevention"
        ],
        strengthAreas: [
          "Human rights commission capacity",
          "Civil society network presence",
          "Community organization structures"
        ]
      }
    ],
    regionalCapacity: [
      {
        region: "Southern Region",
        overallCapacity: 3.4,
        level: "Developing",
        population: 7500000,
        organizations: 98,
        strengths: ["Urban CSO concentration", "Academic institutions", "Media presence"],
        gaps: ["Rural coverage", "Resource distribution", "Coordination"],
        keyOrganizations: [
          { name: "Centre for Human Rights", capacity: 4.2, focus: "Rights advocacy" },
          { name: "Malawi Peace Network", capacity: 3.8, focus: "Peacebuilding" },
          { name: "Women's Voice", capacity: 3.1, focus: "Gender issues" }
        ],
        emergencyContacts: [
          { role: "Regional Coordinator", name: "Dr. Mary Banda", organization: "CHR", phone: "+265 1 123 456" },
          { role: "Early Warning Lead", name: "John Phiri", organization: "MPN", phone: "+265 1 234 567" }
        ]
      },
      {
        region: "Central Region",
        overallCapacity: 3.1,
        level: "Developing",
        population: 7200000,
        organizations: 89,
        strengths: ["Government proximity", "International presence", "Infrastructure"],
        gaps: ["Community outreach", "Traditional authority engagement", "Youth programs"],
        keyOrganizations: [
          { name: "Public Affairs Committee", capacity: 2.8, focus: "Advocacy" },
          { name: "Interfaith Committee", capacity: 3.4, focus: "Religious mediation" },
          { name: "Youth Peace Network", capacity: 2.7, focus: "Youth engagement" }
        ],
        emergencyContacts: [
          { role: "Regional Coordinator", name: "Rev. Peter Msiska", organization: "PAC", phone: "+265 1 345 678" },
          { role: "Mediation Lead", name: "Sister Grace", organization: "Interfaith", phone: "+265 1 456 789" }
        ]
      },
      {
        region: "Northern Region",
        overallCapacity: 2.7,
        level: "Basic",
        population: 2000000,
        organizations: 60,
        strengths: ["Traditional structures", "Community cohesion", "Low conflict history"],
        gaps: ["Limited resources", "Geographic isolation", "Technical capacity"],
        keyOrganizations: [
          { name: "Northern Region Peace Committee", capacity: 2.9, focus: "Coordination" },
          { name: "Traditional Leaders Network", capacity: 2.1, focus: "Community mediation" },
          { name: "Northern Development Initiative", capacity: 2.5, focus: "Development" }
        ],
        emergencyContacts: [
          { role: "Regional Coordinator", name: "Chief Mzukuzuku", organization: "NRPC", phone: "+265 1 567 890" },
          { role: "Community Lead", name: "Alice Mwale", organization: "NDI", phone: "+265 1 678 901" }
        ]
      }
    ],
    capacityBuilding: {
      priorityAreas: [
        {
          area: "Community-level Mediation",
          urgency: "High",
          targetGroups: ["Traditional Leaders", "Youth Leaders", "Women Leaders"],
          suggestedPrograms: [
            "Traditional Authority Mediation Training",
            "Community Dialogue Facilitation",
            "Conflict Resolution Skills Development"
          ],
          estimatedCost: "USD 150,000",
          duration: "12 months",
          expectedImpact: "Reduced community-level conflicts by 40%"
        },
        {
          area: "Early Warning Technology",
          urgency: "High",
          targetGroups: ["Government Officials", "CSO Staff", "Community Monitors"],
          suggestedPrograms: [
            "Digital Early Warning Platform Training",
            "Data Collection and Analysis",
            "Rapid Response Protocol Development"
          ],
          estimatedCost: "USD 200,000",
          duration: "18 months",
          expectedImpact: "50% faster response to emerging threats"
        },
        {
          area: "Youth Engagement",
          urgency: "Medium",
          targetGroups: ["Youth Organizations", "Student Leaders", "Community Youth"],
          suggestedPrograms: [
            "Youth Peace Ambassador Program",
            "Civic Education and Engagement",
            "Alternative Livelihoods Training"
          ],
          estimatedCost: "USD 100,000",
          duration: "24 months",
          expectedImpact: "Reduced youth involvement in political violence by 30%"
        }
      ],
      recommendedPartners: [
        { name: "UN Peacebuilding Fund", expertise: "Capacity Building", contact: "<EMAIL>" },
        { name: "USAID/Malawi", expertise: "Governance & Conflict", contact: "<EMAIL>" },
        { name: "GIZ Malawi", expertise: "Development Cooperation", contact: "<EMAIL>" },
        { name: "African Union", expertise: "Conflict Prevention", contact: "<EMAIL>" }
      ]
    }
  };

  const getCapacityLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'advanced': return 'bg-green-100 text-green-800 border-green-200';
      case 'developing': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'basic': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'limited': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency.toLowerCase()) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Users className="w-6 h-6" />
            Capacity Needs Assessment & Mapping Report
          </h2>
          <p className="text-gray-600 mt-1">Assessment of organizational capacity for peacebuilding and conflict prevention</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Calendar className="w-4 h-4 mr-2" />
            Schedule
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Assessment Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Region</label>
              <Select value={selectedRegion} onValueChange={setSelectedRegion}>
                <SelectTrigger>
                  <SelectValue placeholder="Select region" />
                </SelectTrigger>
                <SelectContent>
                  {regions.map(region => (
                    <SelectItem key={region.id} value={region.id}>
                      {region.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Sector</label>
              <Select value={selectedSector} onValueChange={setSelectedSector}>
                <SelectTrigger>
                  <SelectValue placeholder="Select sector" />
                </SelectTrigger>
                <SelectContent>
                  {sectors.map(sector => (
                    <SelectItem key={sector.id} value={sector.id}>
                      {sector.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Capacity Area</label>
              <Select value={selectedCapacityArea} onValueChange={setSelectedCapacityArea}>
                <SelectTrigger>
                  <SelectValue placeholder="Select area" />
                </SelectTrigger>
                <SelectContent>
                  {capacityAreas.map(area => (
                    <SelectItem key={area.id} value={area.id}>
                      {area.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button className="w-full">Apply Filters</Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Overview Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Organizations</p>
                <p className="text-2xl font-bold">{capacityData.overview.totalOrganizations}</p>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Avg. Capacity</p>
                <div className="flex items-center gap-2">
                  <p className="text-2xl font-bold">{capacityData.overview.assessedCapacity}/5</p>
                  <Badge className={getCapacityLevelColor(capacityData.overview.capacityLevel)}>
                    {capacityData.overview.capacityLevel}
                  </Badge>
                </div>
              </div>
              <Star className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Critical Gaps</p>
                <p className="text-2xl font-bold">{capacityData.overview.criticalGaps}</p>
              </div>
              <AlertCircle className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Strong Areas</p>
                <p className="text-2xl font-bold">{capacityData.overview.strongCapacities}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Last Assessment</p>
                <p className="text-lg font-bold">{capacityData.overview.lastAssessment}</p>
              </div>
              <Calendar className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analysis Tabs */}
      <Tabs defaultValue="capacity" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="capacity">Capacity Areas</TabsTrigger>
          <TabsTrigger value="regional">Regional Mapping</TabsTrigger>
          <TabsTrigger value="building">Capacity Building</TabsTrigger>
          <TabsTrigger value="contacts">Emergency Contacts</TabsTrigger>
        </TabsList>

        <TabsContent value="capacity" className="space-y-4">
          {capacityData.capacityAreas.map((area, index) => (
            <Card key={index} className="border-l-4 border-l-blue-500">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="w-5 h-5" />
                      {area.area}
                    </CardTitle>
                    <p className="text-gray-600">{area.description}</p>
                  </div>
                  <div className="text-right">
                    <Badge className={getCapacityLevelColor(area.level)}>
                      {area.level}
                    </Badge>
                    <p className="text-sm text-gray-600 mt-1">Gap: {area.capacityGap} points</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Capacity Score */}
                  <div>
                    <h4 className="font-semibold mb-3">Capacity Assessment</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Current Score</span>
                        <span className="font-bold text-lg">{area.currentScore}/5</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-3">
                        <div
                          className="bg-blue-500 h-3 rounded-full"
                          style={{ width: `${(area.currentScore / 5) * 100}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-green-600">Required Score</span>
                        <span className="font-bold text-green-600">{area.requiredScore}/5</span>
                      </div>

                      <div className="mt-4">
                        <h5 className="font-medium mb-2">Key Organizations</h5>
                        <div className="space-y-2">
                          {area.organizations.slice(0, 3).map((org, idx) => (
                            <div key={idx} className="flex justify-between items-center text-sm">
                              <div>
                                <span className="font-medium">{org.name}</span>
                                <span className="text-gray-500 text-xs block">{org.role}</span>
                              </div>
                              <Badge variant="outline">{org.score}/5</Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Key Gaps */}
                  <div>
                    <h4 className="font-semibold mb-3">Critical Gaps</h4>
                    <div className="space-y-2">
                      {area.keyGaps.map((gap, idx) => (
                        <div key={idx} className="flex items-start gap-2 p-2 bg-red-50 rounded">
                          <AlertCircle className="w-4 h-4 text-red-600 mt-0.5" />
                          <span className="text-sm">{gap}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Strength Areas */}
                  <div>
                    <h4 className="font-semibold mb-3">Existing Strengths</h4>
                    <div className="space-y-2">
                      {area.strengthAreas.map((strength, idx) => (
                        <div key={idx} className="flex items-start gap-2 p-2 bg-green-50 rounded">
                          <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                          <span className="text-sm">{strength}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="regional" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {capacityData.regionalCapacity.map((region, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="w-5 h-5" />
                      {region.region}
                    </CardTitle>
                    <Badge className={getCapacityLevelColor(region.level)}>
                      {region.level}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-600">Capacity Score</p>
                        <p className="text-2xl font-bold">{region.overallCapacity}/5</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Organizations</p>
                        <p className="text-2xl font-bold">{region.organizations}</p>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm font-medium mb-2">Key Strengths</p>
                      <div className="flex flex-wrap gap-1">
                        {region.strengths.map((strength, idx) => (
                          <Badge key={idx} className="bg-green-100 text-green-800 text-xs">
                            {strength}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <p className="text-sm font-medium mb-2">Priority Gaps</p>
                      <div className="flex flex-wrap gap-1">
                        {region.gaps.map((gap, idx) => (
                          <Badge key={idx} className="bg-red-100 text-red-800 text-xs">
                            {gap}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <p className="text-sm font-medium mb-2">Key Organizations</p>
                      <div className="space-y-1">
                        {region.keyOrganizations.map((org, idx) => (
                          <div key={idx} className="flex justify-between items-center text-xs">
                            <div>
                              <span className="font-medium">{org.name}</span>
                              <span className="text-gray-500 block">{org.focus}</span>
                            </div>
                            <Badge variant="outline" className="text-xs">{org.capacity}/5</Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="building" className="space-y-4">
          <div className="space-y-6">
            {capacityData.capacityBuilding.priorityAreas.map((priority, index) => (
              <Card key={index} className="border-l-4 border-l-green-500">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="w-5 h-5" />
                      {priority.area}
                    </CardTitle>
                    <Badge className={getUrgencyColor(priority.urgency)}>
                      {priority.urgency} Priority
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <div className="space-y-4">
                        <div>
                          <p className="text-sm font-medium mb-2">Target Groups</p>
                          <div className="flex flex-wrap gap-1">
                            {priority.targetGroups.map((group, idx) => (
                              <Badge key={idx} variant="outline" className="text-xs">
                                {group}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <div>
                          <p className="text-sm font-medium mb-2">Program Details</p>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="text-gray-600">Duration:</span>
                              <span className="font-medium ml-2">{priority.duration}</span>
                            </div>
                            <div>
                              <span className="text-gray-600">Cost:</span>
                              <span className="font-medium ml-2">{priority.estimatedCost}</span>
                            </div>
                          </div>
                          <div className="mt-2">
                            <span className="text-gray-600 text-sm">Expected Impact:</span>
                            <p className="font-medium text-sm">{priority.expectedImpact}</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm font-medium mb-2">Suggested Programs</p>
                      <div className="space-y-2">
                        {priority.suggestedPrograms.map((program, idx) => (
                          <div key={idx} className="flex items-start gap-2 p-2 bg-blue-50 rounded">
                            <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5" />
                            <span className="text-sm">{program}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            <Card>
              <CardHeader>
                <CardTitle>Recommended Implementation Partners</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {capacityData.capacityBuilding.recommendedPartners.map((partner, idx) => (
                    <div key={idx} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                      <div>
                        <span className="font-medium">{partner.name}</span>
                        <span className="text-gray-600 text-sm block">{partner.expertise}</span>
                      </div>
                      <span className="text-blue-600 text-sm">{partner.contact}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="contacts" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {capacityData.regionalCapacity.map((region, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="w-5 h-5" />
                    {region.region} - Emergency Contacts
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {region.emergencyContacts.map((contact, idx) => (
                      <div key={idx} className="p-3 bg-gray-50 rounded">
                        <div className="flex justify-between items-start mb-2">
                          <span className="font-medium">{contact.role}</span>
                          <Badge variant="outline" className="text-xs">Emergency</Badge>
                        </div>
                        <div className="space-y-1">
                          <p className="text-sm"><span className="font-medium">Name:</span> {contact.name}</p>
                          <p className="text-sm"><span className="font-medium">Organization:</span> {contact.organization}</p>
                          <p className="text-sm"><span className="font-medium">Phone:</span> {contact.phone}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CapacityNeedsAssessmentReport;