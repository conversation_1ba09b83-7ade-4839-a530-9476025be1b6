import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DollarSign, TrendingDown, TrendingUp, AlertTriangle, Download, Calendar, Filter, Users, Thermometer, Cloud } from 'lucide-react';

const SocioEconomicRisksReport = () => {
  const [selectedRegion, setSelectedRegion] = useState("all");
  const [selectedRiskCategory, setSelectedRiskCategory] = useState("all");
  const [selectedTimeframe, setSelectedTimeframe] = useState("current");

  const regions = [
    { id: "all", label: "All Regions" },
    { id: "northern", label: "Northern Region" },
    { id: "central", label: "Central Region" },
    { id: "southern", label: "Southern Region" }
  ];

  const riskCategories = [
    { id: "all", label: "All Risk Categories" },
    { id: "economic", label: "Economic Risks" },
    { id: "social", label: "Social Risks" },
    { id: "environmental", label: "Environmental Risks" },
    { id: "conflict", label: "Conflict-Related Risks" }
  ];

  const timeframes = [
    { id: "current", label: "Current Assessment" },
    { id: "3months", label: "3-Month Projection" },
    { id: "6months", label: "6-Month Projection" },
    { id: "1year", label: "1-Year Projection" }
  ];

  // Dummy socio-economic data
  const socioEconomicData = {
    overview: {
      overallRiskScore: 7.2,
      riskLevel: "High",
      affectedPopulation: 8500000,
      vulnerableHouseholds: 2100000,
      emergingThreats: 5,
      lastAssessment: "2025-05-30"
    },
    riskCategories: [
      {
        category: "Economic Risks",
        riskScore: 8.1,
        riskLevel: "Critical",
        description: "High unemployment, inflation, and food insecurity",
        indicators: [
          { name: "Youth Unemployment Rate", value: "78%", trend: "increasing", severity: "critical" },
          { name: "Food Price Inflation", value: "45%", trend: "increasing", severity: "high" },
          { name: "Poverty Rate", value: "70%", trend: "stable", severity: "high" },
          { name: "Income Inequality (Gini)", value: "0.62", trend: "increasing", severity: "high" }
        ],
        conflictNexus: [
          "Economic hardship driving youth into political violence",
          "Competition for resources increasing community tensions",
          "Election period economic promises creating unrealistic expectations"
        ]
      },
      {
        category: "Social Risks",
        riskScore: 6.8,
        riskLevel: "High",
        description: "Social cohesion challenges, ethnic tensions, and demographic pressures",
        indicators: [
          { name: "Population Growth Rate", value: "2.8%", trend: "stable", severity: "medium" },
          { name: "Ethnic Tension Index", value: "6.2/10", trend: "increasing", severity: "high" },
          { name: "Education Access Rate", value: "65%", trend: "decreasing", severity: "high" },
          { name: "Healthcare Access", value: "42%", trend: "stable", severity: "critical" }
        ],
        conflictNexus: [
          "Limited healthcare access increasing social unrest",
          "Educational gaps creating intergenerational tensions",
          "Ethnic divisions exploited during election campaigns"
        ]
      },
      {
        category: "Environmental Risks",
        riskScore: 7.5,
        riskLevel: "High",
        description: "Climate change impacts, natural disasters, and resource scarcity",
        indicators: [
          { name: "Climate Vulnerability Index", value: "8.3/10", trend: "increasing", severity: "critical" },
          { name: "Water Scarcity Level", value: "High", trend: "increasing", severity: "high" },
          { name: "Deforestation Rate", value: "2.1%/year", trend: "stable", severity: "high" },
          { name: "Natural Disaster Frequency", value: "12/year", trend: "increasing", severity: "medium" }
        ],
        conflictNexus: [
          "Water scarcity causing farmer-herder conflicts",
          "Climate migration straining urban resources",
          "Disasters disrupting electoral processes and security"
        ]
      },
      {
        category: "Gender-Based Violence (GBV)",
        riskScore: 6.9,
        riskLevel: "High",
        description: "Systemic gender-based violence and women's vulnerability",
        indicators: [
          { name: "GBV Incident Rate", value: "35%", trend: "increasing", severity: "critical" },
          { name: "Women's Economic Participation", value: "58%", trend: "stable", severity: "medium" },
          { name: "Child Marriage Rate", value: "42%", trend: "decreasing", severity: "high" },
          { name: "Maternal Mortality Rate", value: "439/100k", trend: "decreasing", severity: "high" }
        ],
        conflictNexus: [
          "Political tensions increasing domestic violence rates",
          "Election period restrictions limiting women's mobility",
          "Economic stress escalating household violence"
        ]
      }
    ],
    regionalAnalysis: [
      {
        region: "Southern Region",
        overallRisk: 8.2,
        riskLevel: "Critical",
        population: 7500000,
        vulnerablePopulation: 68,
        primaryRisks: ["Food Insecurity", "Climate Vulnerability", "High Population Density"],
        conflictIndicators: [
          { type: "Resource Competition", level: "High" },
          { type: "Political Tensions", level: "Critical" },
          { type: "Economic Distress", level: "Critical" }
        ]
      },
      {
        region: "Central Region",
        overallRisk: 7.1,
        riskLevel: "High",
        population: 7200000,
        vulnerablePopulation: 61,
        primaryRisks: ["Urban Migration", "Youth Unemployment", "Infrastructure Strain"],
        conflictIndicators: [
          { type: "Resource Competition", level: "Medium" },
          { type: "Political Tensions", level: "High" },
          { type: "Economic Distress", level: "High" }
        ]
      },
      {
        region: "Northern Region",
        overallRisk: 5.8,
        riskLevel: "Medium",
        population: 2000000,
        vulnerablePopulation: 52,
        primaryRisks: ["Geographic Isolation", "Limited Services", "Agricultural Challenges"],
        conflictIndicators: [
          { type: "Resource Competition", level: "Medium" },
          { type: "Political Tensions", level: "Medium" },
          { type: "Economic Distress", level: "Medium" }
        ]
      }
    ],
    earlyWarningIndicators: [
      {
        indicator: "Food Price Volatility",
        currentLevel: "Critical",
        trend: "Increasing",
        threshold: "20% monthly increase",
        currentValue: "32% increase",
        description: "Staple food prices have exceeded emergency thresholds"
      },
      {
        indicator: "Youth Unemployment Protests",
        currentLevel: "High",
        trend: "Increasing",
        threshold: ">5 incidents/month",
        currentValue: "8 incidents this month",
        description: "Organized youth protests increasing in frequency and intensity"
      },
      {
        indicator: "Seasonal Migration Patterns",
        currentLevel: "Medium",
        trend: "Stable",
        threshold: ">15% population movement",
        currentValue: "12% movement detected",
        description: "Climate-induced migration within acceptable ranges"
      },
      {
        indicator: "Inter-community Tensions",
        currentLevel: "High",
        trend: "Increasing",
        threshold: ">3 incidents/week",
        currentValue: "5 incidents/week",
        description: "Resource-related conflicts between communities rising"
      }
    ]
  };

  const getRiskLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing': return <TrendingUp className="w-4 h-4 text-red-500" />;
      case 'decreasing': return <TrendingDown className="w-4 h-4 text-green-500" />;
      default: return <div className="w-4 h-4 bg-gray-400 rounded-full" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <DollarSign className="w-6 h-6" />
            Socio-economic Risks Analysis Report
          </h2>
          <p className="text-gray-600 mt-1">Comprehensive analysis of socio-economic risks and their nexus with conflict dimensions</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Calendar className="w-4 h-4 mr-2" />
            Schedule
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Assessment Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Region</label>
              <Select value={selectedRegion} onValueChange={setSelectedRegion}>
                <SelectTrigger>
                  <SelectValue placeholder="Select region" />
                </SelectTrigger>
                <SelectContent>
                  {regions.map(region => (
                    <SelectItem key={region.id} value={region.id}>
                      {region.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Risk Category</label>
              <Select value={selectedRiskCategory} onValueChange={setSelectedRiskCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {riskCategories.map(category => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Assessment Period</label>
              <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
                <SelectTrigger>
                  <SelectValue placeholder="Select timeframe" />
                </SelectTrigger>
                <SelectContent>
                  {timeframes.map(timeframe => (
                    <SelectItem key={timeframe.id} value={timeframe.id}>
                      {timeframe.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button className="w-full">Apply Filters</Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Overview Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Risk Score</p>
                <div className="flex items-center gap-2">
                  <p className="text-2xl font-bold">{socioEconomicData.overview.overallRiskScore}/10</p>
                  <Badge className={getRiskLevelColor(socioEconomicData.overview.riskLevel)}>
                    {socioEconomicData.overview.riskLevel}
                  </Badge>
                </div>
              </div>
              <Thermometer className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Affected Population</p>
                <p className="text-2xl font-bold">{(socioEconomicData.overview.affectedPopulation / 1000000).toFixed(1)}M</p>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Vulnerable HH</p>
                <p className="text-2xl font-bold">{(socioEconomicData.overview.vulnerableHouseholds / 1000000).toFixed(1)}M</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Emerging Threats</p>
                <p className="text-2xl font-bold">{socioEconomicData.overview.emergingThreats}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Last Assessment</p>
                <p className="text-lg font-bold">{socioEconomicData.overview.lastAssessment}</p>
              </div>
              <Calendar className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analysis Tabs */}
      <Tabs defaultValue="risks" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="risks">Risk Categories</TabsTrigger>
          <TabsTrigger value="regional">Regional Analysis</TabsTrigger>
          <TabsTrigger value="indicators">Early Warning</TabsTrigger>
          <TabsTrigger value="nexus">Conflict Nexus</TabsTrigger>
        </TabsList>

        <TabsContent value="risks" className="space-y-4">
          {socioEconomicData.riskCategories.map((category, index) => (
            <Card key={index} className="border-l-4 border-l-orange-500">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <DollarSign className="w-5 h-5" />
                      {category.category}
                    </CardTitle>
                    <p className="text-gray-600">{category.description}</p>
                  </div>
                  <div className="text-right">
                    <Badge className={getRiskLevelColor(category.riskLevel)}>
                      {category.riskLevel} Risk
                    </Badge>
                    <p className="text-sm text-gray-600 mt-1">Score: {category.riskScore}/10</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Risk Indicators */}
                  <div>
                    <h4 className="font-semibold mb-3">Key Indicators</h4>
                    <div className="space-y-3">
                      {category.indicators.map((indicator, idx) => (
                        <div key={idx} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-medium">{indicator.name}</span>
                              {getTrendIcon(indicator.trend)}
                            </div>
                            <div className="flex items-center gap-2 mt-1">
                              <div
                                className={`w-2 h-2 rounded-full ${getSeverityColor(indicator.severity)}`}
                              ></div>
                              <span className="text-xs text-gray-600 capitalize">{indicator.severity}</span>
                            </div>
                          </div>
                          <span className="font-bold text-lg">{indicator.value}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Conflict Nexus */}
                  <div>
                    <h4 className="font-semibold mb-3">Conflict Nexus Points</h4>
                    <div className="space-y-2">
                      {category.conflictNexus.map((nexus, idx) => (
                        <div key={idx} className="flex items-start gap-2 p-2 bg-red-50 rounded">
                          <AlertTriangle className="w-4 h-4 text-red-600 mt-0.5" />
                          <span className="text-sm">{nexus}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="regional" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {socioEconomicData.regionalAnalysis.map((region, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">{region.region}</CardTitle>
                    <Badge className={getRiskLevelColor(region.riskLevel)}>
                      {region.riskLevel}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-600">Risk Score</p>
                        <p className="text-2xl font-bold">{region.overallRisk}/10</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Vulnerable Pop.</p>
                        <p className="text-2xl font-bold">{region.vulnerablePopulation}%</p>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm font-medium mb-2">Primary Risks</p>
                      <div className="flex flex-wrap gap-1">
                        {region.primaryRisks.map((risk, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {risk}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <p className="text-sm font-medium mb-2">Conflict Indicators</p>
                      <div className="space-y-1">
                        {region.conflictIndicators.map((indicator, idx) => (
                          <div key={idx} className="flex justify-between items-center">
                            <span className="text-xs">{indicator.type}</span>
                            <Badge className={getRiskLevelColor(indicator.level)} size="sm">
                              {indicator.level}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="indicators" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Early Warning Indicators</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {socioEconomicData.earlyWarningIndicators.map((indicator, idx) => (
                  <Card key={idx} className="border-l-4 border-l-yellow-500">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-3">
                        <div>
                          <h4 className="font-semibold">{indicator.indicator}</h4>
                          <p className="text-sm text-gray-600">{indicator.description}</p>
                        </div>
                        <div className="text-right">
                          <Badge className={getRiskLevelColor(indicator.currentLevel)}>
                            {indicator.currentLevel}
                          </Badge>
                          <div className="flex items-center gap-1 mt-1">
                            {getTrendIcon(indicator.trend.toLowerCase())}
                            <span className="text-xs text-gray-600">{indicator.trend}</span>
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-xs text-gray-600">Threshold</p>
                          <p className="font-medium">{indicator.threshold}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-600">Current Value</p>
                          <p className="font-medium">{indicator.currentValue}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="nexus" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Socio-Economic & Conflict Nexus Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3">Primary Nexus Points</h4>
                    <div className="space-y-3">
                      <div className="p-3 bg-red-50 rounded border-l-4 border-red-500">
                        <h5 className="font-medium">Economic Grievances → Political Violence</h5>
                        <p className="text-sm text-gray-600">High unemployment driving youth participation in political violence</p>
                      </div>
                      <div className="p-3 bg-orange-50 rounded border-l-4 border-orange-500">
                        <h5 className="font-medium">Resource Scarcity → Community Conflicts</h5>
                        <p className="text-sm text-gray-600">Water and land scarcity creating inter-community tensions</p>
                      </div>
                      <div className="p-3 bg-yellow-50 rounded border-l-4 border-yellow-500">
                        <h5 className="font-medium">Climate Impacts → Migration Pressures</h5>
                        <p className="text-sm text-gray-600">Environmental degradation forcing population movements</p>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3">Risk Mitigation Priorities</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between p-2 bg-green-50 rounded">
                        <span className="text-sm">Youth Employment Programs</span>
                        <Badge className="bg-green-100 text-green-800">High Priority</Badge>
                      </div>
                      <div className="flex items-center justify-between p-2 bg-blue-50 rounded">
                        <span className="text-sm">Food Security Interventions</span>
                        <Badge className="bg-blue-100 text-blue-800">High Priority</Badge>
                      </div>
                      <div className="flex items-center justify-between p-2 bg-purple-50 rounded">
                        <span className="text-sm">Climate Adaptation Support</span>
                        <Badge className="bg-purple-100 text-purple-800">Medium Priority</Badge>
                      </div>
                      <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm">Social Cohesion Programs</span>
                        <Badge className="bg-gray-100 text-gray-800">Medium Priority</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SocioEconomicRisksReport;