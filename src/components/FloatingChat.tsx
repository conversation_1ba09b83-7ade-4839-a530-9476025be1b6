import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { MessageCircle, X, Minus, Send, Settings, Maximize2, Minimize2 } from 'lucide-react';
import { ChatSettings, ChatMessage, TypingIndicator } from '@/components/ChatComponents';
import { useChat } from '@/hooks/useChat';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
}

const FloatingChat: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);  const [isMaximized, setIsMaximized] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [inputMessage, setInputMessage] = useState('');
  const [isConnected, setIsConnected] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  // Use the chat hook
  const {
    messages,
    isLoading,
    error,
    sendMessage: sendChatMessage,
    clearMessages,
    exportMessages
  } = useChat({
    initialMessages: [{
      id: '1',
      text: 'Hello! I\'m Magwero (Sources), your AI assistant. How can I help you today? I can assist with Malawi media monitoring, analytics, and answer questions about the Magwero platform. Note: My answers are within data I have monitored from different Media platforms.',
      sender: 'ai',
      timestamp: new Date()    }],
    conversationId: "1" // Use "1" as per your API requirement
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);  const sendMessage = async () => {
    if (!inputMessage.trim()) return;
    
    try {
      await sendChatMessage(inputMessage);
      setInputMessage('');
      setIsConnected(true);
    } catch (error) {
      console.error('Failed to send message:', error);
      setIsConnected(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };
  const toggleChat = () => {
    setIsOpen(!isOpen);
    setIsMinimized(false);
    setShowSettings(false);
  };

  const minimizeChat = () => {
    setIsMinimized(true);
    setShowSettings(false);
  };
  const maximizeChat = () => {
    setIsMaximized(!isMaximized);
    setIsMinimized(false); // Ensure it's not minimized when maximizing
  };

  const restoreChat = () => {
    setIsMinimized(false);
    setIsMaximized(false); // Reset maximize state when restoring from minimize
  };

  const toggleSettings = () => {
    setShowSettings(!showSettings);
  };
  const clearChatHistory = () => {
    clearMessages();
    setShowSettings(false);
  };

  const exportChat = () => {
    const chatData = exportMessages();
    
    const dataStr = JSON.stringify(chatData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `chat-export-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
    setShowSettings(false);
  };

  return (
    <>
      {/* Floating Chat Button */}
      {!isOpen && (
        <div className="fixed bottom-4 right-4 z-50">
          <Button
            onClick={toggleChat}
            className="rounded-full w-14 h-14 bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-200"
          >
            <MessageCircle className="w-6 h-6 text-white" />
          </Button>
        </div>
      )}      {/* Chat Window */}
      {isOpen && !showSettings && (
        <div className="fixed bottom-4 right-4 z-50">          <Card className={`transition-all duration-200 shadow-xl ${
            isMinimized 
              ? 'w-80 h-12' 
              : isMaximized 
                ? 'w-96 h-[600px]' 
                : 'w-80 h-96'
          }`}>
            {/* Chat Header */}
            <CardHeader className="p-3 border-b bg-blue-600 text-white rounded-t-lg">
              <div className="flex items-center justify-between">                <CardTitle 
                  className="text-sm font-medium cursor-pointer flex items-center gap-2"
                  onClick={isMinimized ? restoreChat : undefined}
                >
                  <MessageCircle className="w-4 h-4" />                  Magwero (Sources)
                  {isLoading && (
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                      <span className="text-xs">Processing...</span>
                    </div>
                  )}
                </CardTitle>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={toggleSettings}
                    className="h-6 w-6 p-0 hover:bg-blue-700 text-white"
                  >
                    <Settings className="w-3 h-3" />
                  </Button>                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={maximizeChat}
                    className="h-6 w-6 p-0 hover:bg-blue-700 text-white"
                    title={isMaximized ? "Restore Size" : "Maximize"}
                  >
                    {isMaximized ? <Minimize2 className="w-3 h-3" /> : <Maximize2 className="w-3 h-3" />}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={minimizeChat}
                    className="h-6 w-6 p-0 hover:bg-blue-700 text-white"
                  >
                    <Minus className="w-3 h-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                    className="h-6 w-6 p-0 hover:bg-blue-700 text-white"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            </CardHeader>

            {/* Chat Content */}
            {!isMinimized && (
              <CardContent className="p-0 flex flex-col" style={{ 
                height: isMaximized ? '540px' : '320px' 
              }}>
                {/* Messages Area */}                <ScrollArea className="flex-1 p-3">
                  <div className="space-y-1">                    {messages.map((message) => (
                      <ChatMessage key={message.id} message={message} />
                    ))}
                    <TypingIndicator isVisible={isLoading} />
                    {error && (
                      <div className="flex justify-center my-2">
                        <div className="bg-red-50 border border-red-200 rounded-lg px-3 py-2 text-sm text-red-700">
                          {error}
                        </div>
                      </div>
                    )}
                    <div ref={messagesEndRef} data-chat-messages-end />
                  </div>
                </ScrollArea>

                {/* Input Area */}
                <div className="p-3 border-t bg-gray-50">
                  <div className="flex gap-2">
                    <Input
                      value={inputMessage}
                      onChange={(e) => setInputMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Type your message..."
                      disabled={isLoading}
                      className="flex-1 text-sm"
                    />
                    <Button
                      onClick={sendMessage}
                      disabled={!inputMessage.trim() || isLoading}
                      size="sm"
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Send className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            )}
          </Card>
        </div>
      )}      {/* Settings Panel */}
      {showSettings && (
        <div className="fixed bottom-4 right-4 z-50">
          <ChatSettings
            onClose={() => setShowSettings(false)}
            onClearHistory={clearChatHistory}
            onExportChat={exportChat}
            isConnected={isConnected}
          />
        </div>
      )}
    </>
  );
};

export default FloatingChat;
