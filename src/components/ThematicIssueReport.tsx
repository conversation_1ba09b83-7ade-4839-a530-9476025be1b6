import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Hash, TrendingUp, Users, MessageSquare, Download, Calendar, Filter } from 'lucide-react';

const ThematicIssueReport = () => {
  const [selectedTheme, setSelectedTheme] = useState("elections");
  const [selectedPeriod, setSelectedPeriod] = useState("30days");

  const themes = [
    { id: "elections", label: "Elections & Voting", color: "bg-blue-100 text-blue-800" },
    { id: "gbv", label: "Gender-Based Violence", color: "bg-red-100 text-red-800" },
    { id: "climate", label: "Climate Policy", color: "bg-green-100 text-green-800" },
    { id: "reforms", label: "Political Reforms", color: "bg-purple-100 text-purple-800" },
    { id: "economy", label: "Economic Issues", color: "bg-yellow-100 text-yellow-800" }
  ];

  const periods = [
    { id: "7days", label: "Last 7 Days" },
    { id: "30days", label: "Last 30 Days" },
    { id: "90days", label: "Last 3 Months" },
    { id: "custom", label: "Custom Range" }
  ];

  // Dummy data for Elections theme
  const thematicData = {
    elections: {
      overview: {
        totalMentions: 15678,
        averageSentiment: 0.3,
        topPlatform: "Facebook",
        peakActivity: "May 15, 2025 - Election Rally Day",
        riskLevel: "Medium"
      },
      volumeData: [
        { date: "2025-05-01", mentions: 1245 },
        { date: "2025-05-02", mentions: 1367 },
        { date: "2025-05-03", mentions: 1456 },
        { date: "2025-05-15", mentions: 3421 }, // Peak day
        { date: "2025-05-16", mentions: 2987 },
        { date: "2025-05-30", mentions: 1678 }
      ],
      sentimentBreakdown: {
        positive: 45,
        neutral: 32,
        negative: 23
      },
      keyActors: [
        { name: "Lazarus Chakwera", mentions: 3456, sentiment: 0.2, role: "Presidential Candidate" },
        { name: "DPP Party", mentions: 2876, sentiment: -0.1, role: "Political Party" },
        { name: "MCP Youth", mentions: 2134, sentiment: 0.4, role: "Political Movement" },
        { name: "Civil Society Coalition", mentions: 1789, sentiment: 0.6, role: "CSO" }
      ],
      narratives: [
        { 
          narrative: "Youth Engagement in Elections", 
          volume: 4567, 
          sentiment: 0.5, 
          description: "Strong positive sentiment around youth participation",
          keyPhrases: ["#YouthVote", "Future Leaders", "Democracy"] 
        },
        { 
          narrative: "Electoral Process Transparency", 
          volume: 3456, 
          sentiment: 0.1, 
          description: "Mixed sentiment regarding electoral transparency measures",
          keyPhrases: ["Free and Fair", "Electoral Commission", "Transparency"] 
        },
        { 
          narrative: "Campaign Violence Concerns", 
          volume: 2789, 
          sentiment: -0.4, 
          description: "Negative sentiment around reported campaign incidents",
          keyPhrases: ["Safety Concerns", "Violence", "Peaceful Elections"] 
        }
      ],
      topPosts: [
        {
          content: "Youth engagement in this election cycle shows unprecedented levels...",
          author: "@MalawiYouth2025",
          platform: "Twitter",
          engagement: 3456,
          sentiment: 0.7,
          date: "2025-05-15"
        },
        {
          content: "Electoral Commission announces new transparency measures...",
          author: "Malawi Electoral Commission",
          platform: "Facebook",
          engagement: 2876,
          sentiment: 0.2,
          date: "2025-05-14"
        }
      ]
    }
  };

  const getSentimentColor = (sentiment: number) => {
    if (sentiment > 0.2) return "bg-green-100 text-green-800";
    if (sentiment > -0.2) return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800";
  };

  const getSentimentLabel = (sentiment: number) => {
    if (sentiment > 0.2) return "Positive";
    if (sentiment > -0.2) return "Neutral";
    return "Negative";
  };

  const currentThemeData = thematicData[selectedTheme] || thematicData.elections;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Hash className="w-6 h-6" />
            Thematic Issue Report
          </h2>
          <p className="text-gray-600 mt-1">Volume, sentiment, key actors, and narratives analysis by theme</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Calendar className="w-4 h-4 mr-2" />
            Schedule
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Report Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Theme</label>
              <Select value={selectedTheme} onValueChange={setSelectedTheme}>
                <SelectTrigger>
                  <SelectValue placeholder="Select theme" />
                </SelectTrigger>
                <SelectContent>
                  {themes.map(theme => (
                    <SelectItem key={theme.id} value={theme.id}>
                      {theme.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Time Period</label>
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger>
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  {periods.map(period => (
                    <SelectItem key={period.id} value={period.id}>
                      {period.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button className="w-full">Apply Filters</Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Overview Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Mentions</p>
                <p className="text-2xl font-bold">{currentThemeData.overview.totalMentions.toLocaleString()}</p>
              </div>
              <MessageSquare className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Avg. Sentiment</p>
                <div className="flex items-center gap-2">
                  <p className="text-2xl font-bold">{currentThemeData.overview.averageSentiment > 0 ? '+' : ''}{(currentThemeData.overview.averageSentiment * 100).toFixed(0)}%</p>
                  <Badge className={getSentimentColor(currentThemeData.overview.averageSentiment)}>
                    {getSentimentLabel(currentThemeData.overview.averageSentiment)}
                  </Badge>
                </div>
              </div>
              <TrendingUp className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Top Platform</p>
                <p className="text-2xl font-bold">{currentThemeData.overview.topPlatform}</p>
              </div>
              <Hash className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Peak Activity</p>
                <p className="text-xs font-medium text-gray-900">{currentThemeData.overview.peakActivity}</p>
              </div>
              <Calendar className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Risk Level</p>
                <Badge className="bg-orange-100 text-orange-800">{currentThemeData.overview.riskLevel}</Badge>
              </div>
              <Users className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analysis Tabs */}
      <Tabs defaultValue="narratives" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="narratives">Key Narratives</TabsTrigger>
          <TabsTrigger value="actors">Key Actors</TabsTrigger>
          <TabsTrigger value="sentiment">Sentiment Analysis</TabsTrigger>
          <TabsTrigger value="posts">Top Posts</TabsTrigger>
        </TabsList>

        <TabsContent value="narratives" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Key Narratives Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {currentThemeData.narratives.map((narrative, index) => (
                  <Card key={index} className="border-l-4 border-l-blue-500">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-semibold text-lg">{narrative.narrative}</h4>
                        <div className="flex items-center gap-2">
                          <Badge className={getSentimentColor(narrative.sentiment)}>
                            {getSentimentLabel(narrative.sentiment)}
                          </Badge>
                          <span className="text-sm text-gray-600">{narrative.volume.toLocaleString()} mentions</span>
                        </div>
                      </div>
                      <p className="text-gray-600 mb-3">{narrative.description}</p>
                      <div className="flex flex-wrap gap-2">
                        {narrative.keyPhrases.map((phrase, idx) => (
                          <Badge key={idx} variant="outline">{phrase}</Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="actors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Key Actors & Influencers</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-gray-50">
                      <th className="text-left p-3">Actor Name</th>
                      <th className="text-left p-3">Role</th>
                      <th className="text-left p-3">Mentions</th>
                      <th className="text-left p-3">Sentiment</th>
                    </tr>
                  </thead>
                  <tbody>
                    {currentThemeData.keyActors.map((actor, index) => (
                      <tr key={index} className="border-b hover:bg-gray-50">
                        <td className="p-3 font-medium">{actor.name}</td>
                        <td className="p-3">{actor.role}</td>
                        <td className="p-3">{actor.mentions.toLocaleString()}</td>
                        <td className="p-3">
                          <Badge className={getSentimentColor(actor.sentiment)}>
                            {getSentimentLabel(actor.sentiment)}
                          </Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sentiment" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Sentiment Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="border-green-200">
                  <CardContent className="p-4 text-center">
                    <div className="text-3xl font-bold text-green-600">{currentThemeData.sentimentBreakdown.positive}%</div>
                    <div className="text-sm text-gray-600">Positive Sentiment</div>
                  </CardContent>
                </Card>
                <Card className="border-yellow-200">
                  <CardContent className="p-4 text-center">
                    <div className="text-3xl font-bold text-yellow-600">{currentThemeData.sentimentBreakdown.neutral}%</div>
                    <div className="text-sm text-gray-600">Neutral Sentiment</div>
                  </CardContent>
                </Card>
                <Card className="border-red-200">
                  <CardContent className="p-4 text-center">
                    <div className="text-3xl font-bold text-red-600">{currentThemeData.sentimentBreakdown.negative}%</div>
                    <div className="text-sm text-gray-600">Negative Sentiment</div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="posts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Most Influential Posts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {currentThemeData.topPosts.map((post, index) => (
                  <Card key={index} className="border-l-4 border-l-purple-500">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{post.author}</span>
                          <Badge variant="outline">{post.platform}</Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge className={getSentimentColor(post.sentiment)}>
                            {getSentimentLabel(post.sentiment)}
                          </Badge>
                          <span className="text-sm text-gray-600">{post.date}</span>
                        </div>
                      </div>
                      <p className="text-gray-700 mb-2">{post.content}</p>
                      <div className="text-sm text-gray-500">
                        {post.engagement.toLocaleString()} engagements
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ThematicIssueReport;