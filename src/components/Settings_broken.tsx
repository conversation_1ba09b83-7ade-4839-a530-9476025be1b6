import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, Dialog<PERSON>ooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Plus, Edit, Trash2, Power, PowerOff, Eye, Download } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

type WebsiteLink = {
  website_id: number;
  name: string;
  url: string;
  date_added: string;
  sublinks?: string | null; // API returns as JSON string or null
  status?: string;
  articles_count?: number;
  processed_count?: number;
};

type WebsiteFormData = {
  name: string;
  url: string;
  sublinks: { id: string; key: string; value: string }[]; // Array with stable IDs
};

type ArticleData = {
  id: number;
  url: string;
  title: string;
  url_hash: string;
  title_hash: string;
  content_hash: string;
  excerpt?: string | null;
  full_content: string;
  content_length: number;
  word_count: number;
  reading_time: string;
  author: string;
  published_date?: string | null;
  category: string;
  tags: string[];
  featured_image: string;
  description: string;
  source_domain: string;
  source_url: string;
  source_page_type: string;
  scraped_at: string;
  scraping_method: string;
  extraction_method: string;
  best_selector: string;
  scraping_success: number;
  scraping_error?: string | null;
  cache_used: number;
  ai_confidence: string;
  is_duplicate: number;
  duplicate_of?: string | null;
  is_active: number;
  quality_score: string;
  created_at: string;
  updated_at: string;
  processed: string;
};

type ProcessedPostData = {
  id: number;
  original_language: string;
  english_translation?: string | null;
  post_summary: string;
  raw_content: string;
  source_url: string;
  timestamp_provided: string;
  platform: string;
  primary_category: string;
  civil_rights_restriction?: string | null;
  electoral_misconduct?: string | null;
  discrimination_type?: string | null;
  law_enforcement_misconduct?: string | null;
  physical_integrity_violation?: string | null;
  gender_based_violence?: string | null;
  political_attacks?: string | null;
  pwa_attacks?: string | null;
  protest_type?: string | null;
  perpetrator: string;
  victims_profile: string;
  gender_of_victim: string;
  district: string | null;
  incident_year: number;
  incident_month: number;
  incident_day: number;
  date_of_incident: string;
  details: string;
  sentiment: string;
  severity_level: string;
  election_relevance: string;
  total_incidents: number;
  main_categories_identified: string[];
  districts_affected: string[];
  time_period: string;
  overall_sentiment: string;
  key_themes: string[];
  sources_mentioned: string[];
  language_detected: string;
  full_analysis_json: any;
  created_at: string;
  updated_at: string;
};

type ArticlesResponse = {
  success: boolean;
  message: string;
  data: {
    articles: ArticleData[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  };  timestamp: string;
};

type ProcessedPostsResponse = {
  success: boolean;
  message: string;
  data: {
    posts: ProcessedPostData[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  };
  timestamp: string;
};

type ApiResponse = {
  success: boolean;
  message: string;
  data: {
    links: WebsiteLink[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  };
  timestamp: string;
};

type FacebookPage = {
  page_id: number;
  page_username: string;
  date_added: string;
  added_by: number;
  posts_count: number;
  election_count: number;
};

type FacebookPageFormData = {
  page_username: string;
};

type FacebookPagesResponse = {
  success: boolean;
  message: string;
  data: {
    pages: FacebookPage[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  };
  timestamp: string;
};

type FacebookPageResponse = {
  success: boolean;
  data: FacebookPage;
  message?: string;
};

type TwitterPage = {
  twitter_page_id: number;
  page_username: string;
  date_added: string;
  posts_count: number;
  election_count: number;
};

type TwitterPageFormData = {
  page_username: string;
};

type TwitterPagesResponse = {
  success: boolean;
  message: string;
  data: {
    pages: TwitterPage[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  };
  timestamp: string;
};

type TwitterPageResponse = {
  success: boolean;
  message: string;
  data: TwitterPage;
  timestamp: string;
};

type FacebookPostData = {
  id: number;
  post_id: string;
  author_name: string;
  author_username: string;
  content: string;
  created_at: string;
  likes_count: number;
  comments_count: number;
  shares_count: number;
  media_urls: string;
  urls: string;
  hashtags: string;
  mentions: string;
  post_url: string;
  post_type: string;
  sentiment?: string | null;
  scraped_at: string;
  raw_json: string;
  processed: string;
};

type FacebookComment = {
  author: string;
  text: string;
  timestamp: string;
  likes: string;
  postUrl: string;
};

type FacebookPostsResponse = {
  success: boolean;
  message: string;
  data: {
    posts: FacebookPostData[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
    page: {
      page_id: number;
      page_username: string;
      date_added: string;
      added_by: number;
      posts_count: number;      election_count: number;
    };
  };
  timestamp: string;
};

type FacebookProcessedPost = {
  id: number;
  original_language: string;
  english_translation: string;
  post_summary: string;
  raw_content: string;
  source_url: string;
  timestamp_provided: string;
  platform: string;
  primary_category: string;
  civil_rights_restriction: string | null;
  electoral_misconduct: string | null;
  discrimination_type: string | null;
  law_enforcement_misconduct: string | null;
  physical_integrity_violation: string | null;
  gender_based_violence: string | null;
  political_attacks: string | null;
  pwa_attacks: string | null;
  protest_type: string | null;
  perpetrator: string;
  victims_profile: string;
  gender_of_victim: string;
  district: string;
  incident_year: number;
  incident_month: number;
  incident_day: number;
  date_of_incident: string;
  details: string;
  sentiment: string;
  severity_level: string;
  election_relevance: string;
  total_incidents: number;
  main_categories_identified: string[];
  districts_affected: string[];
  time_period: string;
  overall_sentiment: string;
  key_themes: string[];
  sources_mentioned: string[];
  language_detected: string;
  full_analysis_json: any;
  created_at: string;
  updated_at: string;
};

type FacebookProcessedPostsResponse = {
  success: boolean;
  message: string;
  data: {
    posts: FacebookProcessedPost[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
    page: {
      page_id: number;
      page_username: string;
      date_added: string;
      added_by: number;
      posts_count: number;
      election_count: number;
    };
  };
  timestamp: string;
};

export const Settings = () => {
  const [activeTab, setActiveTab] = useState('websites');
  const [websiteLinks, setWebsiteLinks] = useState<WebsiteLink[]>([]);  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'add' | 'edit'>('add');
  const [editingWebsite, setEditingWebsite] = useState<WebsiteLink | null>(null);
    // Scraping modal state
  const [isScrapeModalOpen, setIsScrapeModalOpen] = useState(false);
  const [scrapingWebsite, setScrapingWebsite] = useState<WebsiteLink | null>(null);
  const [scrapingUrls, setScrapingUrls] = useState<string[]>([]);
  const [scrapingStates, setScrapingStates] = useState<Record<string, 'idle' | 'scraping' | 'success' | 'error'>>({});
  const [maxArticlesToFetch, setMaxArticlesToFetch] = useState(100);
  const [fetchFullContent, setFetchFullContent] = useState(true);
    const [isViewArticlesModalOpen, setIsViewArticlesModalOpen] = useState(false);
  const [viewingWebsite, setViewingWebsite] = useState<WebsiteLink | null>(null);
  // Facebook Posts Modal state
  const [isViewFacebookPostsModalOpen, setIsViewFacebookPostsModalOpen] = useState(false);
  const [viewingFacebookPage, setViewingFacebookPage] = useState<FacebookPage | null>(null);
  const [facebookPosts, setFacebookPosts] = useState<FacebookPostData[]>([]);
  const [facebookPostsLoading, setFacebookPostsLoading] = useState(false);
  const [expandedComments, setExpandedComments] = useState<Set<number>>(new Set());
  const [facebookProcessedPosts, setFacebookProcessedPosts] = useState<FacebookProcessedPost[]>([]);
  const [facebookProcessedPostsLoading, setFacebookProcessedPostsLoading] = useState(false);
  const [facebookModalActiveTab, setFacebookModalActiveTab] = useState('posts'); // For Facebook modal tabs
  
  const [articles, setArticles] = useState<ArticleData[]>([]);  const [articlesLoading, setArticlesLoading] = useState(false);
  const [processedPosts, setProcessedPosts] = useState<ProcessedPostData[]>([]);
  const [processedPostsLoading, setProcessedPostsLoading] = useState(false);
  const [modalActiveTab, setModalActiveTab] = useState('articles'); // For modal tabs
  
  const [websiteForm, setWebsiteForm] = useState<WebsiteFormData>({
    name: '', 
    url: '', 
    sublinks: [] 
  });

  // Facebook Pages state
  const [facebookPages, setFacebookPages] = useState<FacebookPage[]>([]);
  const [facebookLoading, setFacebookLoading] = useState(false);
  const [isFacebookModalOpen, setIsFacebookModalOpen] = useState(false);
  const [facebookModalMode, setFacebookModalMode] = useState<'add' | 'edit'>('add');
  const [editingFacebookPage, setEditingFacebookPage] = useState<FacebookPage | null>(null);
  const [facebookForm, setFacebookForm] = useState<FacebookPageFormData>({
    page_username: ''
  });
  const [facebookSearchTerm, setFacebookSearchTerm] = useState('');
  const [facebookStats, setFacebookStats] = useState<{
    totalPages: number;
    recentlyAdded: number;
  }>({ totalPages: 0, recentlyAdded: 0 });

  // Twitter Pages state
  const [twitterPages, setTwitterPages] = useState<TwitterPage[]>([]);
  const [twitterLoading, setTwitterLoading] = useState(false);
  const [isTwitterModalOpen, setIsTwitterModalOpen] = useState(false);
  const [twitterModalMode, setTwitterModalMode] = useState<'add' | 'edit'>('add');
  const [editingTwitterPage, setEditingTwitterPage] = useState<TwitterPage | null>(null);
  const [twitterForm, setTwitterForm] = useState<TwitterPageFormData>({
    page_username: ''
  });
  const [twitterSearchTerm, setTwitterSearchTerm] = useState('');
  const [twitterStats, setTwitterStats] = useState<{
    totalPages: number;
    recentlyAdded: number;
    topUsernamePatterns: string[];
  }>({ totalPages: 0, recentlyAdded: 0, topUsernamePatterns: [] });

  // Twitter Posts Modal state
  const [isViewTwitterPostsModalOpen, setIsViewTwitterPostsModalOpen] = useState(false);
  const [viewingTwitterPage, setViewingTwitterPage] = useState<TwitterPage | null>(null);
  const [twitterPosts, setTwitterPosts] = useState<any[]>([]);
  const [twitterPostsLoading, setTwitterPostsLoading] = useState(false);
  const [twitterProcessedPosts, setTwitterProcessedPosts] = useState<any[]>([]);
  const [twitterProcessedPostsLoading, setTwitterProcessedPostsLoading] = useState(false);
  const [twitterModalActiveTab, setTwitterModalActiveTab] = useState<'posts' | 'processed'>('posts');

  const [submitting, setSubmitting] = useState(false);
  const [sublinkKeys, setSublinkKeys] = useState<string[]>([]); // Track keys separately
  const { toast } = useToast();
  // Helper function to parse sublinks from API response
  const parseSublinks = (sublinksString: string | null): { [key: string]: string } => {
    if (!sublinksString) return {};
    try {
      return JSON.parse(sublinksString);
    } catch (error) {
      console.error('Error parsing sublinks:', error);
      return {};
    }
  };

  // Helper function to format sublinks for display
  const getFormattedSublinks = (website: WebsiteLink): { [key: string]: string } => {
    return parseSublinks(website.sublinks);
  };

  // Convert object to array format for form
  const objectToArray = (sublinksObj: { [key: string]: string }): { id: string; key: string; value: string }[] => {
    return Object.entries(sublinksObj).map(([key, value], index) => ({
      id: `sublink-${Date.now()}-${index}`,
      key,
      value
    }));
  };

  // Convert array format back to object for API
  const arrayToObject = (sublinksArray: { id: string; key: string; value: string }[]): { [key: string]: string } => {
    return sublinksArray.reduce((acc, item) => {
      if (item.key.trim()) {
        acc[item.key.trim()] = item.value;
      }
      return acc;
    }, {} as { [key: string]: string });  };

  // Auto-fetch website links when component mounts
  useEffect(() => {
    fetchWebsiteLinks();
  }, []);

  const fetchWebsiteLinks = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:3200/api/website-links');
      const data: ApiResponse = await response.json();
      if (data.success) {
        setWebsiteLinks(data.data.links);
      }
    } catch (error) {
      console.error('Error fetching website links:', error);
      toast({
        title: "Error",
        description: "Failed to fetch website links",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };
  const addWebsiteLink = async () => {
    if (!websiteForm.name.trim() || !websiteForm.url.trim()) {
      toast({
        title: "Validation Error",
        description: "Please fill in both name and URL fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setSubmitting(true);      const payload = {
        name: websiteForm.name.trim(),
        url: websiteForm.url.trim(),
        sublinks: arrayToObject(websiteForm.sublinks)
      };

      const response = await fetch('http://localhost:3200/api/website-links', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        toast({
          title: "Success",
          description: "Website link added successfully",
        });
        resetForm();
        setIsModalOpen(false);
        fetchWebsiteLinks();
      } else {
        throw new Error(data.message || 'Failed to add website link');
      }
    } catch (error) {
      console.error('Error adding website link:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add website link",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const updateWebsiteLink = async () => {
    if (!editingWebsite || !websiteForm.name.trim() || !websiteForm.url.trim()) {
      toast({
        title: "Validation Error",
        description: "Please fill in both name and URL fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setSubmitting(true);      const payload = {
        name: websiteForm.name.trim(),
        url: websiteForm.url.trim(),
        sublinks: arrayToObject(websiteForm.sublinks)
      };

      const response = await fetch(`http://localhost:3200/api/website-links/${editingWebsite.website_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        toast({
          title: "Success",
          description: "Website link updated successfully",
        });
        resetForm();
        setIsModalOpen(false);
        fetchWebsiteLinks();
      } else {
        throw new Error(data.message || 'Failed to update website link');
      }
    } catch (error) {
      console.error('Error updating website link:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update website link",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };  const resetForm = () => {
    setWebsiteForm({ name: '', url: '', sublinks: [] });
    setSublinkKeys([]);
    setEditingWebsite(null);
  };  const addSublink = () => {
    const newId = `sublink-${Date.now()}`;
    setWebsiteForm(prev => ({
      ...prev,
      sublinks: [...prev.sublinks, { id: newId, key: '', value: '' }]
    }));
  };  const removeSublink = (idToRemove: string) => {
    setWebsiteForm(prev => ({
      ...prev,
      sublinks: prev.sublinks.filter(item => item.id !== idToRemove)
    }));
  };
  const updateSublink = (id: string, field: 'key' | 'value', newValue: string) => {
    setWebsiteForm(prev => ({
      ...prev,
      sublinks: prev.sublinks.map(item => 
        item.id === id ? { ...item, [field]: newValue } : item
      )
    }));
  };  const openAddModal = () => {
    setModalMode('add');
    resetForm();
    setIsModalOpen(true);
  };  const openEditModal = (website: WebsiteLink) => {
    setModalMode('edit');
    setEditingWebsite(website);
    const parsedSublinks = parseSublinks(website.sublinks);
    setWebsiteForm({
      name: website.name,
      url: website.url,
      sublinks: objectToArray(parsedSublinks)
    });
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    resetForm();
  };
  const handleSubmit = () => {
    if (modalMode === 'add') {
      addWebsiteLink();
    } else {
      updateWebsiteLink();
    }
  };

  const activateWebsite = async (websiteId: number) => {
    try {
      setSubmitting(true);
      const response = await fetch(`http://localhost:3200/api/website-links/${websiteId}/activate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        toast({
          title: "Success",
          description: "Website activated successfully",
        });
        fetchWebsiteLinks();
      } else {
        throw new Error(data.message || 'Failed to activate website');
      }
    } catch (error) {
      console.error('Error activating website:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to activate website",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const deactivateWebsite = async (websiteId: number) => {
    try {
      setSubmitting(true);
      const response = await fetch(`http://localhost:3200/api/website-links/${websiteId}/deactivate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        toast({
          title: "Success",
          description: "Website deactivated successfully",
        });
        fetchWebsiteLinks();
      } else {
        throw new Error(data.message || 'Failed to deactivate website');
      }
    } catch (error) {
      console.error('Error deactivating website:', error);      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to deactivate website",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const fetchArticlesForWebsite = async (websiteId: number) => {
    try {
      setArticlesLoading(true);
      const response = await fetch(`http://localhost:3200/api/website-links/${websiteId}/articles`);
      const data: ArticlesResponse = await response.json();
      
      if (response.ok && data.success) {
        setArticles(data.data.articles);
      } else {
        throw new Error(data.message || 'Failed to fetch articles');
      }
    } catch (error) {
      console.error('Error fetching articles:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch articles",
        variant: "destructive",
      });
      setArticles([]);    } finally {
      setArticlesLoading(false);
    }
  };

  const fetchProcessedPostsForWebsite = async (websiteId: number) => {
    try {
      setProcessedPostsLoading(true);
      const response = await fetch(`http://localhost:3200/api/website-links/${websiteId}/processed`);
      const data: ProcessedPostsResponse = await response.json();
      
      if (response.ok && data.success) {
        setProcessedPosts(data.data.posts);
      } else {
        throw new Error(data.message || 'Failed to fetch processed posts');
      }
    } catch (error) {
      console.error('Error fetching processed posts:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch processed posts",
        variant: "destructive",
      });
      setProcessedPosts([]);
    } finally {
      setProcessedPostsLoading(false);
    }
  };  const openViewArticlesModal = (website: WebsiteLink) => {
    setViewingWebsite(website);
    setIsViewArticlesModalOpen(true);
    setModalActiveTab('articles'); // Reset to articles tab
    fetchArticlesForWebsite(website.website_id);
    fetchProcessedPostsForWebsite(website.website_id);
  };

  // Facebook Posts Functions
  const fetchFacebookPostsForPage = async (pageId: number) => {
    try {
      setFacebookPostsLoading(true);
      const response = await fetch(`http://localhost:3200/api/facebook-pages/${pageId}/posts`);
      const data: FacebookPostsResponse = await response.json();
      
      if (response.ok && data.success) {
        setFacebookPosts(data.data.posts);
      } else {
        throw new Error(data.message || 'Failed to fetch Facebook posts');
      }
    } catch (error) {
      console.error('Error fetching Facebook posts:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch Facebook posts",
        variant: "destructive",
      });
      setFacebookPosts([]);    } finally {
      setFacebookPostsLoading(false);
    }
  };

  const fetchFacebookProcessedPostsForPage = async (pageId: number) => {
    try {
      setFacebookProcessedPostsLoading(true);
      const response = await fetch(`http://localhost:3200/api/facebook-pages/${pageId}/processed`);
      const data: FacebookProcessedPostsResponse = await response.json();
      
      if (response.ok && data.success) {
        setFacebookProcessedPosts(data.data.posts);
      } else {
        throw new Error(data.message || 'Failed to fetch Facebook processed posts');
      }
    } catch (error) {
      console.error('Error fetching Facebook processed posts:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch Facebook processed posts",
        variant: "destructive",
      });
      setFacebookProcessedPosts([]);
    } finally {
      setFacebookProcessedPostsLoading(false);
    }
  };

  const openViewFacebookPostsModal = (page: FacebookPage) => {
    setViewingFacebookPage(page);
    setIsViewFacebookPostsModalOpen(true);
    setExpandedComments(new Set()); // Reset expanded comments
    setFacebookModalActiveTab('posts'); // Reset to posts tab
    fetchFacebookPostsForPage(page.page_id);
    fetchFacebookProcessedPostsForPage(page.page_id);
  };

  const openViewTwitterPostsModal = (page: TwitterPage) => {
    setViewingTwitterPage(page);
    setIsViewTwitterPostsModalOpen(true);
    setTwitterModalActiveTab('posts'); // Reset to posts tab
    fetchTwitterPostsForPage(page.twitter_page_id);
    fetchTwitterProcessedPostsForPage(page.twitter_page_id);
  };

  const fetchTwitterPostsForPage = async (pageId: number) => {
    setTwitterPostsLoading(true);
    try {
      const response = await fetch(`/api/twitter-pages/${pageId}/posts`);
      if (!response.ok) {
        throw new Error('Failed to fetch Twitter posts');
      }
      const data = await response.json();
      setTwitterPosts(data.data || []);
    } catch (error) {
      console.error('Error fetching Twitter posts:', error);
      toast({
        title: "Error",
        description: "Failed to fetch Twitter posts",
        variant: "destructive",
      });
      setTwitterPosts([]);
    } finally {
      setTwitterPostsLoading(false);
    }
  };

  const fetchTwitterProcessedPostsForPage = async (pageId: number) => {
    setTwitterProcessedPostsLoading(true);
    try {
      const response = await fetch(`/api/twitter-pages/${pageId}/processed`);
      if (!response.ok) {
        throw new Error('Failed to fetch Twitter processed posts');
      }
      const data = await response.json();
      setTwitterProcessedPosts(data.data || []);
    } catch (error) {
      console.error('Error fetching Twitter processed posts:', error);
      toast({
        title: "Error",
        description: "Failed to fetch Twitter processed posts",
        variant: "destructive",
      });
      setTwitterProcessedPosts([]);
    } finally {
      setTwitterProcessedPostsLoading(false);
    }
  };

  // Website scraping functions
  const openScrapeModal = (website: WebsiteLink) => {
    // Implement scraping modal functionality
    console.log('Opening scrape modal for website:', website);
  };

  // Facebook functions
  const fetchFacebookPages = async (searchTerm?: string) => {
    try {
      setLoading(true);
      const url = searchTerm 
        ? `http://localhost:3200/api/facebook-pages?search=${encodeURIComponent(searchTerm)}`
        : 'http://localhost:3200/api/facebook-pages';
      const response = await fetch(url);
      const data = await response.json();
      setFacebookPages(data.data || []);
    } catch (error) {
      console.error('Error fetching Facebook pages:', error);
      toast({
        title: "Error",
        description: "Failed to fetch Facebook pages",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const openFacebookAddModal = () => {
    // Implement Facebook add modal functionality
    console.log('Opening Facebook add modal');
  };

  const handleFacebookSearch = (searchTerm: string) => {
    setFacebookSearchTerm(searchTerm);
  };

  const openFacebookEditModal = (page: FacebookPage) => {
    // Implement Facebook edit modal functionality
    console.log('Opening Facebook edit modal for page:', page);
  };

  const deleteFacebookPage = async (pageId: number) => {
    try {
      const response = await fetch(`http://localhost:3200/api/facebook-pages/${pageId}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        toast({
          title: "Success",
          description: "Facebook page deleted successfully",
        });
        fetchFacebookPages();
      } else {
        throw new Error('Failed to delete Facebook page');
      }
    } catch (error) {
      console.error('Error deleting Facebook page:', error);
      toast({
        title: "Error",
        description: "Failed to delete Facebook page",
        variant: "destructive",
      });
    }  };

  // Twitter functions
  const fetchTwitterPages = async (searchTerm?: string) => {
    try {
      setLoading(true);
      const url = searchTerm 
        ? `http://localhost:3200/api/twitter-pages?search=${encodeURIComponent(searchTerm)}`
        : 'http://localhost:3200/api/twitter-pages';
      const response = await fetch(url);
      const data = await response.json();
      setTwitterPages(data);
    } catch (error) {
      console.error('Error fetching Twitter pages:', error);
    } finally {
      setLoading(false);    }
  };

  const fetchTwitterPosts = async (twitterPageId: number) => {
    try {
      setTwitterPostsLoading(true);
      const response = await fetch(`http://localhost:3200/api/twitter-posts/${twitterPageId}`);
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching Twitter posts:', error);
      return [];
    } finally {
      setTwitterPostsLoading(false);
    }
  };

  const fetchTwitterProcessedPosts = async (twitterPageId: number) => {
    try {
      setTwitterProcessedPostsLoading(true);
      const response = await fetch(`http://localhost:3200/api/twitter-processed-posts/${twitterPageId}`);
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching Twitter processed posts:', error);
      return [];
    } finally {
      setTwitterProcessedPostsLoading(false);
    }
  };
  const openViewTwitterPostsModal = async (twitterPage: any) => {
    setViewingTwitterPage(twitterPage);
    const posts = await fetchTwitterPosts(twitterPage.id);
    const processedPosts = await fetchTwitterProcessedPosts(twitterPage.id);
    setTwitterPosts(posts);
    setTwitterProcessedPosts(processedPosts);
    setIsViewTwitterPostsModalOpen(true);
  };

  // Component JSX starts here
  return (
    <div className="space-y-6">
      <Tabs defaultValue="websites" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="websites">Website Monitoring</TabsTrigger>
          <TabsTrigger value="facebook">Facebook Pages</TabsTrigger>
          <TabsTrigger value="twitter">Twitter Pages</TabsTrigger>
        </TabsList>

        <TabsContent value="websites">
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Website Links</h2>
              <Button onClick={openAddModal} className="inline-flex items-center gap-2">
                <Plus className="w-4 h-4" />
                Add New Website
              </Button>
            </div>
            
            <div className="grid gap-4 sm:grid-cols-2">
              <Input
                placeholder="Search websites..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="max-w-md"
              />
              <div className="flex gap-2">
                <Button 
                  onClick={() => fetchWebsiteLinks()}
                  variant="outline"
                  disabled={loading}
                >
                  {loading ? 'Loading...' : 'Refresh'}
                </Button>
              </div>
            </div>
            
            {loading ? (
              <div className="mt-8 text-center py-8">
                <div className="inline-flex items-center gap-2">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  <p className="text-muted-foreground">Loading websites...</p>
                </div>
              </div>
            ) : websiteLinks.length > 0 ? (
              <Table className="mt-4">
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>URL</TableHead>
                    <TableHead>Sublinks</TableHead>
                    <TableHead>Articles</TableHead>
                    <TableHead>Processed</TableHead>
                    <TableHead>Unprocessed</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Date Added</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>                <TableBody>
                  {websiteLinks.map((link) => (
                    <TableRow key={link.website_id}>
                      <TableCell>{link.website_id}</TableCell>
                      <TableCell>{link.name}</TableCell>
                      <TableCell>
                        <a href={link.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                          {link.url}
                        </a>
                      </TableCell>
                      <TableCell>
                        {(() => {
                          const sublinks = getFormattedSublinks(link);
                          return Object.keys(sublinks).length > 0 ? (
                            <div className="space-y-1">
                              {Object.entries(sublinks).map(([key, value]) => (
                                <div key={key} className="text-xs">
                                  <span className="font-medium">{key}:</span>{' '}
                                  <a href={value} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                                    {value}
                                  </a>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <span className="text-muted-foreground text-sm">No sublinks</span>
                          );
                        })()}
                      </TableCell>
                      <TableCell>{link.articles_count || 0}</TableCell>
                      <TableCell>{link.processed_count || 0}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          (link.articles_count || 0) - (link.processed_count || 0) > 0
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {(link.articles_count || 0) - (link.processed_count || 0)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          link.status === 'active' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {link.status || 'unknown'}
                        </span>
                      </TableCell>
                      <TableCell>{new Date(link.date_added).toLocaleDateString()}</TableCell>                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openViewArticlesModal(link)}
                            disabled={submitting}
                            title="View articles"
                            className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openScrapeModal(link)}
                            disabled={submitting}
                            title="Scrape website"
                            className="text-green-600 hover:text-green-700 hover:bg-green-50"
                          >
                            <Download className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditModal(link)}
                            disabled={submitting}
                            title="Edit website"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          {link.status === 'active' ? (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => deactivateWebsite(link.website_id)}
                              disabled={submitting}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              title="Deactivate website"
                            >
                              <PowerOff className="w-4 h-4" />
                            </Button>
                          ) : (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => activateWebsite(link.website_id)}
                              disabled={submitting}
                              className="text-green-600 hover:text-green-700 hover:bg-green-50"
                              title="Activate website"
                            >
                              <Power className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="mt-8 text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                <div className="text-gray-500">
                  <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 48 48">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M34 40h10v-4a6 6 0 00-10.712-3.714M34 40H14m20 0v-4a9.971 9.971 0 00-.712-3.714M14 40H4v-4a6 6 0 0110.712-3.714M14 40v-4a9.971 9.971 0 01.712-3.714M18 20a6 6 0 1112 0v3.5M16 17.5a4 4 0 118 0v3.5" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No websites added yet</h3>
                  <p className="text-gray-500 mb-4">Start by adding your first website to monitor</p>
                  <Button onClick={openAddModal} className="inline-flex items-center gap-2">
                    <Plus className="w-4 h-4" />
                    Add Your First Website
                  </Button>                </div>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="facebook">
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Facebook Pages</h2>              <Button 
                onClick={() => fetchFacebookPages()}
                variant="outline"
                disabled={facebookLoading}
              >
                {facebookLoading ? 'Loading...' : 'Refresh Facebook Pages'}
              </Button>
            </div>
            
            <div className="mb-4">
              <Button onClick={openFacebookAddModal} className="inline-flex items-center gap-2">
                <Plus className="w-4 h-4" />
                Add New Facebook Page
              </Button>
            </div>
            
            <div className="grid gap-4 sm:grid-cols-2">
              <Input
                placeholder="Search Facebook pages..."
                value={facebookSearchTerm}
                onChange={(e) => handleFacebookSearch(e.target.value)}
                className="sm:max-w-[400px]"
              />
              
              <Button
                onClick={() => fetchFacebookPages(facebookSearchTerm)}
                variant="outline"
                disabled={facebookLoading}
                className="whitespace-nowrap"
              >
                {facebookLoading ? 'Searching...' : 'Search'}
              </Button>
            </div>
            
            <div className="mt-4">
              {facebookLoading ? (
                <div className="text-center py-8">
                  <p>Loading Facebook pages...</p>
                </div>
              ) : facebookPages.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No Facebook pages found. Add a new page to get started.</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <p className="text-sm text-muted-foreground mb-4">
                    Showing {facebookPages.length} Facebook page{facebookPages.length !== 1 ? 's' : ''}
                  </p>                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Page Username</TableHead>
                        <TableHead>Posts</TableHead>
                        <TableHead>Processed</TableHead>
                        <TableHead>Unprocessed</TableHead>
                        <TableHead>Date Added</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {facebookPages.map((page) => (
                        <TableRow key={page.page_id}>
                          <TableCell>{page.page_id}</TableCell>
                          <TableCell>
                            <a 
                              href={`https://www.facebook.com/${page.page_username}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:underline"
                            >
                              {page.page_username}
                            </a>
                          </TableCell>
                          <TableCell>
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {page.posts_count}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              {page.election_count}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                              {page.posts_count - page.election_count}
                            </span>
                          </TableCell>
                          <TableCell>{new Date(page.date_added).toLocaleDateString()}</TableCell>                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openViewFacebookPostsModal(page)}
                                disabled={submitting}
                                title="View Facebook posts"
                                className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                              >
                                <Eye className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openFacebookEditModal(page)}
                                disabled={submitting}
                                title="Edit Facebook page"
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => deleteFacebookPage(page.page_id)}
                                disabled={submitting}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                title="Delete Facebook page"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </div>
            
            {/* Facebook Stats */}
            <div className="mt-8 p-4 bg-white rounded shadow">
              <h3 className="text-md font-medium mb-2">Facebook Pages Stats</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Total Pages Monitored</p>
                  <p className="text-lg font-semibold">{facebookStats.totalPages}</p>
                </div>
                <div>                  <p className="text-sm text-muted-foreground">Recently Added</p>
                  <p className="text-lg font-semibold">{facebookStats.recentlyAdded}</p>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="twitter">
          <div className="mt-4">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Twitter Pages</h2>
                <p className="text-gray-600 mt-1">Manage Twitter pages for social media monitoring</p>
              </div>
              <Button 
                onClick={() => fetchTwitterPages()}
                variant="outline"
                disabled={twitterLoading}
              >
                {twitterLoading ? 'Loading...' : 'Refresh'}
              </Button>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-white p-4 rounded-lg border shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Pages</p>
                    <p className="text-2xl font-bold text-blue-600">{twitterStats.totalPages}</p>
                  </div>
                  <div className="text-blue-500">🐦</div>
                </div>
              </div>
              <div className="bg-white p-4 rounded-lg border shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Recently Added</p>
                    <p className="text-2xl font-bold text-green-600">{twitterStats.recentlyAdded}</p>
                  </div>
                  <div className="text-green-500">📈</div>
                </div>
              </div>
              <div className="bg-white p-4 rounded-lg border shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active</p>
                    <p className="text-2xl font-bold text-purple-600">{twitterPages.length}</p>
                  </div>
                  <div className="text-purple-500">⚡</div>
                </div>
              </div>
            </div>            {/* Top Username Patterns */}
            {twitterStats.topUsernamePatterns.length > 0 && (
              <div className="bg-white p-4 rounded-lg border shadow-sm mb-6">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Popular Username Patterns</h3>
                <div className="flex flex-wrap gap-2">
                  {twitterStats.topUsernamePatterns.map((pattern, index) => (
                    <span key={index} className="px-2 py-1 text-xs rounded bg-blue-100 text-blue-800">
                      {pattern}
                    </span>
                  ))}
                </div>
              </div>
            )}
            
            <Input
              placeholder="Search Twitter pages..."
              value={twitterSearchTerm}
              onChange={(e) => handleTwitterSearch(e.target.value)}
              className="w-full"
            />
            <Button onClick={openTwitterAddModal} className="inline-flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Add Twitter Page
            </Button>

            {/* Loading State */}
            {twitterLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-gray-600">Loading Twitter pages...</span>
              </div>            ) : twitterPages.length > 0 ? (
              /* Twitter Pages Table */
              <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
                <div className="p-4 border-b">
                  <p className="text-sm text-gray-600">
                    Showing {twitterPages.length} Twitter page{twitterPages.length !== 1 ? 's' : ''}
                  </p>
                </div>                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Page Username</TableHead>
                      <TableHead>Posts</TableHead>
                      <TableHead>Processed</TableHead>
                      <TableHead>Unprocessed</TableHead>
                      <TableHead>Date Added</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {twitterPages.map((page) => (
                      <TableRow key={page.twitter_page_id}>
                        <TableCell>{page.twitter_page_id}</TableCell>
                        <TableCell>
                          <a 
                            href={`https://twitter.com/${page.page_username}`} 
                            target="_blank" 
                            rel="noopener noreferrer" 
                            className="text-blue-600 hover:underline"
                          >
                            @{page.page_username}
                          </a>
                        </TableCell>
                        <TableCell>
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {page.posts_count}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {page.election_count}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                            {page.posts_count - page.election_count}
                          </span>
                        </TableCell>
                        <TableCell>{new Date(page.date_added).toLocaleDateString()}</TableCell>                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openViewTwitterPostsModal(page)}
                              disabled={submitting}
                              title="View Twitter posts"
                              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openTwitterEditModal(page)}
                              disabled={submitting}
                              title="Edit Twitter page"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => deleteTwitterPage(page.twitter_page_id)}
                              disabled={submitting}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              title="Delete Twitter page"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              /* Empty State */
              <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                <div className="text-gray-500">
                  <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 48 48">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M34 40h10v-4a6 6 0 00-10.712-3.714M34 40H14m20 0v-4a9.971 9.971 0 00-.712-3.714M14 40H4v-4a6 6 0 0110.712-3.714M14 40v-4a9.971 9.971 0 01.712-3.714M18 20a6 6 0 1112 0v3.5M16 17.5a4 4 0 118 0v3.5" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Twitter pages added yet</h3>
                  <p className="text-gray-500 mb-4">Start by adding your first Twitter page to monitor</p>
                  <Button onClick={openTwitterAddModal} className="inline-flex items-center gap-2">
                    <Plus className="w-4 h-4" />
                    Add Your First Twitter Page
                  </Button>
                </div>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="tiktok">
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <p>TikTok links will be displayed here.</p>
          </div>
        </TabsContent>        <TabsContent value="youtube">
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <p>YouTube links will be displayed here.</p>
          </div>
        </TabsContent>

        <TabsContent value="hootsuite">
          <div className="mt-4">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Hootsuite Integration</h2>
                <p className="text-gray-600 mt-1">Manage Hootsuite data integration for social media monitoring</p>
              </div>
            </div>
            
            <div className="bg-white rounded-lg border shadow-sm p-6">
              <div className="text-center py-12">
                <div className="text-gray-500">
                  <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 48 48">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4a2 2 0 012-2h8a2 2 0 012 2v2m-6 12v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Hootsuite Integration</h3>
                  <p className="text-gray-500 mb-4">Hootsuite data integration will be configured here</p>
                  <p className="text-sm text-gray-400">Coming soon...</p>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>{/* Articles and Processed Posts View Modal */}
      <Dialog open={isViewArticlesModalOpen} onOpenChange={setIsViewArticlesModalOpen}>
        <DialogContent className="sm:max-w-[95vw] max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>
              Content for {viewingWebsite?.name}
            </DialogTitle>
            <DialogDescription>
              View articles and processed posts from {viewingWebsite?.url}
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex-1 overflow-hidden min-h-0">
            <Tabs value={modalActiveTab} onValueChange={setModalActiveTab} className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-2 mb-4">
                <TabsTrigger value="articles">Articles</TabsTrigger>
                <TabsTrigger value="processed-posts">Processed Posts</TabsTrigger>
              </TabsList>
              
              <TabsContent value="articles" className="flex-1 overflow-hidden min-h-0">
                <div className="h-full overflow-y-auto max-h-[60vh]">
                  {articlesLoading ? (
                    <div className="text-center py-8">
                      <p>Loading articles...</p>
                    </div>
                  ) : articles.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">No articles found for this website.</p>
                    </div>                  ) : (
                    <div className="overflow-x-auto">
                      <p className="text-sm text-muted-foreground mb-4">
                        Showing {articles.length} articles
                      </p>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>ID</TableHead>
                            <TableHead>Title</TableHead>
                            <TableHead>Author</TableHead>
                            <TableHead>Category</TableHead>
                            <TableHead>Reading Time</TableHead>
                            <TableHead>Published Date</TableHead>
                            <TableHead>Date Scraped</TableHead>
                            <TableHead>Processed</TableHead>
                            <TableHead>URL</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {articles.map((article) => (
                            <TableRow key={article.id}>
                              <TableCell>{article.id}</TableCell>
                              <TableCell className="max-w-xs">
                                <div className="truncate" title={article.title}>
                                  {article.title}
                                </div>
                                {article.excerpt && (
                                  <div className="text-xs text-muted-foreground mt-1 truncate" title={article.excerpt}>
                                    {article.excerpt}
                                  </div>
                                )}
                              </TableCell>
                              <TableCell>{article.author || 'Unknown'}</TableCell>
                              <TableCell>
                                <span className="px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                                  {article.category}
                                </span>
                              </TableCell>
                              <TableCell>{article.reading_time}</TableCell>
                              <TableCell>
                                {article.published_date ? new Date(article.published_date).toLocaleDateString() : 'N/A'}
                              </TableCell>
                              <TableCell>
                                {new Date(article.scraped_at).toLocaleDateString()}
                              </TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded-full text-xs ${
                                  article.processed === 'yes' 
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-yellow-100 text-yellow-800'
                                }`}>
                                  {article.processed === 'yes' ? 'Processed' : 'Pending'}
                                </span>
                              </TableCell>
                              <TableCell>
                                <a 
                                  href={article.url} 
                                  target="_blank" 
                                  rel="noopener noreferrer" 
                                  className="text-blue-600 hover:underline text-sm"
                                  title={article.url}
                                >
                                  View Article
                                </a>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </div>
              </TabsContent>
                <TabsContent value="processed-posts" className="flex-1 overflow-hidden min-h-0">
                <div className="h-full overflow-y-auto max-h-[60vh]">
                  {facebookProcessedPostsLoading ? (
                    <div className="text-center py-8">
                      <p>Loading processed posts...</p>
                    </div>
                  ) : facebookProcessedPosts.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">No processed posts found for this page.</p>
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <p className="text-sm text-muted-foreground mb-4">
                        Showing {facebookProcessedPosts.length} processed post{facebookProcessedPosts.length !== 1 ? 's' : ''}
                      </p>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>ID</TableHead>
                            <TableHead>Summary</TableHead>
                            <TableHead>Platform</TableHead>
                            <TableHead>Category</TableHead>
                            <TableHead>Severity</TableHead>
                            <TableHead>Electoral Misconduct</TableHead>
                            <TableHead>Political Attacks</TableHead>
                            <TableHead>Law Enforcement Misconduct</TableHead>
                            <TableHead>Perpetrator</TableHead>
                            <TableHead>Victims Profile</TableHead>
                            <TableHead>District</TableHead>
                            <TableHead>Date of Incident</TableHead>
                            <TableHead>Created At</TableHead>
                            <TableHead>Sentiment</TableHead>
                            <TableHead>Election Relevance</TableHead>
                            <TableHead>Key Themes</TableHead>
                            <TableHead>Language Detected</TableHead>
                            <TableHead>Source URL</TableHead>
                          </TableRow>
                        </TableHeader>                        <TableBody>
                          {facebookProcessedPosts.map((post) => (
                            <TableRow key={post.id}>
                              <TableCell className="font-mono text-xs">{post.id}</TableCell>
                              <TableCell className="max-w-sm">
                                <div className="truncate" title={post.post_summary}>
                                  {post.post_summary}
                                </div>
                                <div className="text-xs text-muted-foreground mt-1">
                                  {post.time_period} • {post.total_incidents} incident{post.total_incidents !== 1 ? 's' : ''}
                                </div>
                              </TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded-full text-xs ${
                                  post.platform.toLowerCase() === 'website' 
                                    ? 'bg-purple-100 text-purple-800'
                                    : post.platform.toLowerCase() === 'facebook'
                                    ? 'bg-blue-100 text-blue-800'
                                    : post.platform.toLowerCase() === 'twitter'
                                    ? 'bg-sky-100 text-sky-800'
                                    : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {post.platform}
                                </span>
                              </TableCell>
                              <TableCell className="max-w-xs">
                                <div className="truncate" title={post.primary_category}>
                                  {post.primary_category}
                                </div>
                              </TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded-full text-xs ${
                                  post.severity_level === 'High'
                                    ? 'bg-red-100 text-red-800'
                                    : post.severity_level === 'Medium'
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : 'bg-green-100 text-green-800'
                                }`}>
                                  {post.severity_level}
                                </span>
                              </TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded-full text-xs ${
                                  post.sentiment === 'Positive' 
                                    ? 'bg-green-100 text-green-800'
                                    : post.sentiment === 'Negative'
                                    ? 'bg-red-100 text-red-800'
                                    : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {post.sentiment}
                                </span>
                              </TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded-full text-xs ${
                                  post.election_relevance === 'Direct'
                                    ? 'bg-orange-100 text-orange-800'
                                    : post.election_relevance === 'Indirect'
                                    ? 'bg-amber-100 text-amber-800'
                                    : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {post.election_relevance}
                                </span>
                              </TableCell>
                              <TableCell className="text-xs">
                                {post.created_at ? new Date(post.created_at).toLocaleDateString() : 'N/A'}
                              </TableCell>
                              <TableCell>
                                <div className="max-w-md">
                                  <p className="text-sm line-clamp-3" title={post.english_translation}>
                                    {post.english_translation}
                                  </p>
                                </div>
                              </TableCell>
                              <TableCell className="sticky right-0 bg-white z-10">
                                {post.source_url ? (
                                  <a 
                                    href={post.source_url} 
                                    target="_blank" 
                                    rel="noopener noreferrer" 
                                    className="inline-flex items-center px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
                                    title={post.source_url}
                                  >
                                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                  </svg>
                                  View
                                </a>
                              ) : 'N/A'}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="flex items-center justify-center py-12">
                    <div className="text-center">                      <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 48 48">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M34 40h10v-4a6 6 0 00-10.712-3.714M34 40H14m20 0v-4a9.971 9.971 0 00-.712-3.714M14 40H4v-4a6 6 0 0110.712-3.714M14 40v-4a9.971 9.971 0 01.712-3.714M18 20a6 6 0 1112 0v3.5M16 17.5a4 4 0 118 0v3.5" />
                      </svg>                      <h3 className="text-lg font-medium text-gray-900 mb-2">No processed posts found</h3>                      <p className="text-gray-500">This page doesn't have any processed posts yet.</p>                    </div>
                  </div>
                )}
                </div>
              </TabsContent>
            </Tabs>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsViewFacebookPostsModalOpen(false)}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Twitter Posts Modal */}
      <Dialog open={isViewTwitterPostsModalOpen} onOpenChange={setIsViewTwitterPostsModalOpen}>
        <DialogContent className="sm:max-w-[95vw] max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>
              Twitter Posts for @{viewingTwitterPage?.page_username}
            </DialogTitle>
            <DialogDescription>
              View scraped Twitter posts and processed posts
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex-1 overflow-hidden min-h-0">
            <Tabs value={twitterModalActiveTab} onValueChange={(value) => setTwitterModalActiveTab(value as 'posts' | 'processed')} className="w-full h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="posts" className="flex items-center gap-2">
                  Posts
                  <Badge variant="secondary" className="ml-1">
                    {twitterPosts.length}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger value="processed" className="flex items-center gap-2">
                  Processed
                  <Badge variant="secondary" className="ml-1">
                    {twitterProcessedPosts.length}
                  </Badge>
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="posts" className="flex-1 overflow-hidden mt-4">
                <div className="h-full overflow-y-auto max-h-[60vh]">
                  {twitterPostsLoading ? (
                    <div className="flex items-center justify-center py-12">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      <span className="ml-2 text-gray-600">Loading Twitter posts...</span>
                    </div>
                  ) : twitterPosts.length > 0 ? (
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader className="sticky top-0 bg-white z-10">
                          <TableRow>
                            <TableHead className="w-[80px]">ID</TableHead>
                            <TableHead className="w-[120px]">Author</TableHead>
                            <TableHead className="min-w-[300px]">Content</TableHead>
                            <TableHead className="w-[100px]">Likes</TableHead>
                            <TableHead className="w-[100px]">Retweets</TableHead>
                            <TableHead className="w-[100px]">Replies</TableHead>
                            <TableHead className="w-[120px]">Date</TableHead>
                            <TableHead className="w-[100px]">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {twitterPosts.map((post) => (
                            <TableRow key={post.id}>
                              <TableCell className="font-mono text-xs">{post.id}</TableCell>
                              <TableCell>
                                <div className="text-sm">
                                  <div className="font-medium">{post.author_name}</div>
                                  <div className="text-gray-500">@{post.author_username}</div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="max-w-md">
                                  <p className="text-sm line-clamp-3">{post.content}</p>
                                </div>
                              </TableCell>
                              <TableCell>
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                  ❤️ {post.likes_count || 0}
                                </span>
                              </TableCell>
                              <TableCell>
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  🔄 {post.retweets_count || 0}
                                </span>
                              </TableCell>
                              <TableCell>
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  💬 {post.replies_count || 0}
                                </span>
                              </TableCell>
                              <TableCell className="text-xs">
                                {post.created_at ? new Date(post.created_at).toLocaleDateString() : 'N/A'}
                              </TableCell>
                              <TableCell>
                                {post.post_url ? (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => window.open(post.post_url, '_blank')}
                                    title="View original post"
                                    className="text-blue-600 hover:text-blue-700"
                                  >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                    </svg>
                                  </Button>
                                ) : 'N/A'}
                              </TableCell>
                            </TableRow>
                          ))}                        </TableBody>
                      </Table>
                    </div>
                  ) : (

                    <div className="flex items-center justify-center py-12">
                      <div className="text-center">
                        <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 48 48">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M34 40h10v-4a6 6 0 00-10.712-3.714M34 40H14m20 0v-4a9.971 9.971 0 00-.712-3.714M14 40H4v-4a6 6 0 0110.712-3.714M14 40v-4a9.971 9.971 0 01.712-3.714M18 20a6 6 0 1112 0v3.5M16 17.5a4 4 0 118 0v3.5" />
                        </svg>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No Twitter posts found</h3>
                        <p className="text-gray-500">This page doesn't have any scraped posts yet.</p>
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>
              
              <TabsContent value="processed" className="flex-1 overflow-hidden mt-4">
                <div className="h-full overflow-y-auto max-h-[60vh]">
                  {twitterProcessedPostsLoading ? (
                    <div className="flex items-center justify-center py-12">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      <span className="ml-2 text-gray-600">Loading processed posts...</span>
                    </div>
                  ) : twitterProcessedPosts.length > 0 ? (
                    <div className="overflow-x-auto">
                      <p className="text-sm text-muted-foreground mb-4">
                        Showing {twitterProcessedPosts.length} processed post{twitterProcessedPosts.length !== 1 ? 's' : ''}
                      </p>
                      <Table>
                        <TableHeader className="sticky top-0 bg-white z-10">
                          <TableRow>
                            <TableHead className="w-[80px]">ID</TableHead>
                            <TableHead className="min-w-[300px]">Summary</TableHead>
                            <TableHead className="w-[120px]">Platform</TableHead>
                            <TableHead className="w-[180px]">Category</TableHead>
                            <TableHead className="w-[120px]">Severity</TableHead>
                            <TableHead className="w-[120px]">Sentiment</TableHead>
                            <TableHead className="w-[140px]">Election Relevance</TableHead>
                            <TableHead className="w-[120px]">Created At</TableHead>
                            <TableHead className="w-[100px] sticky right-0 bg-white">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {twitterProcessedPosts.map((post) => (
                            <TableRow key={post.id}>
                              <TableCell className="font-mono text-xs">{post.id}</TableCell>
                              <TableCell className="max-w-sm">
                                <div className="truncate" title={post.post_summary}>
                                  {post.post_summary}
                                </div>
                              </TableCell>
                              <TableCell>
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-sky-100 text-sky-800">
                                  {post.platform}
                                </span>
                              </TableCell>
                              <TableCell className="max-w-xs">
                                <div className="truncate" title={post.primary_category}>
                                  {post.primary_category}
                                </div>
                              </TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded-full text-xs ${
                                  post.severity_level === 'High'
                                    ? 'bg-red-100 text-red-800'
                                    : post.severity_level === 'Medium'
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : 'bg-green-100 text-green-800'
                                }`}>
                                  {post.severity_level}
                                </span>
                              </TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded-full text-xs ${
                                  post.sentiment === 'Positive' 
                                    ? 'bg-green-100 text-green-800'
                                    : post.sentiment === 'Negative'
                                    ? 'bg-red-100 text-red-800'
                                    : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {post.sentiment}
                                </span>
                              </TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded-full text-xs ${
                                  post.election_relevance === 'Direct'
                                    ? 'bg-orange-100 text-orange-800'
                                    : post.election_relevance === 'Indirect'
                                    ? 'bg-amber-100 text-amber-800'
                                    : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {post.election_relevance}
                                </span>
                              </TableCell>
                              <TableCell className="text-xs">
                                {post.created_at ? new Date(post.created_at).toLocaleDateString() : 'N/A'}
                              </TableCell>
                              <TableCell className="sticky right-0 bg-white z-10">
                                {post.source_url ? (
                                  <a 
                                    href={post.source_url} 
                                    target="_blank" 
                                    rel="noopener noreferrer" 
                                    className="inline-flex items-center px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
                                    title={post.source_url}
                                  >
                                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                    </svg>
                                    View
                                  </a>
                                ) : 'N/A'}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center py-12">
                      <div className="text-center">
                        <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 48 48">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M34 40h10v-4a6 6 0 00-10.712-3.714M34 40H14m20 0v-4a9.971 9.971 0 00-.712-3.714M14 40H4v-4a6 6 0 0110.712-3.714M14 40v-4a9.971 9.971 0 01.712-3.714M18 20a6 6 0 1112 0v3.5M16 17.5a4 4 0 118 0v3.5" />
                        </svg>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No processed posts found</h3>
                        <p className="text-gray-500">This page doesn't have any processed posts yet.</p>
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsViewTwitterPostsModalOpen(false)}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};