import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Globe } from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from "recharts";
import { TeamReportData } from '../types/teamReportsTypes';

interface TeamReportViewProps {
  data: TeamReportData | null;
  loading: boolean;
  error: string | null;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

const getSeverityColor = (severity: string) => {
  switch (severity.toLowerCase()) {
    case 'critical': return 'bg-red-100 text-red-800 border-red-200';
    case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'low': return 'bg-green-100 text-green-800 border-green-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getPriorityColor = (priority: string) => {
  switch (priority.toLowerCase()) {
    case 'critical': return 'bg-red-500';
    case 'high': return 'bg-orange-500';
    case 'medium': return 'bg-yellow-500';
    case 'low': return 'bg-green-500';
    default: return 'bg-gray-500';
  }
};

export const TeamReportView: React.FC<TeamReportViewProps> = ({ data, loading, error }) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading team report...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert className="border-red-200 bg-red-50">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="text-red-800">
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  if (!data) {
    return (
      <div className="text-center text-gray-500 py-8">
        No data available
      </div>
    );
  }

  return (    <div className="space-y-6">      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Incidents</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {data.overview?.total_incidents || 
               data.election_analytics?.category_breakdown?.reduce((sum, cat) => sum + cat.count, 0) || 0}
            </div>            <p className="text-xs text-muted-foreground">
              {(data.overview && 'avg_severity' in data.overview) ? 
                `Avg severity: ${parseFloat(data.overview.avg_severity).toFixed(1)}` :
                'Total across all categories'
              }
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {'high_severity_count' in (data.overview || {}) ? 'High Severity' : 'Districts Affected'}
            </CardTitle>
            {('high_severity_count' in (data.overview || {})) ? 
              <AlertTriangle className="h-4 w-4 text-orange-500" /> :
              <Globe className="h-4 w-4 text-muted-foreground" />
            }
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {('high_severity_count' in (data.overview || {})) ? 
                (data.overview as any).high_severity_count :
                (data.overview?.districts_affected || 
                 data.election_analytics?.district_breakdown?.length || 0)
              }
            </div>
            <p className="text-xs text-muted-foreground">
              {('high_severity_count' in (data.overview || {})) ? 
                'Critical incidents' : 'Geographic spread'
              }
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Districts Affected</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {data.overview?.districts_affected || 
               data.election_analytics?.district_breakdown?.length || 0}
            </div>
            <p className="text-xs text-muted-foreground">Geographic spread</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Platforms Monitored</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {data.overview?.platforms_monitored || 'N/A'}
            </div>
            <p className="text-xs text-muted-foreground">Data sources</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Team Focus</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.team.toUpperCase()}</div>
            <p className="text-xs text-muted-foreground">{data.period}</p>
          </CardContent>
        </Card>      </div>

      {/* MESP Specific Key Metrics */}
      {'electoral_misconduct' in data.key_metrics && (
        <div className="grid grid-cols-1 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Electoral Misconduct Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {data.key_metrics.electoral_misconduct.map((item, index) => (
                  <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded-lg">
                    <span className="text-sm font-medium">{item.primary_category}</span>
                    <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                      {item.count}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>      )}      {/* Communications Specific Key Metrics */}
      {'media_restrictions' in data.key_metrics && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Media Restrictions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {data.key_metrics.media_restrictions.length > 0 ? (
                  data.key_metrics.media_restrictions.map((item, index) => (
                    <div key={index} className="flex justify-between items-center p-2 bg-red-50 rounded-lg">
                      <span className="text-sm font-medium">{item.restriction_type}</span>
                      <Badge variant="outline" className="bg-red-100 text-red-700 border-red-200">
                        {item.count}
                      </Badge>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-500">No media restrictions detected</p>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Misinformation Indicators</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {data.key_metrics.misinformation_indicators.length > 0 ? (
                  data.key_metrics.misinformation_indicators.map((item, index) => (
                    <div key={index} className="flex justify-between items-center p-2 bg-yellow-50 rounded-lg">
                      <span className="text-sm font-medium">{item.indicator}</span>
                      <Badge variant="outline" className="bg-yellow-100 text-yellow-700 border-yellow-200">
                        {item.count}
                      </Badge>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-500">No misinformation indicators found</p>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Media Entities</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {data.key_metrics.media_entities.length > 0 ? (
                  data.key_metrics.media_entities.map((item, index) => (
                    <div key={index} className="flex justify-between items-center p-2 bg-blue-50 rounded-lg">
                      <span className="text-sm font-medium">{item.entity}</span>
                      <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-200">
                        {item.count}
                      </Badge>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-500">No media entities tracked</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Recent Incidents Section for Communications */}
      {'media_restrictions' in data.key_metrics && data.recent_incidents && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Communication Incidents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {(Array.isArray(data.recent_incidents) ? data.recent_incidents : []).slice(0, 10).map((incident: any, index) => (
                <div key={incident.id || index} className="border border-gray-200 rounded-lg p-3 bg-white shadow-sm">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex gap-2">
                      <Badge className={getSeverityColor(incident.severity_level)}>
                        {incident.severity_level}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {incident.primary_category}
                      </Badge>
                    </div>
                    <div className="text-right">
                      <div className="text-xs text-gray-500">{incident.district}</div>
                      <div className="text-xs text-gray-400">
                        {new Date(incident.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-gray-700 mb-2">{incident.post_summary}</p>
                  {incident.source_url && (
                    <a 
                      href={incident.source_url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-xs text-blue-600 hover:text-blue-800 underline"
                    >
                      View Source
                    </a>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Peace Building Specific Key Metrics */}
      {'violence_incidents' in data.key_metrics && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Violence Incidents</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {data.key_metrics.violence_incidents.map((item, index) => (
                    <div key={index} className="flex justify-between items-center p-2 bg-orange-50 rounded-lg">
                      <span className="text-sm font-medium">{item.primary_category}</span>
                      <Badge variant="outline" className="bg-orange-100 text-orange-700 border-orange-200">
                        {item.count}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Conflict Keywords</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {data.key_metrics.conflict_keywords.map((item, index) => (
                    <div key={index} className="flex justify-between items-center p-2 bg-blue-50 rounded-lg">
                      <span className="text-sm font-medium">{item.keyword}</span>
                      <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-200">
                        {item.count}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Youth Involvement and Hotspot Analysis */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Youth Involvement</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {data.key_metrics.youth_involvement.map((item, index) => (
                    <div key={index} className="flex justify-between items-center p-2 bg-purple-50 rounded-lg">
                      <span className="text-sm font-medium">{item.keyword}</span>
                      <Badge variant="outline" className="bg-purple-100 text-purple-700 border-purple-200">
                        {item.count}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Hotspot Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-80 overflow-y-auto">
                  {data.key_metrics.hotspot_analysis.slice(0, 5).map((incident, index) => (
                    <div key={incident.id} className="border-l-4 border-orange-500 pl-3 py-2 bg-gray-50 rounded">
                      <div className="flex justify-between items-start mb-1">
                        <Badge className={getSeverityColor(incident.severity_level)}>
                          {incident.severity_level}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {incident.district}
                        </span>
                      </div>
                      <p className="text-xs text-gray-700 line-clamp-2">{incident.post_summary}</p>
                      <p className="text-xs text-gray-500 mt-1">{incident.primary_category}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Recent Incidents Section for Peace Building */}
      {'violence_incidents' in data.key_metrics && data.recent_incidents && (
        <Card>
          <CardHeader>
            <CardTitle>Recent High-Severity Incidents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {(Array.isArray(data.recent_incidents) ? data.recent_incidents : []).slice(0, 10).map((incident: any, index) => (
                <div key={incident.id || index} className="border border-gray-200 rounded-lg p-3 bg-white shadow-sm">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex gap-2">
                      <Badge className={getSeverityColor(incident.severity_level)}>
                        {incident.severity_level}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {incident.primary_category}
                      </Badge>
                    </div>
                    <div className="text-right">
                      <div className="text-xs text-gray-500">{incident.district}</div>
                      <div className="text-xs text-gray-400">
                        {new Date(incident.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-gray-700 mb-2">{incident.post_summary}</p>
                  {incident.source_url && (
                    <a 
                      href={incident.source_url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-xs text-blue-600 hover:text-blue-800 underline"
                    >
                      View Source
                    </a>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Hotspots Section for Peace Building */}
      {'violence_incidents' in data.key_metrics && data.hotspots && (
        <Card>
          <CardHeader>
            <CardTitle>District Hotspots</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {(Array.isArray(data.hotspots) ? data.hotspots : []).map((hotspot: any, index) => (
                <div key={hotspot.district || index} className="border border-gray-200 rounded-lg p-4 bg-white">
                  <h4 className="font-semibold text-lg mb-2">{hotspot.district}</h4>
                  <div className="text-sm text-gray-600 mb-3">
                    <div>Total Incidents: <span className="font-medium">{hotspot.incident_count}</span></div>
                  </div>
                  
                  {hotspot.severity_distribution && (
                    <div className="mb-3">
                      <div className="text-xs text-gray-500 mb-1">Severity Distribution:</div>
                      <div className="flex gap-1">
                        {Object.entries(hotspot.severity_distribution).map(([level, count]: [string, any]) => (
                          count > 0 && (
                            <Badge key={level} className={getSeverityColor(level)} variant="outline">
                              {level}: {count}
                            </Badge>
                          )
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {hotspot.recent_incidents && hotspot.recent_incidents.length > 0 && (
                    <div>
                      <div className="text-xs text-gray-500 mb-1">Recent Incidents:</div>
                      <div className="space-y-1">
                        {hotspot.recent_incidents.slice(0, 2).map((incident: any, idx: number) => (
                          <div key={idx} className="text-xs p-2 bg-gray-50 rounded">
                            <div className="font-medium">{incident.primary_category}</div>
                            <div className="text-gray-600 line-clamp-2">{incident.post_summary}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}      {/* Communications specific charts - Media Metrics */}
      {'media_restrictions' in data.key_metrics && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Media Restrictions Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={data.key_metrics.media_restrictions}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="restriction_type" 
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    fontSize={10}
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={(label) => label}
                    contentStyle={{ fontSize: '12px' }}
                  />
                  <Bar dataKey="count" fill="#dc2626" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Top Media Entities</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={data.key_metrics.media_entities.slice(0, 10)}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="entity" 
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    fontSize={10}
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={(label) => label}
                    contentStyle={{ fontSize: '12px' }}
                  />
                  <Bar dataKey="count" fill="#3b82f6" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Charts - only show if election_analytics is available */}
      {data.election_analytics && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Category Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle>Incident Categories</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={data.election_analytics.category_breakdown.slice(0, 6)}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="primary_category" 
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    fontSize={10}
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={(label) => label}
                    contentStyle={{ fontSize: '12px' }}
                  />
                  <Bar dataKey="count" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* District Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>District Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={data.election_analytics.district_breakdown.slice(0, 8)}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ district, percent }) => 
                      percent > 5 ? `${district || 'Unknown'} ${(percent * 100).toFixed(0)}%` : ''
                    }
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                  >
                    {data.election_analytics.district_breakdown.slice(0, 8).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
              
              {/* Legend */}
              <div className="mt-4 grid grid-cols-2 gap-2">
                {data.election_analytics.district_breakdown.slice(0, 8).map((entry, index) => (
                  <div key={entry.district || 'Unknown'} className="flex items-center gap-2">
                    <div 
                      className="w-4 h-4 rounded-full" 
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    ></div>
                    <span className="text-sm text-gray-700">
                      {entry.district || 'Unknown'} ({entry.count})
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Peace Building specific charts - Violence Incidents and Conflict Keywords */}
      {'violence_incidents' in data.key_metrics && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Violence Incidents Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={data.key_metrics.violence_incidents}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="primary_category" 
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    fontSize={10}
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={(label) => label}
                    contentStyle={{ fontSize: '12px' }}
                  />
                  <Bar dataKey="count" fill="#f97316" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Top Conflict Keywords</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={data.key_metrics.conflict_keywords.slice(0, 10)}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="keyword" 
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    fontSize={10}
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={(label) => label}
                    contentStyle={{ fontSize: '12px' }}
                  />
                  <Bar dataKey="count" fill="#3b82f6" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Trend Analysis */}
      {data.early_warning.trend_data.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Incident Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={data.early_warning.trend_data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="incident_date" 
                  tickFormatter={(date) => new Date(date).toLocaleDateString()}
                />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip 
                  labelFormatter={(date) => new Date(date).toLocaleDateString()}
                />
                <Bar yAxisId="left" dataKey="daily_count" fill="#8884d8" name="Daily Count" />
                <Line 
                  yAxisId="right" 
                  type="monotone" 
                  dataKey="avg_severity" 
                  stroke="#ff7300" 
                  strokeWidth={2}
                  name="Avg Severity"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      )}      {/* Top Keywords and Entities */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Top Keywords</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {/* Handle both RCO (top_keywords) and MESP (key_metrics.governance_issues) structures */}
              {(data.top_keywords || 
                ('governance_issues' in data.key_metrics ? data.key_metrics.governance_issues : [])
              )?.slice(0, 10).map((keyword, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-sm font-medium">{keyword.keyword}</span>
                  <Badge variant="secondary">{keyword.count}</Badge>
                </div>
              ))}
              {(!data.top_keywords && !('governance_issues' in data.key_metrics)) && (
                <p className="text-sm text-gray-500">No keyword data available</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Top Entities</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {/* Handle both RCO (top_entities) and MESP (key_metrics.political_entities) structures */}
              {(data.top_entities || 
                ('political_entities' in data.key_metrics ? data.key_metrics.political_entities : [])
              )?.slice(0, 10).map((entity, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-sm font-medium">{entity.entity}</span>
                  <Badge variant="secondary">{entity.count}</Badge>
                </div>
              ))}
              {(!data.top_entities && !('political_entities' in data.key_metrics)) && (
                <p className="text-sm text-gray-500">No entity data available</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* High Severity Incidents */}
      {data.early_warning.recent_high_severity.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recent High Severity Incidents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.early_warning.recent_high_severity.slice(0, 5).map((incident) => (
                <div key={incident.id} className="border-l-4 border-orange-500 pl-4 py-2">
                  <div className="flex justify-between items-start mb-2">
                    <Badge className={getSeverityColor(incident.severity_level)}>
                      {incident.severity_level}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      {new Date(incident.created_at).toLocaleDateString()}
                    </span>
                  </div>
                  <p className="text-sm mb-2">{incident.post_summary}</p>
                  <div className="flex justify-between items-center text-xs text-gray-600">
                    <span>{incident.primary_category}</span>
                    <span>{incident.district || 'Unknown District'}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}      {/* Recommendations */}
      {data.recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recommendations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.recommendations.map((rec: any, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 border rounded-lg">
                  <div className={`w-3 h-3 rounded-full mt-1 ${getPriorityColor(rec.priority)}`}></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-medium text-sm">{rec.action}</span>
                      <Badge variant="outline" className="text-xs">
                        {rec.priority}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-600 mb-2">{rec.rationale}</p>
                    {rec.target_areas && rec.target_areas.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        <span className="text-xs text-gray-500">Target Areas:</span>
                        {rec.target_areas.map((area: string, idx: number) => (
                          <Badge key={idx} variant="outline" className="text-xs bg-blue-50 text-blue-700">
                            {area}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
