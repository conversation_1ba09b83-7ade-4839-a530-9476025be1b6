import { NavLink } from "react-router-dom";
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  T<PERSON>ding<PERSON><PERSON>, 
  Vote as V<PERSON><PERSON><PERSON>, 
  Alert<PERSON>ctagon, 
  ShieldCheck, 
  Bell, 
  FileText, 
  UserCheck, 
  Settings,
  FolderOpen,
  Radio,
  Sliders
} from "lucide-react";

export const SidebarNavigation = () => {  const navItems = [
    { path: "/dashboard", icon: BarChart3, label: "Dashboard" },
    { path: "/dashboard/analytics", icon: TrendingUp, label: "Analytics" },
    { path: "/dashboard/reports", icon: FileText, label: "Processed Incidences" },
    { path: "/dashboard/all-reports", icon: FolderOpen, label: "Reports" },
    { path: "/dashboard/alerts", icon: Bell, label: "Alerts" },
    { path: "/dashboard/users", icon: UserCheck, label: "System Users" },
    { path: "/dashboard/media-profiles", icon: Radio, label: "Media Profiles/Listen" },
    { path: "/dashboard/platform-settings", icon: Slide<PERSON>, label: "Set<PERSON><PERSON>" },
  ];

  return (
    <div className="space-y-2">
      {navItems.map((item) => {
        const Icon = item.icon;
        return (
          <NavLink
            key={item.path}
            to={item.path}
            end={item.path === "/dashboard"}
            className={({ isActive }) =>
              `w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                isActive ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
              }`
            }
          >
            <Icon className="w-5 h-5" />
            <span>{item.label}</span>
          </NavLink>
        );
      })}
    </div>
  );
};
