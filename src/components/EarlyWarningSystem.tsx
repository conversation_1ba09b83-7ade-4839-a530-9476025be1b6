import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  AlertTriangle,
  Bell,
  MapPin,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Search
} from "lucide-react";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as RechartsTooltip, 
  ResponsiveContainer,
  LineChart,
  Line
} from "recharts";
import SimpleLeafletMap from "@/components/SimpleLeafletMap";
import { useDashboardData } from "@/hooks/useDashboardData";

// Mock data
const earlyWarningAlerts = [
  {
    id: 1,
    type: "Hate Speech",
    severity: "Critical",
    severityColor: "bg-red-100 text-red-800",
    timestamp: "2 hours ago",
    location: "Lilongwe Central",
    source: "Facebook",
    description: "Coordinated hate speech targeting ethnic group in election context detected across multiple platforms.",
    impactScore: 89
  },
  {
    id: 2,
    type: "Misinformation",
    severity: "High",
    severityColor: "bg-orange-100 text-orange-800",
    timestamp: "3 hours ago",
    location: "Blantyre",
    source: "Twitter/X",
    description: "False claims of ballot tampering spreading rapidly with significant engagement.",
    impactScore: 76
  },
  {
    id: 3,
    type: "Inflammatory Content",
    severity: "Medium",
    severityColor: "bg-yellow-100 text-yellow-800",
    timestamp: "5 hours ago",
    location: "Mzuzu",
    source: "WhatsApp",
    description: "Messages inciting protests against election commission being shared in multiple groups.",
    impactScore: 65
  },
  {
    id: 4,
    type: "Violence Indicator",
    severity: "High",
    severityColor: "bg-orange-100 text-orange-800",
    timestamp: "6 hours ago",
    location: "Zomba",
    source: "TikTok",
    description: "Videos showing preparation for post-election civil unrest gaining significant traction.",
    impactScore: 72
  },
  {
    id: 5,
    type: "Foreign Influence",
    severity: "Medium",
    severityColor: "bg-yellow-100 text-yellow-800",
    timestamp: "12 hours ago",
    location: "Multiple Regions",
    source: "Multiple Platforms",
    description: "Coordinated influence campaign from foreign actors detected pushing divisive narratives.",
    impactScore: 61
  }
];

const trendData = [
  { date: "Jan", hateSpeech: 42, misinformation: 53, violence: 24 },
  { date: "Feb", hateSpeech: 47, misinformation: 55, violence: 28 },
  { date: "Mar", hateSpeech: 52, misinformation: 49, violence: 30 },
  { date: "Apr", hateSpeech: 58, misinformation: 62, violence: 35 },
  { date: "May", hateSpeech: 67, misinformation: 68, violence: 42 },
  { date: "Jun", hateSpeech: 63, misinformation: 71, violence: 46 }
];

const regionData = [
  { name: "Lilongwe", incidents: 42 },
  { name: "Blantyre", incidents: 38 },
  { name: "Mzuzu", incidents: 30 },
  { name: "Zomba", incidents: 25 },
  { name: "Kasungu", incidents: 18 },
  { name: "Other", incidents: 22 }
];

export const EarlyWarningSystem = () => {
  const { dashboardData, loading } = useDashboardData(60000);
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Early Warning System</h1>
          <p className="text-gray-600 mt-1">Monitor potential threats to election stability</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" className="bg-white">
            <Bell className="w-4 h-4 mr-2" />
            Configure Alerts
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            Export Warnings
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-red-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Critical Alerts</p>
                <p className="text-3xl font-bold text-red-600 mt-1">12</p>
                <p className="text-xs text-gray-500 mt-1">+3 since yesterday</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">High-risk Areas</p>
                <p className="text-3xl font-bold text-orange-600 mt-1">4</p>
                <p className="text-xs text-gray-500 mt-1">+1 since last week</p>
              </div>
              <MapPin className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Weekly Incidents</p>
                <p className="text-3xl font-bold text-blue-600 mt-1">78</p>
                <p className="text-xs text-gray-500 mt-1">+23% from previous week</p>
              </div>
              <TrendingUp className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Response Rate</p>
                <p className="text-3xl font-bold text-green-600 mt-1">92%</p>
                <p className="text-xs text-gray-500 mt-1">+4% improvement</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="current">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="current">Current Alerts</TabsTrigger>
          <TabsTrigger value="trends">Incident Trends</TabsTrigger>
          <TabsTrigger value="geographic">Geographic Analysis</TabsTrigger>
        </TabsList>
        
        <TabsContent value="current" className="space-y-6">
          <div className="relative">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-gray-800">Active Early Warning Alerts</h2>
              <div className="flex items-center bg-white border rounded-md px-3 py-1">
                <Search className="h-4 w-4 text-gray-400" />
                <input 
                  type="text" 
                  placeholder="Search alerts..." 
                  className="ml-2 outline-none bg-transparent"
                />
              </div>
            </div>
            
            <div className="space-y-4">
              {earlyWarningAlerts.map((alert) => (
                <Card key={alert.id} className="border-l-4 border-l-red-500 overflow-hidden">
                  <CardContent className="p-0">
                    <div className="flex flex-col lg:flex-row">
                      <div className="p-4 lg:p-6 flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <AlertCircle className={
                              alert.severity === "Critical" ? "w-5 h-5 text-red-600 mr-2" :
                              alert.severity === "High" ? "w-5 h-5 text-orange-600 mr-2" :
                              "w-5 h-5 text-yellow-600 mr-2"
                            } />
                            <span className="font-bold text-lg">{alert.type}</span>
                          </div>
                          <Badge className={alert.severityColor}>
                            {alert.severity}
                          </Badge>
                        </div>
                        <p className="text-gray-700 mb-3">{alert.description}</p>
                        <div className="flex flex-wrap gap-y-2">
                          <div className="flex items-center mr-6">
                            <span className="text-gray-600 text-sm mr-1">Location:</span>
                            <span className="text-sm font-medium">{alert.location}</span>
                          </div>
                          <div className="flex items-center mr-6">
                            <span className="text-gray-600 text-sm mr-1">Source:</span>
                            <span className="text-sm font-medium">{alert.source}</span>
                          </div>
                          <div className="flex items-center mr-6">
                            <span className="text-gray-600 text-sm mr-1">Impact Score:</span>
                            <span className="text-sm font-medium">{alert.impactScore}/100</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-gray-600 text-sm mr-1">Detected:</span>
                            <span className="text-sm font-medium">{alert.timestamp}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="bg-gray-50 p-4 lg:p-6 lg:border-l border-gray-200 flex lg:flex-col justify-between items-center lg:w-48">
                        <Button className="w-full bg-blue-600 hover:bg-blue-700 mb-2">
                          View Details
                        </Button>
                        <Button variant="outline" className="w-full">
                          Mark Reviewed
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="trends" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Incident Trend Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={trendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <RechartsTooltip />
                  <Line type="monotone" dataKey="hateSpeech" name="Hate Speech" stroke="#ef4444" strokeWidth={3} />
                  <Line type="monotone" dataKey="misinformation" name="Misinformation" stroke="#f59e0b" strokeWidth={3} />
                  <Line type="monotone" dataKey="violence" name="Violence Indicators" stroke="#7c2d12" strokeWidth={3} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="bg-red-50">
              <CardHeader>
                <CardTitle className="text-red-800">Hate Speech Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-4xl font-bold text-red-700">+50%</div>
                <p className="text-red-600 mt-1">Increase over 30 days</p>
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700">Primary Target:</span>
                    <span className="font-medium">Ethnic Groups</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700">Main Platform:</span>
                    <span className="font-medium">Facebook</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700">Risk Level:</span>
                    <Badge className="bg-red-200 text-red-800">Critical</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-orange-50">
              <CardHeader>
                <CardTitle className="text-orange-800">Misinformation Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-4xl font-bold text-orange-700">+34%</div>
                <p className="text-orange-600 mt-1">Increase over 30 days</p>
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700">Primary Focus:</span>
                    <span className="font-medium">Electoral Process</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700">Main Platform:</span>
                    <span className="font-medium">WhatsApp</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700">Risk Level:</span>
                    <Badge className="bg-orange-200 text-orange-800">High</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-yellow-50">
              <CardHeader>
                <CardTitle className="text-yellow-800">Violence Indicators</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-4xl font-bold text-yellow-700">+92%</div>
                <p className="text-yellow-600 mt-1">Increase over 30 days</p>
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700">Primary Type:</span>
                    <span className="font-medium">Political Violence</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700">Main Platform:</span>
                    <span className="font-medium">Twitter/X</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700">Risk Level:</span>
                    <Badge className="bg-yellow-200 text-yellow-800">Medium</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="geographic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Geographic Distribution of Incidents</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={regionData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <RechartsTooltip />
                  <Bar dataKey="incidents" fill="#3b82f6" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>High-risk Areas</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                    <div>
                      <h3 className="font-bold">Lilongwe Central</h3>
                      <p className="text-sm text-gray-600">Urban district with high political tensions</p>
                    </div>
                    <Badge className="bg-red-100 text-red-800">Critical</Badge>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                    <div>
                      <h3 className="font-bold">Blantyre North</h3>
                      <p className="text-sm text-gray-600">Increased hate speech and misinformation</p>
                    </div>
                    <Badge className="bg-orange-100 text-orange-800">High</Badge>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                    <div>
                      <h3 className="font-bold">Mzuzu Central</h3>
                      <p className="text-sm text-gray-600">Reports of voter intimidation</p>
                    </div>
                    <Badge className="bg-orange-100 text-orange-800">High</Badge>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                    <div>
                      <h3 className="font-bold">Zomba Urban</h3>
                      <p className="text-sm text-gray-600">Emerging tensions between political groups</p>
                    </div>
                    <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Incident Heatmap</CardTitle>
              </CardHeader>
              <CardContent>
                {dashboardData ? (
                  <SimpleLeafletMap 
                    statisticsData={dashboardData.by_district.reduce((acc, item) => {
                      if (item.district && item.district.trim()) {
                        acc[item.district] = item.count;
                      }
                      return acc;
                    }, {} as Record<string, number>)}
                    categoryData={{}}
                    loading={loading}
                    showCategories={false}
                  />
                ) : (
                  <div className="w-full h-[300px] bg-gray-100 rounded-lg flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <MapPin className="w-12 h-12 mx-auto text-gray-400" />
                      <p className="mt-2">Loading map...</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
