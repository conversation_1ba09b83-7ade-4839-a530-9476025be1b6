import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertTriangle, RefreshCw, ExternalLink, ChevronDown, Eye, Table, Grid, Download, FileSpreadsheet, Settings, MoreHorizontal, Filter, ChevronUp } from 'lucide-react';
import * as XLSX from 'xlsx';
import { useIncidents } from '../hooks/useIncidents';
import { 
  MALAWI_DISTRICTS, 
  INCIDENT_CATEGORIES, 
  PLATFORMS, 
  MALAWI_CANDIDATES, 
  POLITICAL_ENTITIES, 
  PROMINENT_NAMES, 
  FREQUENT_PHRASES,
  POLITICAL_ELECTORAL_MISCONDUCT,
  DISCRIMINATION_DISADVANTAGED_GROUPS,
  LAW_ENFORCEMENT_MISCONDUCT,
  PHYSICAL_INTEGRITY_VIOLATIONS,
  GENDER_BASED_VIOLENCE,
  POLITICAL_ATTACKS_HARASSMENT,
  PWA_ATTACKS,
  PROTESTS_DEMONSTRATIONS,
  PERPETRATOR_VICTIM_PROFILES,
  SENTIMENT_OPTIONS,
  REPORT_PERIOD_OPTIONS,
  REPORT_PERIOD_LABELS
} from '../types/incidentsTypes';
import IncidentModerationModal from './IncidentModerationModal';

const getSeverityColor = (severity: string) => {
  switch (severity.toLowerCase()) {
    case 'critical': return 'bg-red-100 text-red-800 border-red-200';
    case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'low': return 'bg-green-100 text-green-800 border-green-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getCategoryColor = (category: string) => {
  switch (category) {
    case 'Political/electoral misconduct': return 'bg-purple-100 text-purple-800';
    case 'Violation of right to physical integrity (violent attacks)': return 'bg-red-100 text-red-800';
    case 'Law enforcement misconduct (Violation of right to liberty and security of persons)': return 'bg-orange-100 text-orange-800';
    case 'Restrictions on civil and political rights': return 'bg-blue-100 text-blue-800';
    case 'Politically motivated attacks/harassment/intimidation/incitement': return 'bg-pink-100 text-pink-800';
    case 'Misinformation': return 'bg-yellow-100 text-yellow-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getPlatformIcon = (platform: string) => {
  switch (platform.toLowerCase()) {
    case 'twitter': return '🐦';
    case 'facebook': return '📘';
    case 'website': return '🌐';
    case 'instagram': return '📷';
    default: return '📱';
  }
};

// Helper functions to extract data from full_analysis_json
const getNestedIncidentData = (incident: any, field: string) => {
  // First check if the field exists at the top level
  if (incident[field] !== undefined && incident[field] !== null) {
    return incident[field];
  }
  
  // Then check in full_analysis_json.incidents[0]
  if (incident.full_analysis_json?.incidents?.[0]?.[field] !== undefined) {
    return incident.full_analysis_json.incidents[0][field];
  }
  
  return null;
};

const getPoliticalContext = (incident: any) => {
  return getNestedIncidentData(incident, 'political_context');
};

const getEntitiesInvolved = (incident: any) => {
  return getNestedIncidentData(incident, 'entities_involved');
};

const getKeywordsRelated = (incident: any) => {
  return getNestedIncidentData(incident, 'keywords_related_to_incident');
};

const getPhysicalIntegrityViolation = (incident: any) => {
  return getNestedIncidentData(incident, 'physical_integrity_violation');
};

const getElectoralMisconduct = (incident: any) => {
  return getNestedIncidentData(incident, 'electoral_misconduct');
};

const getPoliticalAttacks = (incident: any) => {
  return getNestedIncidentData(incident, 'political_attacks');
};

const getLawEnforcementMisconduct = (incident: any) => {
  return getNestedIncidentData(incident, 'law_enforcement_misconduct');
};

const getCivilRightsRestriction = (incident: any) => {
  return getNestedIncidentData(incident, 'civil_rights_restriction');
};

const getDiscriminationType = (incident: any) => {
  return getNestedIncidentData(incident, 'discrimination_type');
};

const getGenderBasedViolence = (incident: any) => {
  return getNestedIncidentData(incident, 'gender_based_violence');
};

const getPwaAttacks = (incident: any) => {
  return getNestedIncidentData(incident, 'pwa_attacks');
};

const getVictimsProfile = (incident: any) => {
  return getNestedIncidentData(incident, 'victims_profile');
};

const getPerpetrator = (incident: any) => {
  return getNestedIncidentData(incident, 'perpetrator');
};

const getSentiment = (incident: any) => {
  return getNestedIncidentData(incident, 'sentiment');
};

const getElectionRelevance = (incident: any) => {
  return getNestedIncidentData(incident, 'election_relevance');
};

const getSourceUrl = (incident: any) => {
  return getNestedIncidentData(incident, 'source') || incident.source_url;
};

const getSourcesMentioned = (incident: any) => {
  // Check in full_analysis_json.analysis_summary.sources_mentioned first
  if (incident.full_analysis_json?.analysis_summary?.sources_mentioned) {
    return incident.full_analysis_json.analysis_summary.sources_mentioned;
  }
  return getNestedIncidentData(incident, 'sources_mentioned');
};

// Component for expandable array display
const ExpandableArray: React.FC<{ items: any[], maxLength: number }> = ({ items, maxLength }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  return (
    <div className="space-y-1">
      {items.slice(0, isExpanded ? items.length : 3).map((item, index) => (
        <div key={index} className="text-xs bg-gray-100 px-2 py-1 rounded">
          {String(item).length > maxLength ? (
            <span title={String(item)} className="cursor-help">
              {String(item).substring(0, maxLength)}...
            </span>
          ) : (
            String(item)
          )}
        </div>
      ))}
      {items.length > 3 && (
        <button 
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-xs text-blue-600 hover:text-blue-800 cursor-pointer font-medium"
        >
          {isExpanded ? `Show less` : `+${items.length - 3} more`}
        </button>
      )}
    </div>
  );
};

// Helper function to render array values or single values with tooltips and expandable content
const renderValue = (value: any, maxLength = 50, isExpandable = false) => {
  if (value === null || value === undefined) return 'N/A';
  
  if (Array.isArray(value)) {
    if (value.length === 0) return 'None';
    
    if (value.length === 1) {
      const str = String(value[0]);
      if (str.length > maxLength) {
        return (
          <span title={str} className="cursor-help">
            {str.substring(0, maxLength)}...
          </span>
        );
      }
      return str;
    }
    
    // For multiple items, use expandable component
    return <ExpandableArray items={value} maxLength={maxLength} />;
  }
  
  if (typeof value === 'object') {
    // Special handling for entities_involved structure
    if (value.victims || value.perpetrators || value.witnesses_sources) {
      return (
        <div className="space-y-1">
          {value.victims && value.victims.length > 0 && (
            <div className="text-xs">
              <span className="font-medium text-blue-600">Victims:</span> {value.victims.join(', ')}
            </div>
          )}
          {value.perpetrators && value.perpetrators.length > 0 && (
            <div className="text-xs">
              <span className="font-medium text-red-600">Perpetrators:</span> {value.perpetrators.join(', ')}
            </div>
          )}
          {value.witnesses_sources && value.witnesses_sources.length > 0 && (
            <div className="text-xs">
              <span className="font-medium text-green-600">Witnesses:</span> {value.witnesses_sources.join(', ')}
            </div>
          )}
        </div>
      );
    }
    
    // Special handling for political_context structure
    if (value.party_involved !== undefined || value.youth_wing_involved !== undefined || value.institutional_failure !== undefined) {
      return (
        <div className="space-y-1">
          {value.party_involved && (
            <div className="text-xs">
              <span className="font-medium text-purple-600">Party:</span> {value.party_involved}
            </div>
          )}
          {value.youth_wing_involved && (
            <div className="text-xs">
              <span className="font-medium text-orange-600">Youth Wing:</span> {value.youth_wing_involved}
            </div>
          )}
          {value.institutional_failure && (
            <div className="text-xs">
              <span className="font-medium text-red-600">Institutional Failure:</span> {value.institutional_failure}
            </div>
          )}
        </div>
      );
    }
    
    // Default object handling with tooltip
    try {
      const str = JSON.stringify(value, null, 2);
      if (str.length > maxLength) {
        return (
          <span title={str} className="cursor-help">
            {str.substring(0, maxLength)}...
          </span>
        );
      }
      return str;
    } catch {
      return String(value);
    }
  }
  
  const str = String(value);
  if (str.length > maxLength) {
    return (
      <span title={str} className="cursor-help">
        {str.substring(0, maxLength)}...
      </span>
    );
  }
  return str;
};

export const MonitoredIncidents: React.FC = () => {
  const [viewMode, setViewMode] = useState<'cards' | 'table'>('cards');
  const [selectedIncident, setSelectedIncident] = useState<any>(null);
  const [showModerationModal, setShowModerationModal] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  const { data, loading, error, filters, updateFilters, loadMore, refetch } = useIncidents({
    limit: 20,
    offset: 0,
    severity: 'all',
    district: 'all',
    report_period: 'all_time'
  });

  // Export functions
  const prepareExportData = () => {
    if (!data?.incidents) return [];
    
    return data.incidents.map((incident) => ({
      ID: incident.id,
      'Post Summary': incident.post_summary,
      'Primary Category': incident.primary_category,
      'Severity Level': incident.severity_level,
      District: incident.district || 'N/A',
      Platform: incident.platform,
      Perpetrator: incident.perpetrator,
      'Victims Profile': incident.victims_profile,
      Details: incident.details,
      'Date of Incident': incident.date_of_incident,
      'Created At': new Date(incident.created_at).toLocaleDateString(),
      'Original Language': getNestedIncidentData(incident, 'original_language') || 'N/A',
      'English Translation': getNestedIncidentData(incident, 'english_translation') || 'N/A',
      'Source URL': getNestedIncidentData(incident, 'source_url') || 'N/A',
      'Civil Rights Restriction': getNestedIncidentData(incident, 'civil_rights_restriction') || 'N/A',
      'Electoral Misconduct': getNestedIncidentData(incident, 'electoral_misconduct') || 'N/A',
      'Discrimination Type': getNestedIncidentData(incident, 'discrimination_type') || 'N/A',
      'Law Enforcement Misconduct': getNestedIncidentData(incident, 'law_enforcement_misconduct') || 'N/A',
      'Gender Based Violence': getNestedIncidentData(incident, 'gender_based_violence') || 'N/A',
      'Political Attacks': getNestedIncidentData(incident, 'political_attacks') || 'N/A',
      'PWA Attacks': getNestedIncidentData(incident, 'pwa_attacks') || 'N/A',
      'Protest Type': getNestedIncidentData(incident, 'protest_type') || 'N/A',
      'Gender of Victim': getNestedIncidentData(incident, 'gender_of_victim') || 'N/A',
      Sentiment: getSentiment(incident) || 'N/A',
      'Election Relevance': getNestedIncidentData(incident, 'election_relevance') || 'N/A',
      'Key Themes': Array.isArray(getNestedIncidentData(incident, 'key_themes')) 
        ? getNestedIncidentData(incident, 'key_themes').join(', ') 
        : getNestedIncidentData(incident, 'key_themes') || 'N/A',
      'Sources Mentioned': Array.isArray(getNestedIncidentData(incident, 'sources_mentioned')) 
        ? getNestedIncidentData(incident, 'sources_mentioned').join(', ') 
        : getNestedIncidentData(incident, 'sources_mentioned') || 'N/A',
      'Language Detected': getNestedIncidentData(incident, 'language_detected') || 'N/A',
      'Alert Level': getNestedIncidentData(incident, 'alert_level') || 'N/A',
      'Risk Assessment': getNestedIncidentData(incident, 'risk_assessment') || 'N/A'
    }));
  };

  const exportToExcel = () => {
    const exportData = prepareExportData();
    if (exportData.length === 0) {
      alert('No data to export');
      return;
    }

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Monitored Incidents');
    
    // Generate filename with current date
    const date = new Date().toISOString().split('T')[0];
    const filename = `monitored_incidents_${date}.xlsx`;
    
    XLSX.writeFile(workbook, filename);
  };

  const exportToCSV = () => {
    const exportData = prepareExportData();
    if (exportData.length === 0) {
      alert('No data to export');
      return;
    }

    // Convert to CSV
    const headers = Object.keys(exportData[0]);
    const csvContent = [
      headers.join(','),
      ...exportData.map(row => 
        headers.map(header => {
          const value = row[header];
          // Escape commas and quotes in CSV
          if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        }).join(',')
      )
    ].join('\n');

    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    
    // Generate filename with current date
    const date = new Date().toISOString().split('T')[0];
    link.setAttribute('download', `monitored_incidents_${date}.csv`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Moderation functions
  const handleViewIncident = (incident: any) => {
    setSelectedIncident(incident);
    setShowModerationModal(true);
  };

  const handleModerateIncident = (incident: any) => {
    setSelectedIncident(incident);
    setShowModerationModal(true);
  };

  const handleSaveIncident = (updatedIncident: any) => {
    // Here you would typically call an API to update the incident
    console.log('Saving incident:', updatedIncident);
    alert('Incident updated successfully! (This is a demo - implement API call)');
    refetch(); // Refresh the data
  };

  const handleDeleteIncident = (incidentId: number) => {
    // Here you would typically call an API to delete the incident
    console.log('Deleting incident:', incidentId);
    alert('Incident deleted successfully! (This is a demo - implement API call)');
    refetch(); // Refresh the data
  };

  if (loading && !data) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading incidents...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert className="border-red-200 bg-red-50">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="text-red-800">
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  if (!data) {
    return (
      <div className="text-center text-gray-500 py-8">
        No incidents data available
      </div>
    );
  }

  return (
    <>
      <IncidentModerationModal
        isOpen={showModerationModal}
        onClose={() => {
          setShowModerationModal(false);
          setSelectedIncident(null);
        }}
        incident={selectedIncident}
        onSave={handleSaveIncident}
        onDelete={handleDeleteIncident}
      />
      
      <div className="space-y-6">
      {/* Filters and Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{data.pagination.total}</div>
            <p className="text-xs text-muted-foreground">Total Incidents</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-red-600">
              {data.incidents.filter(i => i.severity_level === 'High' || i.severity_level === 'Critical').length}
            </div>
            <p className="text-xs text-muted-foreground">High/Critical Severity</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-blue-600">
              {new Set(data.incidents.map(i => i.district).filter(Boolean)).size}
            </div>
            <p className="text-xs text-muted-foreground">Districts Affected</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-green-600">
              {new Set(data.incidents.map(i => i.platform)).size}
            </div>
            <p className="text-xs text-muted-foreground">Platforms Monitored</p>
          </CardContent>
        </Card>
      </div>      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Filter className="w-5 h-5" />
              Filters
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              {showFilters ? (
                <>
                  <ChevronUp className="w-4 h-4" />
                  Hide Filters
                </>
              ) : (
                <>
                  <ChevronDown className="w-4 h-4" />
                  Show Filters
                </>
              )}
            </Button>
          </div>
        </CardHeader>
        {showFilters && (
          <CardContent>
            <div className="space-y-4">
            {/* First Row - Basic Filters */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Severity</label>
                <Select
                  value={filters.severity || 'all'}
                  onValueChange={(value) => updateFilters({ severity: value as any, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Severity Levels</SelectItem>
                    <SelectItem value="Critical">Critical</SelectItem>
                    <SelectItem value="High">High</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="Low">Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">District</label>
                <Select
                  value={filters.district || 'all'}
                  onValueChange={(value) => updateFilters({ district: value, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Districts</SelectItem>
                    {MALAWI_DISTRICTS.map((district) => (
                      <SelectItem key={district} value={district}>
                        {district}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Platform</label>
                <Select
                  value={filters.platform || 'all'}
                  onValueChange={(value) => updateFilters({ platform: value, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Platforms</SelectItem>
                    {PLATFORMS.map((platform) => (
                      <SelectItem key={platform} value={platform}>
                        {platform}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Category</label>
                <Select
                  value={filters.category || 'all'}
                  onValueChange={(value) => updateFilters({ category: value, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {INCIDENT_CATEGORIES.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category.length > 30 ? `${category.substring(0, 30)}...` : category}
                      </SelectItem>
                    ))}                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Second Row - Advanced Filters */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Candidate Name</label>
                <Select
                  value={filters.candidate || 'all'}
                  onValueChange={(value) => updateFilters({ candidate: value, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Candidates</SelectItem>
                    {MALAWI_CANDIDATES.map((candidate) => (
                      <SelectItem key={candidate} value={candidate}>
                        {candidate}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>              <div className="space-y-2">
                <label className="text-sm font-medium">Political Party/CSOs/Movements/Groupings</label>
                <Select
                  value={filters.political_entity || 'all'}
                  onValueChange={(value) => updateFilters({ political_entity: value, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Entities</SelectItem>
                    {POLITICAL_ENTITIES.map((entity) => (
                      <SelectItem key={entity} value={entity}>
                        {entity.length > 35 ? `${entity.substring(0, 35)}...` : entity}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>              <div className="space-y-2">
                <label className="text-sm font-medium">Prominent Names</label>
                <Select
                  value={filters.prominent_name || 'all'}
                  onValueChange={(value) => updateFilters({ prominent_name: value, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Names</SelectItem>
                    {PROMINENT_NAMES.map((name) => (
                      <SelectItem key={name} value={name}>
                        {name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Frequent Phrases</label>
                <Select
                  value={filters.frequent_phrase || 'all'}
                  onValueChange={(value) => updateFilters({ frequent_phrase: value, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Phrases</SelectItem>
                    {FREQUENT_PHRASES.map((phrase) => (
                      <SelectItem key={phrase} value={phrase}>
                        {phrase}
                      </SelectItem>
                    ))}                  </SelectContent>
                </Select>              </div>
            </div>            {/* Third Row - Violation Categories */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Political/electoral misconduct</label>
                <Select
                  value={filters.political_electoral_misconduct || 'all'}
                  onValueChange={(value) => updateFilters({ political_electoral_misconduct: value, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Political/Electoral</SelectItem>
                    {POLITICAL_ELECTORAL_MISCONDUCT.map((item) => (
                      <SelectItem key={item} value={item}>
                        {item.length > 60 ? `${item.substring(0, 60)}...` : item}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Discrimination against disadvantaged groups</label>
                <Select
                  value={filters.discrimination_disadvantaged || 'all'}
                  onValueChange={(value) => updateFilters({ discrimination_disadvantaged: value, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Discrimination</SelectItem>
                    {DISCRIMINATION_DISADVANTAGED_GROUPS.map((item) => (
                      <SelectItem key={item} value={item}>
                        {item.length > 60 ? `${item.substring(0, 60)}...` : item}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Law enforcement misconduct</label>
                <Select
                  value={filters.law_enforcement_misconduct || 'all'}
                  onValueChange={(value) => updateFilters({ law_enforcement_misconduct: value, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Law Enforcement</SelectItem>
                    {LAW_ENFORCEMENT_MISCONDUCT.map((item) => (
                      <SelectItem key={item} value={item}>
                        {item.length > 60 ? `${item.substring(0, 60)}...` : item}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Fourth Row - More Violation Categories */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Physical integrity violations</label>
                <Select
                  value={filters.physical_integrity_violation || 'all'}
                  onValueChange={(value) => updateFilters({ physical_integrity_violation: value, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Physical Violations</SelectItem>
                    {PHYSICAL_INTEGRITY_VIOLATIONS.map((item) => (
                      <SelectItem key={item} value={item}>
                        {item.length > 60 ? `${item.substring(0, 60)}...` : item}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Gender-based violence</label>
                <Select
                  value={filters.gender_based_violence || 'all'}
                  onValueChange={(value) => updateFilters({ gender_based_violence: value, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Gender Violence</SelectItem>
                    {GENDER_BASED_VIOLENCE.map((item) => (
                      <SelectItem key={item} value={item}>
                        {item.length > 60 ? `${item.substring(0, 60)}...` : item}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Political attacks/harassment</label>
                <Select
                  value={filters.political_attacks_harassment || 'all'}
                  onValueChange={(value) => updateFilters({ political_attacks_harassment: value, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Political Attacks</SelectItem>
                    {POLITICAL_ATTACKS_HARASSMENT.map((item) => (
                      <SelectItem key={item} value={item}>
                        {item.length > 60 ? `${item.substring(0, 60)}...` : item}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Fifth Row - Final Violation Categories */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Attacks against PWA</label>
                <Select
                  value={filters.pwa_attacks || 'all'}
                  onValueChange={(value) => updateFilters({ pwa_attacks: value, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All PWA Attacks</SelectItem>
                    {PWA_ATTACKS.map((item) => (
                      <SelectItem key={item} value={item}>
                        {item.length > 60 ? `${item.substring(0, 60)}...` : item}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>              <div className="space-y-2">
                <label className="text-sm font-medium">Protests/Demonstrations</label>
                <Select
                  value={filters.protests_demonstrations || 'all'}
                  onValueChange={(value) => updateFilters({ protests_demonstrations: value, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Protests</SelectItem>
                    {PROTESTS_DEMONSTRATIONS.map((item) => (
                      <SelectItem key={item} value={item}>
                        {item.length > 60 ? `${item.substring(0, 60)}...` : item}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Sixth Row - Perpetrator/Victim Profiles */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Perpetrator Profile</label>
                <Select
                  value={filters.perpetrator_profile || 'all'}
                  onValueChange={(value) => updateFilters({ perpetrator_profile: value, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Perpetrator Profiles</SelectItem>
                    {Object.entries(PERPETRATOR_VICTIM_PROFILES).map(([category, profiles]) => [
                      <div key={`${category}-header`} className="px-2 py-1 text-sm font-semibold text-gray-700 bg-gray-100">
                        {category}
                      </div>,
                      ...profiles.map((profile) => (
                        <SelectItem key={profile} value={profile} className="pl-4">
                          {profile.length > 60 ? `${profile.substring(0, 60)}...` : profile}
                        </SelectItem>
                      ))
                    ]).flat()}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Victim Profile</label>
                <Select
                  value={filters.victim_profile || 'all'}
                  onValueChange={(value) => updateFilters({ victim_profile: value, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Victim Profiles</SelectItem>
                    {Object.entries(PERPETRATOR_VICTIM_PROFILES).map(([category, profiles]) => [
                      <div key={`${category}-header`} className="px-2 py-1 text-sm font-semibold text-gray-700 bg-gray-100">
                        {category}
                      </div>,
                      ...profiles.map((profile) => (
                        <SelectItem key={profile} value={profile} className="pl-4">
                          {profile.length > 60 ? `${profile.substring(0, 60)}...` : profile}
                        </SelectItem>
                      ))
                    ]).flat()}
                  </SelectContent>
                </Select>
              </div>            </div>            {/* Seventh Row - Sentiment Filter */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Sentiment</label>
                <Select
                  value={filters.sentiment || 'all'}
                  onValueChange={(value) => updateFilters({ sentiment: value, offset: 0 })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Sentiments</SelectItem>
                    {SENTIMENT_OPTIONS.map((sentiment) => (
                      <SelectItem key={sentiment} value={sentiment}>
                        {sentiment}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Eighth Row - Report Period Filter */}
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Report Period</label>
                <Select
                  value={filters.report_period || 'all_time'}
                  onValueChange={(value) => updateFilters({ 
                    report_period: value, 
                    offset: 0,
                    // Clear custom dates when switching away from custom
                    ...(value !== 'custom' && { date_from: '', date_to: '' })
                  })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {REPORT_PERIOD_OPTIONS.map((period) => (
                      <SelectItem key={period} value={period}>
                        {REPORT_PERIOD_LABELS[period]}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Date From</label>
                <input
                  type="date"
                  value={filters.date_from || ''}
                  onChange={(e) => updateFilters({ date_from: e.target.value, offset: 0 })}
                  disabled={filters.report_period !== 'custom'}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-md text-sm ${
                    filters.report_period !== 'custom' 
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                      : 'bg-white'
                  }`}
                />
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Date To</label>
                <input
                  type="date"
                  value={filters.date_to || ''}
                  onChange={(e) => updateFilters({ date_to: e.target.value, offset: 0 })}
                  disabled={filters.report_period !== 'custom'}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-md text-sm ${
                    filters.report_period !== 'custom' 
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                      : 'bg-white'
                  }`}
                />
              </div>
            </div>

            {/* Refresh Button */}
            <div className="flex justify-end">
              <Button onClick={refetch} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
            </div>
          </CardContent>
        )}
      </Card>{/* Incidents List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <span>Monitored Incidents</span>
              <Badge variant="outline">
                Showing {data.incidents.length} of {data.pagination.total}
              </Badge>
            </CardTitle>            <div className="flex gap-2">
              <Button 
                variant="outline"
                size="sm"
                onClick={exportToExcel}
                disabled={!data?.incidents?.length}
              >
                <FileSpreadsheet className="h-4 w-4 mr-1" />
                Export Excel
              </Button>
              <Button 
                variant="outline"
                size="sm"
                onClick={exportToCSV}
                disabled={!data?.incidents?.length}
              >
                <Download className="h-4 w-4 mr-1" />
                Export CSV
              </Button>
              <div className="border-l border-gray-300 h-6 mx-1"></div>
              <Button 
                variant={viewMode === 'cards' ? 'default' : 'outline'} 
                size="sm"
                onClick={() => setViewMode('cards')}
              >
                <Grid className="h-4 w-4 mr-1" />
                Cards
              </Button>
              <Button 
                variant={viewMode === 'table' ? 'default' : 'outline'} 
                size="sm"
                onClick={() => setViewMode('table')}
              >
                <Table className="h-4 w-4 mr-1" />
                Table
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {viewMode === 'table' ? (
            <div className="overflow-x-auto">              <table className="w-full border-collapse border border-gray-200">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">ID</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Summary</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Category</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Severity</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">District</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Platform</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Electoral Misconduct</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Political Attacks</th>                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Law Enforcement</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Civil Rights</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Discrimination</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Physical Violence</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Gender Violence</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">PWA Attacks</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Perpetrator</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Victims</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Gender of Victim</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Sentiment</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Election Relevance</th>                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Keyword Density</th>                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Related Keywords</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Entities Involved</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Political Context</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Sources Mentioned</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Incident Date</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Key Themes</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium">Created</th>
                    <th className="border border-gray-200 px-3 py-2 text-left text-sm font-medium sticky right-0 bg-gray-50 z-10">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {data.incidents.map((incident) => (
                    <tr key={incident.id} className="hover:bg-gray-50">
                      <td className="border border-gray-200 px-3 py-2 text-sm">{incident.id}</td>
                      <td className="border border-gray-200 px-3 py-2 text-sm max-w-xs">
                        <div className="truncate" title={incident.post_summary}>
                          {incident.post_summary}
                        </div>
                      </td>
                      <td className="border border-gray-200 px-3 py-2 text-sm">
                        <Badge className={getCategoryColor(incident.primary_category)} variant="outline">
                          {incident.primary_category.length > 20 
                            ? `${incident.primary_category.substring(0, 20)}...` 
                            : incident.primary_category}
                        </Badge>
                      </td>
                      <td className="border border-gray-200 px-3 py-2 text-sm">
                        <Badge className={getSeverityColor(incident.severity_level)}>
                          {incident.severity_level}
                        </Badge>
                      </td>
                      <td className="border border-gray-200 px-3 py-2 text-sm">{incident.district || 'N/A'}</td>
                      <td className="border border-gray-200 px-3 py-2 text-sm">
                        <Badge variant="secondary" className="text-xs">
                          {getPlatformIcon(incident.platform)} {incident.platform}
                        </Badge>
                      </td>                      <td className="border border-gray-200 px-3 py-2 text-sm max-w-xs">
                        <div className="text-xs">
                          {renderValue(getElectoralMisconduct(incident), 25)}
                        </div>
                      </td>
                      <td className="border border-gray-200 px-3 py-2 text-sm max-w-xs">
                        <div className="text-xs">
                          {renderValue(getPoliticalAttacks(incident), 25)}
                        </div>
                      </td>                      <td className="border border-gray-200 px-3 py-2 text-sm max-w-xs">
                        <div className="text-xs">
                          {renderValue(getLawEnforcementMisconduct(incident), 25)}
                        </div>
                      </td>
                      <td className="border border-gray-200 px-3 py-2 text-sm max-w-xs">
                        <div className="text-xs">
                          {renderValue(getCivilRightsRestriction(incident), 25)}
                        </div>
                      </td>
                      <td className="border border-gray-200 px-3 py-2 text-sm max-w-xs">
                        <div className="text-xs">
                          {renderValue(getDiscriminationType(incident), 25)}
                        </div>
                      </td>                      <td className="border border-gray-200 px-3 py-2 text-sm max-w-xs">
                        <div className="text-xs">
                          {renderValue(getPhysicalIntegrityViolation(incident), 25)}
                        </div>
                      </td>
                      <td className="border border-gray-200 px-3 py-2 text-sm max-w-xs">
                        <div className="text-xs">
                          {renderValue(getGenderBasedViolence(incident), 25)}
                        </div>
                      </td>
                      <td className="border border-gray-200 px-3 py-2 text-sm max-w-xs">
                        <div className="text-xs">
                          {renderValue(getPwaAttacks(incident), 25)}
                        </div>
                      </td>
                      <td className="border border-gray-200 px-3 py-2 text-sm max-w-xs">
                        <div className="truncate" title={getPerpetrator(incident)}>
                          {renderValue(getPerpetrator(incident), 30)}
                        </div>
                      </td>
                      <td className="border border-gray-200 px-3 py-2 text-sm max-w-xs">
                        <div className="truncate" title={getVictimsProfile(incident)}>
                          {renderValue(getVictimsProfile(incident), 30)}
                        </div>
                      </td>
                      <td className="border border-gray-200 px-3 py-2 text-sm">
                        <div className="text-xs">
                          {getNestedIncidentData(incident, 'gender_of_victim') || 'N/A'}
                        </div>
                      </td>
                      <td className="border border-gray-200 px-3 py-2 text-sm">
                        <Badge variant="outline" className={
                          getSentiment(incident) === 'Positive' ? 'bg-green-50 text-green-700' :
                          getSentiment(incident) === 'Negative' ? 'bg-red-50 text-red-700' :
                          'bg-gray-50 text-gray-700'
                        }>
                          {getSentiment(incident) || 'N/A'}
                        </Badge>
                      </td>                      <td className="border border-gray-200 px-3 py-2 text-sm">
                        <Badge variant="outline" className={
                          getElectionRelevance(incident) === 'Direct' ? 'bg-red-50 text-red-700' :
                          getElectionRelevance(incident) === 'Indirect' ? 'bg-yellow-50 text-yellow-700' :
                          'bg-blue-50 text-blue-700'
                        }>
                          {getElectionRelevance(incident) || 'N/A'}
                        </Badge>
                      </td>
                      <td className="border border-gray-200 px-3 py-2 text-sm max-w-xs">
                        <div className="text-xs">
                          {renderValue(incident.keyword_density, 25)}
                        </div>
                      </td>                      <td className="border border-gray-200 px-3 py-2 text-sm max-w-xs">
                        <div className="text-xs">
                          {renderValue(getKeywordsRelated(incident), 25, true)}
                        </div>
                      </td>                      <td className="border border-gray-200 px-3 py-2 text-sm max-w-xs">
                        <div className="text-xs">
                          {renderValue(getEntitiesInvolved(incident), 25, true)}
                        </div>
                      </td><td className="border border-gray-200 px-3 py-2 text-sm max-w-xs">
                        <div className="text-xs">
                          {renderValue(getPoliticalContext(incident), 25)}
                        </div>
                      </td>
                      <td className="border border-gray-200 px-3 py-2 text-sm max-w-xs">
                        <div className="text-xs">
                          {renderValue(getSourcesMentioned(incident), 25, true)}
                        </div>
                      </td>
                      <td className="border border-gray-200 px-3 py-2 text-sm">
                        {new Date(incident.date_of_incident).toLocaleDateString()}
                      </td>                      <td className="border border-gray-200 px-3 py-2 text-sm max-w-xs">
                        {renderValue((incident as any).key_themes, 40, true)}
                      </td>
                      <td className="border border-gray-200 px-3 py-2 text-sm">
                        {new Date(incident.created_at).toLocaleDateString()}
                      </td>                      <td className="border border-gray-200 px-3 py-2 text-sm sticky right-0 bg-white z-10 shadow-lg">
                        <div className="flex items-center gap-1">
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleViewIncident(incident)}
                            className="h-7 w-7 p-0"
                            title="View Details"
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleModerateIncident(incident)}
                            className="h-7 w-7 p-0 bg-blue-50 hover:bg-blue-100"
                            title="Moderate"
                          >
                            <Settings className="h-3 w-3" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (            <div className="space-y-4">
              {data.incidents.map((incident) => (
                <div key={incident.id} className="border border-gray-200 rounded-lg p-6 bg-white shadow-sm hover:shadow-md transition-shadow">
                  {/* Header Section */}
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex gap-2 flex-wrap">
                      <Badge className={getSeverityColor(incident.severity_level)}>
                        {incident.severity_level}
                      </Badge>
                      <Badge className={getCategoryColor(incident.primary_category)} variant="outline">
                        {incident.primary_category}
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {getPlatformIcon(incident.platform)} {incident.platform}
                      </Badge>                      <Badge variant="outline" className={
                        getSentiment(incident) === 'Positive' ? 'bg-green-50 text-green-700' :
                        getSentiment(incident) === 'Negative' ? 'bg-red-50 text-red-700' :
                        'bg-gray-50 text-gray-700'
                      }>
                        {getSentiment(incident) || 'N/A'}
                      </Badge>
                      <Badge variant="outline" className={
                        getElectionRelevance(incident) === 'Direct' ? 'bg-red-50 text-red-700' :
                        getElectionRelevance(incident) === 'Indirect' ? 'bg-yellow-50 text-yellow-700' :
                        'bg-blue-50 text-blue-700'
                      }>
                        {getElectionRelevance(incident) || 'N/A'}
                      </Badge>
                    </div>
                    <div className="text-right text-sm text-gray-500">
                      <div>ID: {incident.id}</div>
                      <div>{incident.district || 'N/A'}</div>
                      <div>{new Date(incident.created_at).toLocaleDateString()}</div>
                    </div>
                  </div>

                  {/* Main Content */}
                  <h3 className="font-semibold text-gray-900 mb-3 text-lg">
                    {incident.post_summary}
                  </h3>

                  <div className="text-sm text-gray-600 mb-4 p-3 bg-gray-50 rounded">
                    <strong>Details:</strong> {incident.details}
                  </div>                  {/* Basic Information Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm mb-4">
                    <div>
                      <span className="font-medium text-gray-700">Perpetrator:</span>
                      <div className="text-gray-600 mt-1">{renderValue(getPerpetrator(incident), 80)}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Victims:</span>
                      <div className="text-gray-600 mt-1">{renderValue(getVictimsProfile(incident), 80)}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Gender of Victim:</span>
                      <span className="text-gray-600 block mt-1">{getNestedIncidentData(incident, 'gender_of_victim') || 'Not specified'}</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Incident Date:</span>
                      <span className="text-gray-600 block mt-1">{new Date(incident.date_of_incident).toLocaleDateString()}</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Language:</span>
                      <span className="text-gray-600 block mt-1">{incident.language_detected || incident.original_language || 'N/A'}</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Alert Level:</span>
                      <span className="text-gray-600 block mt-1">{incident.alert_level || 'N/A'}</span>
                    </div>
                  </div>

                  {/* Violation Categories Section */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mb-4 p-3 bg-blue-50 rounded">                    <div>
                      <span className="font-medium text-gray-700">Electoral Misconduct:</span>
                      <div className="text-gray-600 mt-1">{renderValue(getElectoralMisconduct(incident), 80)}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Political Attacks:</span>
                      <div className="text-gray-600 mt-1">{renderValue(getPoliticalAttacks(incident), 80)}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Law Enforcement Misconduct:</span>
                      <div className="text-gray-600 mt-1">{renderValue(getLawEnforcementMisconduct(incident), 80)}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Civil Rights Restriction:</span>
                      <div className="text-gray-600 mt-1">{renderValue(getCivilRightsRestriction(incident), 80)}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Physical Integrity Violation:</span>
                      <div className="text-gray-600 mt-1">{renderValue(getPhysicalIntegrityViolation(incident), 80)}</div>
                    </div>                    <div>
                      <span className="font-medium text-gray-700">Gender-Based Violence:</span>
                      <div className="text-gray-600 mt-1">{renderValue(getGenderBasedViolence(incident), 80)}</div>
                    </div>                    <div>
                      <span className="font-medium text-gray-700">Discrimination Type:</span>
                      <div className="text-gray-600 mt-1">{renderValue(getDiscriminationType(incident), 80)}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">PWA Attacks:</span>
                      <div className="text-gray-600 mt-1">{renderValue(getPwaAttacks(incident), 80)}</div>
                    </div>
                  </div>{/* Entities & Political Context Section */}
                  {(getEntitiesInvolved(incident) || getPoliticalContext(incident)) && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mb-4 p-3 bg-purple-50 rounded">
                      {getEntitiesInvolved(incident) && (
                        <div>
                          <span className="font-medium text-gray-700">Entities Involved:</span>
                          <div className="text-gray-600 mt-1">{renderValue(getEntitiesInvolved(incident), 120)}</div>
                        </div>
                      )}
                      {getPoliticalContext(incident) && (
                        <div>
                          <span className="font-medium text-gray-700">Political Context:</span>
                          <div className="text-gray-600 mt-1">{renderValue(getPoliticalContext(incident), 120)}</div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Keyword Analysis Section */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mb-4 p-3 bg-green-50 rounded">
                    <div>
                      <span className="font-medium text-gray-700">Keyword Density:</span>
                      <div className="text-gray-600 mt-1">{renderValue(incident.keyword_density, 80)}</div>
                    </div>
                  </div>

                  {/* Analysis Fields */}
                  <div className="grid grid-cols-1 gap-3 text-sm mb-4">                    {incident.key_themes && (
                      <div>
                        <span className="font-medium text-gray-700">Key Themes:</span>
                        <div className="text-gray-600 mt-1">{renderValue(incident.key_themes, 120, true)}</div>
                      </div>
                    )}
                    
                    {incident.main_categories_identified && (
                      <div>
                        <span className="font-medium text-gray-700">Categories Identified:</span>
                        <div className="text-gray-600 mt-1">{renderValue(incident.main_categories_identified, 120, true)}</div>
                      </div>
                    )}
                    
                    {incident.districts_affected && (
                      <div>
                        <span className="font-medium text-gray-700">Districts Affected:</span>
                        <div className="text-gray-600 mt-1">{renderValue(incident.districts_affected, 120, true)}</div>
                      </div>
                    )}
                      {getSourcesMentioned(incident) && (
                      <div>
                        <span className="font-medium text-gray-700">Sources:</span>
                        <div className="text-gray-600 mt-1">{renderValue(getSourcesMentioned(incident), 120, true)}</div>
                      </div>
                    )}{incident.entities_involved && (
                      <div>
                        <span className="font-medium text-gray-700">Entities Involved:</span>
                        <div className="text-gray-600 mt-1">{renderValue(incident.entities_involved, 120)}</div>
                      </div>
                    )}

                    {incident.political_context && (
                      <div>
                        <span className="font-medium text-gray-700">Political Context:</span>
                        <div className="text-gray-600 mt-1">{renderValue(incident.political_context, 120)}</div>
                      </div>
                    )}                    {getKeywordsRelated(incident) && (
                      <div>
                        <span className="font-medium text-gray-700">Related Keywords:</span>
                        <div className="text-gray-600 mt-1">{renderValue(getKeywordsRelated(incident), 120, true)}</div>
                      </div>
                    )}

                    {incident.risk_assessment && (
                      <div>
                        <span className="font-medium text-gray-700">Risk Assessment:</span>
                        <div className="text-gray-600 mt-1">{renderValue(incident.risk_assessment, 150)}</div>
                      </div>
                    )}
                  </div>                  {/* Footer Actions */}
                  <div className="flex justify-between items-center mt-4 pt-3 border-t border-gray-100">
                    <div className="flex gap-2">
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => handleViewIncident(incident)}
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        View Details
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => handleModerateIncident(incident)}
                        className="bg-blue-50 hover:bg-blue-100"
                      >
                        <Settings className="h-3 w-3 mr-1" />
                        Moderate
                      </Button>
                      {getSourceUrl(incident) && (
                        <Button size="sm" variant="outline" asChild>
                          <a href={getSourceUrl(incident)} target="_blank" rel="noopener noreferrer">
                            <ExternalLink className="h-3 w-3 mr-1" />
                            Source
                          </a>
                        </Button>
                      )}
                    </div><div className="text-xs text-gray-500 space-y-1 text-right">
                      <div>Platform: {incident.platform}</div>
                      <div>Sentiment: {getSentiment(incident) || 'N/A'}</div>
                      <div>Election Relevance: {getElectionRelevance(incident) || 'N/A'}</div>
                      <div>Total Keywords: {incident.total_keywords_found || 0}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Load More */}
          {data.pagination.has_more && (
            <div className="flex justify-center mt-6">
              <Button onClick={loadMore} variant="outline" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Loading...
                  </>
                ) : (
                  <>
                    <ChevronDown className="h-4 w-4 mr-2" />
                    Load More Incidents
                  </>
                )}
              </Button>
            </div>
          )}

          {/* No more results */}
          {!data.pagination.has_more && data.incidents.length > 0 && (
            <div className="text-center text-gray-500 text-sm mt-6 py-4 border-t border-gray-100">
              You've reached the end of the incidents list
            </div>
          )}
        </CardContent>
      </Card>
    </div>
    </>
  );
};
