import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  MessageCircle, 
  X, 
  Minus, 
  Send, 
  Settings, 
  Trash2, 
  Download,
  Maximize2,
  Minimize2,
  RefreshCw,
  Bot,
  User
} from 'lucide-react';

interface ChatSettingsProps {
  onClose: () => void;
  onClearHistory: () => void;
  onExportChat: () => void;
  isConnected?: boolean;
}

const ChatSettings: React.FC<ChatSettingsProps> = ({ 
  onClose, 
  onClearHistory, 
  onExportChat,
  isConnected = true
}) => {
  return (
    <Card className="w-80 h-96">
      <CardHeader className="p-3 border-b bg-gray-50">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">Chat Settings</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-6 w-6 p-0"
          >
            <X className="w-3 h-3" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium mb-2">Chat Actions</h4>
            <div className="space-y-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onClearHistory}
                className="w-full justify-start"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Clear Chat History
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={onExportChat}
                className="w-full justify-start"
              >
                <Download className="w-4 h-4 mr-2" />
                Export Conversation
              </Button>
            </div>
          </div>

          <Separator />          <div>
            <h4 className="text-sm font-medium mb-2">AI Assistant Info</h4>
            <div className="space-y-2 text-xs text-gray-600">              <div className="flex justify-between">
                <span>Status:</span>
                <Badge variant="secondary" className="text-xs">
                  <div className={`w-2 h-2 rounded-full mr-1 ${
                    isConnected ? 'bg-green-500' : 'bg-red-500'
                  }`} />
                  {isConnected ? 'Connected' : 'Disconnected'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Version:</span>
                <span>v1.0.0</span>
              </div>              <div className="flex justify-between">
                <span>Model:</span>
                <span>gemini-1.5-flash</span>
              </div>              <div className="flex justify-between">
                <span>Platform:</span>
                <span className="text-xs">Magwero - Malawi Media</span>
              </div>
              <div className="flex justify-between">
                <span>Developed By:</span>
                <span className="text-xs">IDT4M - UNDP</span>
              </div>
              <div className="flex justify-between">
                <span>Endpoint:</span>
                <span className="text-xs">localhost:3200</span>
              </div>
            </div>
          </div>

          <Separator />

          <div>
            <h4 className="text-sm font-medium mb-2">Quick Actions</h4>
            <div className="grid grid-cols-2 gap-2">
              <Button variant="outline" size="sm" className="text-xs">
                Help
              </Button>
              <Button variant="outline" size="sm" className="text-xs">
                Feedback
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

interface ChatMessageProps {
  message: {
    id: string;
    text: string;
    sender: 'user' | 'ai';
    timestamp: Date;
  };
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  return (
    <div className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'} mb-3`}>
      {message.sender === 'ai' && (
        <div className="flex-shrink-0 mr-2">
          <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
            <Bot className="w-3 h-3 text-blue-600" />
          </div>
        </div>
      )}
      
      <div className={`max-w-[70%] ${message.sender === 'user' ? 'order-1' : ''}`}>
        <div
          className={`rounded-lg px-3 py-2 text-sm ${
            message.sender === 'user'
              ? 'bg-blue-600 text-white rounded-br-sm'
              : 'bg-gray-100 text-gray-800 rounded-bl-sm border'
          }`}
        >
          <p className="whitespace-pre-wrap">{message.text}</p>
        </div>
        <p className={`text-xs mt-1 ${
          message.sender === 'user' 
            ? 'text-right text-gray-500' 
            : 'text-left text-gray-500'
        }`}>
          {message.timestamp.toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </p>
      </div>

      {message.sender === 'user' && (
        <div className="flex-shrink-0 ml-2 order-2">
          <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
            <User className="w-3 h-3 text-white" />
          </div>
        </div>
      )}
    </div>
  );
};

interface TypingIndicatorProps {
  isVisible: boolean;
}

const TypingIndicator: React.FC<TypingIndicatorProps> = ({ isVisible }) => {
  if (!isVisible) return null;

  return (
    <div className="flex justify-start mb-3">
      <div className="flex-shrink-0 mr-2">
        <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
          <Bot className="w-3 h-3 text-blue-600" />
        </div>
      </div>
      <div className="bg-gray-100 rounded-lg rounded-bl-sm px-3 py-2 border">
        <div className="flex gap-1">
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
        </div>
      </div>
    </div>
  );
};

export { ChatSettings, ChatMessage, TypingIndicator };
