
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertTriangle, Info, CheckCircle, XCircle } from "lucide-react";
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip } from "recharts";

const alerts = [
  {
    id: 1,
    level: "CRITICAL",
    title: "Hate Speech Spike Detected",
    description: "Unusual increase in hate speech content detected in Blantyre region. 347 flagged posts in the last hour.",
    time: "15 minutes ago",
    status: "active",
    icon: AlertTriangle,
    color: "text-red-600",
    bgColor: "bg-red-50",
    borderColor: "border-l-red-500"
  },
  {
    id: 2,
    level: "HIGH",
    title: "Coordinated Inauthentic Behavior",
    description: "Suspicious pattern detected: 23 accounts posting identical content about candidate endorsements.",
    time: "45 minutes ago",
    status: "investigating",
    icon: <PERSON><PERSON><PERSON><PERSON><PERSON>,
    color: "text-orange-600",
    bgColor: "bg-orange-50",
    borderColor: "border-l-orange-500"
  },
  {
    id: 3,
    level: "MEDIUM",
    title: "Trending Topic Alert",
    description: '"#VoterEducation" is trending with 2,345 mentions in the last 2 hours.',
    time: "1 hour ago",
    status: "monitoring",
    icon: Info,
    color: "text-blue-600",
    bgColor: "bg-blue-50",
    borderColor: "border-l-blue-500"
  },
  {
    id: 4,
    level: "HIGH",
    title: "Violence Indicators Detected",
    description: "Multiple posts containing violence-related keywords detected in political discussions.",
    time: "2 hours ago",
    status: "escalated",
    icon: AlertTriangle,
    color: "text-orange-600",
    bgColor: "bg-orange-50",
    borderColor: "border-l-orange-500"
  },
  {
    id: 5,
    level: "RESOLVED",
    title: "Misinformation Campaign",
    description: "False voter registration information campaign has been contained and reported.",
    time: "3 hours ago",
    status: "resolved",
    icon: CheckCircle,
    color: "text-green-600",
    bgColor: "bg-green-50",
    borderColor: "border-l-green-500"
  }
];

const alertDistribution = [
  { name: "Critical", value: 8, color: "#dc2626" },
  { name: "High", value: 23, color: "#ea580c" },
  { name: "Medium", value: 45, color: "#d97706" },
  { name: "Resolved", value: 12, color: "#16a34a" }
];

export const AlertCard = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Alert Management</h1>
          <p className="text-gray-600 mt-2">Monitor and respond to real-time threats</p>
        </div>
      </div>

      {/* Alert Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Alert Level:</label>
              <Select defaultValue="all">
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Status:</label>
              <Select defaultValue="all">
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="investigating">Investigating</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <Button className="bg-blue-600 hover:bg-blue-700">
              Create Alert Rule
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Alert List */}
        <div className="lg:col-span-2 space-y-4">
          {alerts.map((alert) => {
            const IconComponent = alert.icon;
            
            return (
              <Card key={alert.id} className={`border-l-4 ${alert.borderColor} ${alert.bgColor}`}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <div className={`p-2 rounded-lg ${alert.bgColor}`}>
                        <IconComponent className={`w-6 h-6 ${alert.color}`} />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <Badge 
                            variant={alert.level === "CRITICAL" ? "destructive" : 
                                   alert.level === "HIGH" ? "default" :
                                   alert.level === "MEDIUM" ? "secondary" : "outline"}
                            className={
                              alert.level === "CRITICAL" ? "bg-red-600" :
                              alert.level === "HIGH" ? "bg-orange-600" :
                              alert.level === "MEDIUM" ? "bg-blue-600" :
                              "bg-green-600 text-white"
                            }
                          >
                            {alert.level}
                          </Badge>
                          <span className="text-sm text-gray-500">{alert.time}</span>
                        </div>
                        
                        <h3 className="font-semibold text-gray-900 mb-2">{alert.title}</h3>
                        <p className="text-gray-600 text-sm mb-3">{alert.description}</p>
                        
                        <div className="flex space-x-2">
                          {alert.status === "active" && (
                            <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                              Investigate
                            </Button>
                          )}
                          {alert.status === "investigating" && (
                            <Button size="sm" variant="outline">
                              Review
                            </Button>
                          )}
                          {alert.status === "monitoring" && (
                            <Button size="sm" variant="outline">
                              Monitor
                            </Button>
                          )}
                          {alert.status === "escalated" && (
                            <Button size="sm" variant="outline">
                              Escalate
                            </Button>
                          )}
                          {alert.status === "resolved" && (
                            <Button size="sm" variant="outline">
                              View Details
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
        
        {/* Alert Statistics */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Alert Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={200}>
                <PieChart>
                  <Pie
                    data={alertDistribution}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {alertDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
              
              <div className="mt-4 space-y-2">
                {alertDistribution.map((item, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: item.color }}
                      />
                      <span>{item.name} Alerts</span>
                    </div>
                    <span className="font-semibold">{item.value}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">23</div>
                <div className="text-sm text-gray-600">High Priority</div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">45</div>
                <div className="text-sm text-gray-600">Medium Priority</div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">12</div>
                <div className="text-sm text-gray-600">Resolved Today</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
