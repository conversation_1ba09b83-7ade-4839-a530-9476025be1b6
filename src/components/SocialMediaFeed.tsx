
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Heart, MessageCircle, Repeat2, MapPin, Search } from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";

const socialPosts = [
  {
    id: 1,
    user: "<PERSON>",
    username: "johndo<PERSON>",
    platform: "Facebook",
    avatar: "JD",
    time: "2 minutes ago",
    content: "Excited to participate in the upcoming elections! Democracy in action 🗳️ #MalawiElections2025",
    sentiment: "Positive",
    location: "Lilongwe",
    engagement: {
      likes: 234,
      comments: 45,
      shares: 12
    }
  },
  {
    id: 2,
    user: "<PERSON>",
    username: "mariak",
    platform: "Twitter/X",
    avatar: "M<PERSON>",
    time: "5 minutes ago",
    content: "Frustrated with the current political situation. We need real change! #Change2025",
    sentiment: "Negative",
    location: "Blantyre",
    engagement: {
      likes: 89,
      comments: 156,
      shares: 23
    }
  },
  {
    id: 3,
    user: "<PERSON> Mwanza",
    username: "peterm",
    platform: "TikTok",
    avatar: "PM",
    time: "8 minutes ago",
    content: "Here's what you need to know about voter registration for the 2025 elections 📊",
    sentiment: "Neutral",
    location: "Mzuzu",
    engagement: {
      likes: 1200,
      comments: 234,
      shares: 89
    }
  }
];

const liveStats = [
  { time: "5 min ago", posts: 32 },
  { time: "4 min ago", posts: 28 },
  { time: "3 min ago", posts: 35 },
  { time: "2 min ago", posts: 41 },
  { time: "1 min ago", posts: 38 },
  { time: "Now", posts: 42 }
];

export const SocialMediaFeed = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Live Social Media Monitoring</h1>
        <p className="text-gray-600 mt-2">Real-time feed of social media activity</p>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Platform:</label>
              <Select defaultValue="all">
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Platforms</SelectItem>
                  <SelectItem value="facebook">Facebook</SelectItem>
                  <SelectItem value="twitter">Twitter/X</SelectItem>
                  <SelectItem value="tiktok">TikTok</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Sentiment:</label>
              <Select defaultValue="all">
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sentiments</SelectItem>
                  <SelectItem value="positive">Positive</SelectItem>
                  <SelectItem value="negative">Negative</SelectItem>
                  <SelectItem value="neutral">Neutral</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center space-x-2 flex-1 max-w-md">
              <Search className="w-4 h-4 text-gray-400" />
              <Input placeholder="Search keywords..." className="flex-1" />
            </div>
            
            <Button className="bg-blue-600 hover:bg-blue-700">
              Apply Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Social Media Feed */}
        <div className="lg:col-span-2 space-y-4">
          {socialPosts.map((post) => (
            <Card key={post.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  {/* Avatar */}
                  <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                    {post.avatar}
                  </div>
                  
                  <div className="flex-1">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="font-semibold text-gray-900">{post.user}</span>
                        <Badge variant="secondary" className="text-xs">
                          {post.platform}
                        </Badge>
                        <span className="text-sm text-gray-500">• {post.time}</span>
                      </div>
                      
                      <Badge 
                        variant={post.sentiment === "Positive" ? "default" : 
                               post.sentiment === "Negative" ? "destructive" : "secondary"}
                        className={
                          post.sentiment === "Positive" ? "bg-green-600" :
                          post.sentiment === "Negative" ? "bg-red-600" :
                          "bg-gray-600"
                        }
                      >
                        {post.sentiment}
                      </Badge>
                    </div>
                    
                    {/* Content */}
                    <p className="text-gray-700 mb-3">{post.content}</p>
                    
                    {/* Location */}
                    <div className="flex items-center space-x-1 text-sm text-gray-500 mb-3">
                      <MapPin className="w-4 h-4" />
                      <span>{post.location}</span>
                    </div>
                    
                    {/* Engagement */}
                    <div className="flex items-center space-x-6 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Heart className="w-4 h-4" />
                        <span>{post.engagement.likes.toLocaleString()} likes</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <MessageCircle className="w-4 h-4" />
                        <span>{post.engagement.comments} comments</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Repeat2 className="w-4 h-4" />
                        <span>{post.engagement.shares} shares</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        {/* Live Statistics */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Live Feed Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={200}>
                <LineChart data={liveStats}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <Tooltip />
                  <Line 
                    type="monotone" 
                    dataKey="posts" 
                    stroke="#3b82f6" 
                    strokeWidth={3}
                    dot={{ fill: "#3b82f6", strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
              
              <div className="mt-4 text-center">
                <div className="text-2xl font-bold text-blue-600">42</div>
                <div className="text-sm text-gray-600">Posts per minute</div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">45</div>
                <div className="text-sm text-gray-600">Positive Posts</div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-600">35</div>
                <div className="text-sm text-gray-600">Neutral Posts</div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">20</div>
                <div className="text-sm text-gray-600">Negative Posts</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
