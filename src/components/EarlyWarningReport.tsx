import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  AlertTriangle,
  Bell,
  MapPin,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Search,
  Download,
  RefreshCw,
  Eye,
  BarChart3,
  Globe
} from "lucide-react";
import { 
  <PERSON><PERSON><PERSON>, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as RechartsTooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from "recharts";
import SimpleLeafletMap from "@/components/SimpleLeafletMap";
import { apiService } from "@/services/apiService";

interface EarlyWarningReportData {
  success: boolean;
  data: {
    overview: {
      criticalAlerts: {
        current: number;
        change: string;
        changeDirection: string;
        changePeriod: string;
      };
      highRiskAreas: {
        current: number;
        change: string;
        changeDirection: string;
        changePeriod: string;
      };
      weeklyIncidents: {
        current: number;
        change: string;
        changeDirection: string;
        changePeriod: string;
      };
      responseRate: {
        current: number;
        change: string;
        changeDirection: string;
        changePeriod: string;
      };
    };
    alerts: Array<{
      id: number;
      type: string;
      severity: string;
      severityColor: string;
      timestamp: string;
      relativeTime: string;
      location: string;
      coordinates: {
        latitude: number;
        longitude: number;
      };
      source: string;
      description: string;
      impactScore: number;
      status: string;
      priority: number;
      tags: string[];
      affectedPopulation: number;
      verificationStatus: string;
      totalPosts: number;
      evidencePosts: Array<{
        id: string;
        platform: string;
        content: string;
        timestamp: string;
        engagement: any;
        reach: any;
        sentiment: number;
        toxicityScore: number;
        location: string;
        language: string;
        detectionMethods: string[];
        riskFactors: string[];
        status: string;
      }>;
      actions: Array<{
        action: string;
        timestamp: string;
        actor: string;
        details?: string;
        postsAffected?: number;
      }>;
      analysisMetrics: {
        totalEngagement: number;
        uniqueAccounts: number;
        networkAnalysis: {
          coordinatedBehavior: boolean;
          botLikelihood: number;
          influenceNetwork: string;
        };
        geographicSpread: string[];
        peakViralityTime: string;
      };
    }>;
    trendData: Array<{
      date: string;
      hateSpeeech: number;
      misinformation: number;
      violence: number;
      fraud: number;
    }>;
    regionData: Array<{
      name: string;
      total: number;
      critical: number;
      high: number;
      medium: number;
      low: number;
      coordinates: {
        latitude: number;
        longitude: number;
      };
    }>;
    alertTypes: Array<{
      name: string;
      count: number;
      avgSeverity: string;
      color: string;
    }>;
    recentPosts: {
      description: string;
      posts: Array<{
        id: string;
        platform: string;
        content: string;
        timestamp: string;
        alertType: string;
        severity: string;
        location: string;
        engagement: any;
      }>;
    };
    metadata: {
      lastUpdated: string;
      updateFrequency: string;
      totalAlerts: number;
      activeAlerts: number;
      resolvedAlerts: number;
      dataSource: string;
      confidenceLevel: number;
      postsAnalyzed: number;
      postsInLast24Hours: number;
    };
  };
}

export const EarlyWarningReport = () => {
  const [data, setData] = useState<EarlyWarningReportData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [selectedAlert, setSelectedAlert] = useState<any>(null);

  useEffect(() => {
    fetchEarlyWarningReport();
  }, []);

  const fetchEarlyWarningReport = async () => {
    try {
      setLoading(true);
      const response = await apiService.getEarlyWarningReport();
      setData(response);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch early warning report'));
    } finally {
      setLoading(false);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto text-blue-600" />
          <p className="mt-2 text-gray-600">Loading Early Warning Report...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <AlertTriangle className="w-12 h-12 mx-auto text-red-500 mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Report</h3>
        <p className="text-gray-600 mb-4">{error.message}</p>
        <Button onClick={fetchEarlyWarningReport} variant="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  if (!data?.success || !data?.data) {
    return (
      <div className="text-center py-8">
        <AlertTriangle className="w-12 h-12 mx-auto text-yellow-500 mb-4" />
        <h3 className="text-lg font-semibold text-gray-900">No Data Available</h3>
        <p className="text-gray-600">Early warning report data is not available at this time.</p>
      </div>
    );
  }

  const reportData = data.data;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Early Warning Report</h1>
          <p className="text-gray-600 mt-1">Comprehensive analysis of potential election threats</p>
          <p className="text-sm text-gray-500 mt-1">
            Last updated: {formatTimestamp(reportData.metadata.lastUpdated)} • 
            {reportData.metadata.dataSource} • 
            Confidence: {Math.round(reportData.metadata.confidenceLevel * 100)}%
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={fetchEarlyWarningReport}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-red-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Critical Alerts</p>
                <p className="text-3xl font-bold text-red-600 mt-1">
                  {reportData.overview.criticalAlerts.current}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {reportData.overview.criticalAlerts.change} {reportData.overview.criticalAlerts.changePeriod}
                </p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">High-risk Areas</p>
                <p className="text-3xl font-bold text-orange-600 mt-1">
                  {reportData.overview.highRiskAreas.current}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {reportData.overview.highRiskAreas.change} {reportData.overview.highRiskAreas.changePeriod}
                </p>
              </div>
              <MapPin className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Weekly Incidents</p>
                <p className="text-3xl font-bold text-blue-600 mt-1">
                  {reportData.overview.weeklyIncidents.current}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {reportData.overview.weeklyIncidents.change} {reportData.overview.weeklyIncidents.changePeriod}
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Response Rate</p>
                <p className="text-3xl font-bold text-green-600 mt-1">
                  {reportData.overview.responseRate.current}%
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {reportData.overview.responseRate.change} {reportData.overview.responseRate.changePeriod}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="alerts" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="alerts">Active Alerts</TabsTrigger>
          <TabsTrigger value="trends">Trend Analysis</TabsTrigger>
          <TabsTrigger value="geographic">Geographic View</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="alerts" className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-800">Active Early Warning Alerts</h2>
            <div className="flex items-center bg-white border rounded-md px-3 py-1">
              <Search className="h-4 w-4 text-gray-400" />
              <input 
                type="text" 
                placeholder="Search alerts..." 
                className="ml-2 outline-none bg-transparent"
              />
            </div>
          </div>
          
          <div className="space-y-4">
            {reportData.alerts.map((alert) => (
              <Card key={alert.id} className="border-l-4 border-l-red-500 overflow-hidden">
                <CardContent className="p-0">
                  <div className="flex flex-col lg:flex-row">
                    <div className="p-4 lg:p-6 flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <AlertCircle className={
                            alert.severity === "Critical" ? "w-5 h-5 text-red-600 mr-2" :
                            alert.severity === "High" ? "w-5 h-5 text-orange-600 mr-2" :
                            "w-5 h-5 text-yellow-600 mr-2"
                          } />
                          <span className="font-bold text-lg">{alert.type}</span>
                        </div>
                        <Badge className={alert.severityColor}>
                          {alert.severity}
                        </Badge>
                      </div>
                      
                      <p className="text-gray-700 mb-3">{alert.description}</p>
                      
                      <div className="flex flex-wrap gap-y-2">
                        <div className="flex items-center mr-6">
                          <span className="text-gray-600 text-sm mr-1">Location:</span>
                          <span className="text-sm font-medium">{alert.location}</span>
                        </div>
                        <div className="flex items-center mr-6">
                          <span className="text-gray-600 text-sm mr-1">Source:</span>
                          <span className="text-sm font-medium">{alert.source}</span>
                        </div>
                        <div className="flex items-center mr-6">
                          <span className="text-gray-600 text-sm mr-1">Impact Score:</span>
                          <span className="text-sm font-medium">{alert.impactScore}/100</span>
                        </div>
                        <div className="flex items-center mr-6">
                          <span className="text-gray-600 text-sm mr-1">Affected Population:</span>
                          <span className="text-sm font-medium">{alert.affectedPopulation.toLocaleString()}</span>
                        </div>
                        <div className="flex items-center">
                          <span className="text-gray-600 text-sm mr-1">Detected:</span>
                          <span className="text-sm font-medium">{alert.relativeTime}</span>
                        </div>
                      </div>

                      {alert.tags && alert.tags.length > 0 && (
                        <div className="mt-3 flex flex-wrap gap-1">
                          {alert.tags.map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}

                      {alert.evidencePosts && alert.evidencePosts.length > 0 && (
                        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                          <h4 className="text-sm font-semibold text-gray-700 mb-2">Evidence Post Sample:</h4>
                          <p className="text-sm text-gray-600 italic">"{alert.evidencePosts[0].content}"</p>
                          <div className="mt-2 flex items-center text-xs text-gray-500">
                            <span className="mr-4">Platform: {alert.evidencePosts[0].platform}</span>
                            <span className="mr-4">Toxicity: {Math.round(alert.evidencePosts[0].toxicityScore * 100)}%</span>
                            <span>Sentiment: {alert.evidencePosts[0].sentiment}</span>
                          </div>
                        </div>
                      )}
                    </div>
                    
                    <div className="bg-gray-50 p-4 lg:p-6 lg:border-l border-gray-200 flex lg:flex-col justify-between items-center lg:w-48">
                      <Button 
                        className="w-full bg-blue-600 hover:bg-blue-700 mb-2"
                        onClick={() => setSelectedAlert(alert)}
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        View Details
                      </Button>
                      <Button variant="outline" className="w-full">
                        Mark Reviewed
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="trends" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Alert Trends Over Time</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={reportData.trendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <RechartsTooltip />
                  <Line type="monotone" dataKey="hateSpeeech" stroke="#ef4444" name="Hate Speech" />
                  <Line type="monotone" dataKey="misinformation" stroke="#f59e0b" name="Misinformation" />
                  <Line type="monotone" dataKey="violence" stroke="#8b5cf6" name="Violence" />
                  <Line type="monotone" dataKey="fraud" stroke="#10b981" name="Electoral Fraud" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="geographic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Geographic Distribution of Alerts</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={reportData.regionData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <RechartsTooltip />
                  <Bar dataKey="critical" stackId="a" fill="#ef4444" name="Critical" />
                  <Bar dataKey="high" stackId="a" fill="#f59e0b" name="High" />
                  <Bar dataKey="medium" stackId="a" fill="#eab308" name="Medium" />
                  <Bar dataKey="low" stackId="a" fill="#22c55e" name="Low" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Alert Types Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={reportData.alertTypes}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({name, value}) => `${name}: ${value}`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {reportData.alertTypes.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <RechartsTooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>System Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Total Alerts:</span>
                    <span className="font-semibold">{reportData.metadata.totalAlerts}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Active Alerts:</span>
                    <span className="font-semibold">{reportData.metadata.activeAlerts}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Posts Analyzed (24h):</span>
                    <span className="font-semibold">{reportData.metadata.postsInLast24Hours}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Confidence Level:</span>
                    <span className="font-semibold">{Math.round(reportData.metadata.confidenceLevel * 100)}%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Update Frequency:</span>
                    <span className="font-semibold capitalize">{reportData.metadata.updateFrequency}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Recent Flagged Posts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportData.recentPosts.posts.map((post, index) => (
                  <div key={post.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <Badge className="mr-2">{post.platform}</Badge>
                        <Badge variant="outline">{post.alertType}</Badge>
                      </div>
                      <Badge className={
                        post.severity === "Critical" ? "bg-red-100 text-red-800" :
                        post.severity === "High" ? "bg-orange-100 text-orange-800" :
                        "bg-yellow-100 text-yellow-800"
                      }>
                        {post.severity}
                      </Badge>
                    </div>
                    <p className="text-gray-700 mb-2">"{post.content}"</p>
                    <div className="flex items-center text-sm text-gray-500">
                      <span className="mr-4">📍 {post.location}</span>
                      <span>{formatTimestamp(post.timestamp)}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
