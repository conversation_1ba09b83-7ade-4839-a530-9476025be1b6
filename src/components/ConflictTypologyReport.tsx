import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AlertTriangle, Shield, Users, TrendingUp, Download, Calendar, Filter, Target, Activity, BarChart3 } from 'lucide-react';

const ConflictTypologyReport = () => {
  const [selectedRegion, setSelectedRegion] = useState("all");
  const [selectedConflictType, setSelectedConflictType] = useState("all");
  const [selectedTimeframe, setSelectedTimeframe] = useState("6months");

  const regions = [
    { id: "all", label: "All Regions" },
    { id: "northern", label: "Northern Region" },
    { id: "central", label: "Central Region" },
    { id: "southern", label: "Southern Region" }
  ];

  const conflictTypes = [
    { id: "all", label: "All Conflict Types" },
    { id: "political", label: "Political Violence" },
    { id: "communal", label: "Communal Violence" },
    { id: "resource", label: "Resource-Based Conflicts" },
    { id: "electoral", label: "Electoral Violence" },
    { id: "identity", label: "Identity-Based Conflicts" }
  ];

  const timeframes = [
    { id: "3months", label: "Last 3 Months" },
    { id: "6months", label: "Last 6 Months" },
    { id: "1year", label: "Last 12 Months" },
    { id: "2years", label: "Last 2 Years" }
  ];

  // Dummy conflict typology data
  const conflictData = {
    overview: {
      totalConflicts: 342,
      activeConflicts: 67,
      resolvedConflicts: 275,
      conflictIntensity: "Medium-High",
      preventionSuccess: 78,
      lastUpdated: "2025-05-30"
    },
    conflictTypes: [
      {
        type: "Political Violence",
        code: "POL-VIO",
        totalIncidents: 89,
        activeIncidents: 12,
        intensityLevel: "High",
        trendDirection: "increasing",
        description: "Violence related to political activities, campaigns, and party competitions",
        subCategories: [
          { name: "Campaign Violence", incidents: 34, severity: "high", description: "Violence during political campaigns and rallies" },
          { name: "Party Clashes", incidents: 28, severity: "high", description: "Confrontations between different political parties" },
          { name: "Political Intimidation", incidents: 18, severity: "medium", description: "Threats and intimidation of political opponents" },
          { name: "Election-related Violence", incidents: 9, severity: "critical", description: "Violence specifically around election periods" }
        ],
        geographicDistribution: [
          { region: "Southern", incidents: 45, percentage: 51 },
          { region: "Central", incidents: 32, percentage: 36 },
          { region: "Northern", incidents: 12, percentage: 13 }
        ],
        temporalPattern: {
          seasonality: "High during election periods and campaign seasons",
          peakMonths: ["March", "May", "October"],
          cycleLength: "5-year electoral cycle with seasonal variations"
        },
        keyActors: [
          { actor: "Political Party Youth Wings", role: "Primary Perpetrators", involvement: "High" },
          { actor: "Opposition Groups", role: "Targets/Perpetrators", involvement: "High" },
          { actor: "Traditional Authorities", role: "Mediators", involvement: "Medium" },
          { actor: "Law Enforcement", role: "Responders", involvement: "Medium" }
        ]
      },
      {
        type: "Resource-Based Conflicts",
        code: "RES-CON",
        totalIncidents: 78,
        activeIncidents: 23,
        intensityLevel: "Medium",
        trendDirection: "stable",
        description: "Conflicts arising from competition over natural resources, land, and water",
        subCategories: [
          { name: "Land Disputes", incidents: 31, severity: "high", description: "Conflicts over land ownership and boundaries" },
          { name: "Water Access Conflicts", incidents: 24, severity: "medium", description: "Disputes over water sources and access rights" },
          { name: "Fishing Rights", incidents: 15, severity: "medium", description: "Conflicts over fishing areas and rights" },
          { name: "Grazing Disputes", incidents: 8, severity: "low", description: "Farmer-herder conflicts over grazing areas" }
        ],
        geographicDistribution: [
          { region: "Central", incidents: 34, percentage: 44 },
          { region: "Southern", incidents: 28, percentage: 36 },
          { region: "Northern", incidents: 16, percentage: 20 }
        ],
        temporalPattern: {
          seasonality: "Peaks during dry seasons and agricultural cycles",
          peakMonths: ["June", "September", "November"],
          cycleLength: "Annual cycle linked to weather patterns"
        },
        keyActors: [
          { actor: "Farmers", role: "Primary Stakeholders", involvement: "High" },
          { actor: "Pastoralists", role: "Resource Users", involvement: "High" },
          { actor: "Traditional Chiefs", role: "Arbitrators", involvement: "High" },
          { actor: "District Councils", role: "Administrative Authority", involvement: "Medium" }
        ]
      },
      {
        type: "Communal Violence",
        code: "COM-VIO",
        totalIncidents: 56,
        activeIncidents: 8,
        intensityLevel: "Medium",
        trendDirection: "decreasing",
        description: "Violence between different community groups, ethnic, or religious communities",
        subCategories: [
          { name: "Inter-ethnic Tensions", incidents: 22, severity: "medium", description: "Tensions between different ethnic groups" },
          { name: "Religious Conflicts", incidents: 18, severity: "low", description: "Conflicts between religious communities" },
          { name: "Chieftaincy Disputes", incidents: 12, severity: "high", description: "Succession and authority disputes among traditional leaders" },
          { name: "Market Conflicts", incidents: 4, severity: "low", description: "Disputes in trading centers and markets" }
        ],
        geographicDistribution: [
          { region: "Northern", incidents: 25, percentage: 45 },
          { region: "Central", incidents: 19, percentage: 34 },
          { region: "Southern", incidents: 12, percentage: 21 }
        ],
        temporalPattern: {
          seasonality: "No clear seasonal pattern, event-driven",
          peakMonths: ["Variable"],
          cycleLength: "Irregular, triggered by specific events"
        },
        keyActors: [
          { actor: "Community Leaders", role: "Mediators/Participants", involvement: "High" },
          { actor: "Religious Leaders", role: "Mediators", involvement: "Medium" },
          { actor: "Youth Groups", role: "Participants", involvement: "Medium" },
          { actor: "Traditional Authorities", role: "Arbitrators", involvement: "High" }
        ]
      }
    ],
    violenceClassification: {
      byIntensity: [
        { level: "Critical", incidents: 45, percentage: 13.2, description: "Severe violence with fatalities or major injuries" },
        { level: "High", incidents: 89, percentage: 26.0, description: "Significant violence with property damage" },
        { level: "Medium", incidents: 134, percentage: 39.2, description: "Moderate violence with minor injuries" },
        { level: "Low", incidents: 74, percentage: 21.6, description: "Threats and minor confrontations" }
      ],
      byDuration: [
        { duration: "Acute (< 1 week)", incidents: 198, percentage: 57.9 },
        { duration: "Protracted (1-4 weeks)", incidents: 89, percentage: 26.0 },
        { duration: "Chronic (> 1 month)", incidents: 55, percentage: 16.1 }
      ],
      byScope: [
        { scope: "Local (Single community)", incidents: 234, percentage: 68.4 },
        { scope: "District-wide", incidents: 78, percentage: 22.8 },
        { scope: "Multi-district", incidents: 30, percentage: 8.8 }
      ]
    },
    preventionAnalysis: {
      successfulPrevention: [
        {
          case: "Lilongwe Market Dispute Prevention",
          conflictType: "Resource-Based",
          interventionType: "Early Mediation",
          outcome: "Conflict averted",
          keyFactors: ["Quick response", "Trusted mediators", "Community buy-in"],
          lessons: "Early intervention by respected traditional leaders prevented escalation"
        },
        {
          case: "Northern Region Ethnic Tension Management",
          conflictType: "Communal",
          interventionType: "Dialogue Facilitation",
          outcome: "Peaceful resolution",
          keyFactors: ["Inter-faith cooperation", "Youth engagement", "Economic incentives"],
          lessons: "Multi-stakeholder approach with economic components highly effective"
        }
      ],
      preventionGaps: [
        "Limited early warning capacity in remote areas",
        "Insufficient resources for rapid response",
        "Weak coordination between traditional and modern authorities",
        "Limited youth engagement in prevention activities"
      ],
      recommendedInterventions: [
        { intervention: "Strengthen Traditional Mediation Systems", priority: "High", timeframe: "6 months" },
        { intervention: "Establish Community Early Warning Networks", priority: "High", timeframe: "12 months" },
        { intervention: "Improve Inter-Agency Coordination", priority: "Medium", timeframe: "9 months" },
        { intervention: "Develop Youth Peace Ambassador Programs", priority: "Medium", timeframe: "18 months" }
      ]
    },
    riskAssessment: {
      currentRiskLevel: "Medium-High",
      riskFactors: [
        { factor: "Political Competition", level: "High", trend: "Increasing", impact: "Creates polarization and violence" },
        { factor: "Resource Scarcity", level: "Medium", trend: "Stable", impact: "Drives competition and disputes" },
        { factor: "Economic Inequality", level: "High", trend: "Increasing", impact: "Fuels grievances and recruitment" },
        { factor: "Climate Change", level: "Medium", trend: "Increasing", impact: "Exacerbates resource conflicts" }
      ],
      emergingThreats: [
        "Increased political mobilization ahead of elections",
        "Youth unemployment driving recruitment into violence",
        "Climate-induced migration creating new tensions",
        "Social media amplifying conflicts and misinformation"
      ]
    }
  };

  const getIntensityColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing': return <TrendingUp className="w-4 h-4 text-red-500" />;
      case 'decreasing': return <TrendingUp className="w-4 h-4 text-green-500 rotate-180" />;
      default: return <div className="w-4 h-4 bg-gray-400 rounded-full" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <AlertTriangle className="w-6 h-6" />
            Conflict Typology & Dynamics Report
          </h2>
          <p className="text-gray-600 mt-1">Analysis of conflict dynamics, types of violence, and classification for prevention assessment</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Calendar className="w-4 h-4 mr-2" />
            Schedule
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Analysis Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Region</label>
              <Select value={selectedRegion} onValueChange={setSelectedRegion}>
                <SelectTrigger>
                  <SelectValue placeholder="Select region" />
                </SelectTrigger>
                <SelectContent>
                  {regions.map(region => (
                    <SelectItem key={region.id} value={region.id}>
                      {region.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Conflict Type</label>
              <Select value={selectedConflictType} onValueChange={setSelectedConflictType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  {conflictTypes.map(type => (
                    <SelectItem key={type.id} value={type.id}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Time Period</label>
              <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
                <SelectTrigger>
                  <SelectValue placeholder="Select timeframe" />
                </SelectTrigger>
                <SelectContent>
                  {timeframes.map(timeframe => (
                    <SelectItem key={timeframe.id} value={timeframe.id}>
                      {timeframe.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button className="w-full">Apply Filters</Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Overview Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Conflicts</p>
                <p className="text-2xl font-bold">{conflictData.overview.totalConflicts}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Conflicts</p>
                <p className="text-2xl font-bold">{conflictData.overview.activeConflicts}</p>
              </div>
              <Activity className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Resolved</p>
                <p className="text-2xl font-bold">{conflictData.overview.resolvedConflicts}</p>
              </div>
              <Shield className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Intensity Level</p>
                <Badge className="bg-orange-100 text-orange-800">{conflictData.overview.conflictIntensity}</Badge>
              </div>
              <BarChart3 className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Prevention Rate</p>
                <p className="text-2xl font-bold">{conflictData.overview.preventionSuccess}%</p>
              </div>
              <Target className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Last Updated</p>
                <p className="text-sm font-medium">{conflictData.overview.lastUpdated}</p>
              </div>
              <Calendar className="w-8 h-8 text-gray-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analysis Tabs */}
      <Tabs defaultValue="typology" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="typology">Conflict Types</TabsTrigger>
          <TabsTrigger value="classification">Classification</TabsTrigger>
          <TabsTrigger value="prevention">Prevention</TabsTrigger>
          <TabsTrigger value="risk">Risk Assessment</TabsTrigger>
          <TabsTrigger value="dynamics">Dynamics</TabsTrigger>
        </TabsList>

        <TabsContent value="typology" className="space-y-4">
          {conflictData.conflictTypes.map((conflict, index) => (
            <Card key={index} className="border-l-4 border-l-red-500">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <AlertTriangle className="w-5 h-5" />
                      {conflict.type}
                      <Badge variant="outline" className="text-xs">{conflict.code}</Badge>
                    </CardTitle>
                    <p className="text-gray-600">{conflict.description}</p>
                  </div>
                  <div className="text-right">
                    <Badge className={getIntensityColor(conflict.intensityLevel)}>
                      {conflict.intensityLevel} Intensity
                    </Badge>
                    <div className="flex items-center gap-1 mt-1">
                      {getTrendIcon(conflict.trendDirection)}
                      <span className="text-xs text-gray-600 capitalize">{conflict.trendDirection}</span>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Sub-categories */}
                  <div>
                    <h4 className="font-semibold mb-3">Sub-Categories & Incidents</h4>
                    <div className="space-y-3">
                      {conflict.subCategories.map((sub, idx) => (
                        <div key={idx} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-medium">{sub.name}</span>
                              <div className={`w-2 h-2 rounded-full ${getSeverityColor(sub.severity)}`}></div>
                            </div>
                            <p className="text-xs text-gray-600 mt-1">{sub.description}</p>
                          </div>
                          <span className="font-bold text-lg">{sub.incidents}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Geographic Distribution */}
                  <div>
                    <h4 className="font-semibold mb-3">Geographic Distribution</h4>
                    <div className="space-y-3">
                      {conflict.geographicDistribution.map((geo, idx) => (
                        <div key={idx} className="flex items-center justify-between p-3 bg-blue-50 rounded">
                          <span className="text-sm font-medium">{geo.region} Region</span>
                          <div className="text-right">
                            <span className="font-bold">{geo.incidents}</span>
                            <span className="text-xs text-gray-600 ml-2">({geo.percentage}%)</span>
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="mt-4">
                      <h5 className="font-medium mb-2">Key Actors</h5>
                      <div className="space-y-1">
                        {conflict.keyActors.slice(0, 3).map((actor, idx) => (
                          <div key={idx} className="flex justify-between items-center text-sm">
                            <span>{actor.actor}</span>
                            <Badge variant="outline" className="text-xs">{actor.involvement}</Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="classification" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>By Intensity Level</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {conflictData.violenceClassification.byIntensity.map((item, idx) => (
                    <div key={idx} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex-1">
                        <span className="font-medium">{item.level}</span>
                        <p className="text-xs text-gray-600">{item.description}</p>
                      </div>
                      <div className="text-right">
                        <span className="font-bold">{item.incidents}</span>
                        <span className="text-xs text-gray-600 block">{item.percentage}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>By Duration</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {conflictData.violenceClassification.byDuration.map((item, idx) => (
                    <div key={idx} className="flex items-center justify-between p-3 border rounded">
                      <span className="font-medium">{item.duration}</span>
                      <div className="text-right">
                        <span className="font-bold">{item.incidents}</span>
                        <span className="text-xs text-gray-600 block">{item.percentage}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>By Geographic Scope</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {conflictData.violenceClassification.byScope.map((item, idx) => (
                    <div key={idx} className="flex items-center justify-between p-3 border rounded">
                      <span className="font-medium">{item.scope}</span>
                      <div className="text-right">
                        <span className="font-bold">{item.incidents}</span>
                        <span className="text-xs text-gray-600 block">{item.percentage}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="prevention" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Successful Prevention Cases</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {conflictData.preventionAnalysis.successfulPrevention.map((case_, idx) => (
                      <Card key={idx} className="border-l-4 border-l-green-500">
                        <CardContent className="p-4">
                          <h4 className="font-semibold">{case_.case}</h4>
                          <div className="grid grid-cols-2 gap-2 my-2 text-sm">
                            <span><strong>Type:</strong> {case_.conflictType}</span>
                            <span><strong>Intervention:</strong> {case_.interventionType}</span>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{case_.lessons}</p>
                          <div className="flex flex-wrap gap-1">
                            {case_.keyFactors.map((factor, fidx) => (
                              <Badge key={fidx} variant="outline" className="text-xs">
                                {factor}
                              </Badge>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Prevention Gaps</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {conflictData.preventionAnalysis.preventionGaps.map((gap, idx) => (
                      <div key={idx} className="flex items-start gap-2 p-2 bg-red-50 rounded">
                        <AlertTriangle className="w-4 h-4 text-red-600 mt-0.5" />
                        <span className="text-sm">{gap}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recommended Interventions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {conflictData.preventionAnalysis.recommendedInterventions.map((intervention, idx) => (
                      <div key={idx} className="flex items-center justify-between p-3 bg-blue-50 rounded">
                        <div className="flex-1">
                          <span className="text-sm font-medium">{intervention.intervention}</span>
                          <div className="text-xs text-gray-600">{intervention.timeframe}</div>
                        </div>
                        <Badge className={intervention.priority === 'High' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}>
                          {intervention.priority}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="risk" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Current Risk Factors</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {conflictData.riskAssessment.riskFactors.map((factor, idx) => (
                    <div key={idx} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-semibold">{factor.factor}</h4>
                        <div className="flex items-center gap-2">
                          <Badge className={getIntensityColor(factor.level)}>
                            {factor.level}
                          </Badge>
                          {getTrendIcon(factor.trend.toLowerCase())}
                        </div>
                      </div>
                      <p className="text-gray-600 text-sm">{factor.impact}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Emerging Threats</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {conflictData.riskAssessment.emergingThreats.map((threat, idx) => (
                    <div key={idx} className="flex items-start gap-2 p-3 bg-yellow-50 rounded border-l-4 border-yellow-500">
                      <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5" />
                      <span className="text-sm">{threat}</span>
                    </div>
                  ))}
                </div>
                <div className="mt-4 p-4 bg-red-50 rounded">
                  <h4 className="font-semibold text-red-800 mb-2">Overall Risk Level</h4>
                  <Badge className="bg-red-100 text-red-800 text-lg px-3 py-1">
                    {conflictData.riskAssessment.currentRiskLevel}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="dynamics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Conflict Dynamics Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3">Escalation Patterns</h4>
                    <div className="space-y-3">
                      <div className="p-3 bg-red-50 rounded border-l-4 border-red-500">
                        <h5 className="font-medium">Political Cycle Driven</h5>
                        <p className="text-sm text-gray-600">Conflicts intensify during election periods and political campaigns</p>
                      </div>
                      <div className="p-3 bg-orange-50 rounded border-l-4 border-orange-500">
                        <h5 className="font-medium">Resource Seasonality</h5>
                        <p className="text-sm text-gray-600">Resource conflicts peak during dry seasons and harvest times</p>
                      </div>
                      <div className="p-3 bg-yellow-50 rounded border-l-4 border-yellow-500">
                        <h5 className="font-medium">Economic Stress Response</h5>
                        <p className="text-sm text-gray-600">Economic downturns correlate with increased conflict frequency</p>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3">De-escalation Factors</h4>
                    <div className="space-y-3">
                      <div className="p-3 bg-green-50 rounded border-l-4 border-green-500">
                        <h5 className="font-medium">Traditional Mediation</h5>
                        <p className="text-sm text-gray-600">Respected traditional authorities often successfully mediate conflicts</p>
                      </div>
                      <div className="p-3 bg-blue-50 rounded border-l-4 border-blue-500">
                        <h5 className="font-medium">Economic Incentives</h5>
                        <p className="text-sm text-gray-600">Economic opportunities and development projects reduce conflict potential</p>
                      </div>
                      <div className="p-3 bg-purple-50 rounded border-l-4 border-purple-500">
                        <h5 className="font-medium">Youth Engagement</h5>
                        <p className="text-sm text-gray-600">Active youth participation in peacebuilding reduces recruitment into violence</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ConflictTypologyReport;