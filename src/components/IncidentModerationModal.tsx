import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Save, 
  Trash2, 
  Eye, 
  ExternalLink,
  AlertTriangle,
  MapPin,
  Calendar,
  User,
  FileText
} from 'lucide-react';
import { Incident } from '../types/incidentsTypes';
import { 
  MALAWI_DISTRICTS, 
  INCIDENT_CATEGORIES, 
  PLATFORMS, 
  SEVERITY_LEVELS,
  SENTIMENT_OPTIONS,
  POLITICAL_ENTITIES,
  PROMINENT_NAMES,
  MALAWI_CANDIDATES,
  POLITICAL_ELECTORAL_MISCONDUCT,
  DISCRIMINATION_DISADVANTAGED_GROUPS,
  LAW_ENFORCEMENT_MISCONDUCT,
  PHYSICAL_INTEGRITY_VIOLATIONS,
  GENDER_BASED_VIOLENCE,
  POLITICAL_ATTACKS_HARASSMENT,
  PWA_ATTACKS,
  PROTESTS_DEMONSTRATIONS,
  PERPETRATOR_VICTIM_PROFILES
} from '../types/incidentsTypes';

interface IncidentModerationModalProps {
  isOpen: boolean;
  onClose: () => void;
  incident: Incident | null;
  onSave?: (updatedIncident: Partial<Incident>) => void;
  onDelete?: (incidentId: number) => void;
}

const getSeverityColor = (severity: string) => {
  switch (severity.toLowerCase()) {
    case 'critical': return 'bg-red-100 text-red-800 border-red-200';
    case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'low': return 'bg-green-100 text-green-800 border-green-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getCategoryColor = (category: string) => {
  switch (category) {
    case 'Political/electoral misconduct': return 'bg-purple-100 text-purple-800';
    case 'Violation of right to physical integrity (violent attacks)': return 'bg-red-100 text-red-800';
    case 'Law enforcement misconduct (Violation of right to liberty and security of persons)': return 'bg-orange-100 text-orange-800';
    case 'Restrictions on civil and political rights': return 'bg-blue-100 text-blue-800';
    case 'Politically motivated attacks/harassment/intimidation/incitement': return 'bg-pink-100 text-pink-800';
    case 'Misinformation': return 'bg-yellow-100 text-yellow-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getPlatformIcon = (platform: string) => {
  switch (platform.toLowerCase()) {
    case 'facebook': return '📘';
    case 'twitter': return '🐦';
    case 'whatsapp': return '💬';
    case 'tiktok': return '🎵';
    case 'instagram': return '📸';
    case 'website': return '🌐';
    default: return '📱';
  }
};

const getSentimentColor = (sentiment: string) => {
  switch (sentiment) {
    case 'Positive': return 'bg-green-50 text-green-700 border-green-200';
    case 'Negative': return 'bg-red-50 text-red-700 border-red-200';
    case 'Neutral': return 'bg-gray-50 text-gray-700 border-gray-200';
    default: return 'bg-gray-50 text-gray-700 border-gray-200';
  }
};

const IncidentModerationModal: React.FC<IncidentModerationModalProps> = ({
  isOpen,
  onClose,
  incident,
  onSave,
  onDelete
}) => {
  const [editableIncident, setEditableIncident] = useState<Partial<Incident>>({});
  const [isEditing, setIsEditing] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [approvalStatus, setApprovalStatus] = useState<'pending' | 'approved' | 'rejected'>('pending');

  useEffect(() => {
    if (incident) {
      setEditableIncident({
        id: incident.id,
        post_summary: incident.post_summary,
        primary_category: incident.primary_category,
        severity_level: incident.severity_level,
        district: incident.district,
        platform: incident.platform,
        perpetrator: incident.perpetrator,
        victims_profile: incident.victims_profile,
        details: incident.details,
        date_of_incident: incident.date_of_incident,
        sentiment: incident.sentiment,
        created_at: incident.created_at,
        // Additional filter fields
        civil_rights_restriction: incident.civil_rights_restriction,
        electoral_misconduct: incident.electoral_misconduct,
        discrimination_type: incident.discrimination_type,
        law_enforcement_misconduct: incident.law_enforcement_misconduct,
        gender_based_violence: incident.gender_based_violence,
        political_attacks: incident.political_attacks,
        pwa_attacks: incident.pwa_attacks,
        protest_type: incident.protest_type,
        physical_integrity_violation: incident.physical_integrity_violation,
        gender_of_victim: incident.gender_of_victim,
        election_relevance: incident.election_relevance,
        // Political context fields
        candidate: (incident as any).candidate,
        political_entity: (incident as any).political_entity,
        prominent_name: (incident as any).prominent_name,
        perpetrator_profile: (incident as any).perpetrator_profile,
        victim_profile: (incident as any).victim_profile
      });
      setIsEditing(false);
      setHasChanges(false);
    }
  }, [incident]);

  const handleFieldChange = (field: keyof Incident, value: any) => {
    // Convert special placeholder values back to null/empty
    const processedValue = 
      value === 'not_specified' || value === 'none' ? null : value;
    
    setEditableIncident(prev => ({
      ...prev,
      [field]: processedValue
    }));
    setHasChanges(true);
  };

  const handleSave = () => {
    if (onSave && hasChanges) {
      const updatedIncident = {
        ...editableIncident,
        approval_status: approvalStatus,
        reviewed_at: new Date().toISOString()
      };
      onSave(updatedIncident);
    }
    setIsEditing(false);
    setHasChanges(false);
    onClose();
  };

  const handleDelete = () => {
    if (onDelete && incident) {
      if (window.confirm('Are you sure you want to delete this incident? This action cannot be undone.')) {
        onDelete(incident.id);
        onClose();
      }
    }
  };

  const toggleEdit = () => {
    setIsEditing(!isEditing);
    if (isEditing && hasChanges) {
      const confirmDiscard = window.confirm('You have unsaved changes. Do you want to discard them?');
      if (!confirmDiscard) {
        return;
      }
    }
    if (!isEditing) {
      setHasChanges(false);
    }
  };

  if (!incident) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[95vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="text-center">
            <div className="flex items-center justify-center gap-2 text-xl mb-3">
              <FileText className="w-5 h-5 text-blue-600" />
              Incident Moderation - ID #{incident.id}
            </div>
            <div className="flex items-center justify-center gap-2">
              <Button
                variant={isEditing ? "secondary" : "default"}
                size="sm"
                onClick={toggleEdit}
                className={!isEditing ? "bg-blue-600 hover:bg-blue-700 text-white" : ""}
              >
                {isEditing ? "Cancel Edit" : "🔧 Edit Fields"}
              </Button>
              {isEditing && hasChanges && (
                <Button
                  size="sm"
                  onClick={handleSave}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Save className="w-4 h-4 mr-1" />
                  Save Changes
                </Button>
              )}
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <ScrollArea className="h-[70vh] pr-4">
              <div className="space-y-6">
                {/* Status and Basic Info */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="w-5 h-5" />
                        Basic Information
                      </div>
                      {!isEditing && (
                        <div className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                          Click "Edit" button above to modify fields
                        </div>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Current badges display */}
                    {!isEditing && (
                      <div className="flex gap-2 flex-wrap mb-4">
                        <Badge className={getSeverityColor(editableIncident.severity_level || '')}>
                          {editableIncident.severity_level}
                        </Badge>
                        <Badge className={getCategoryColor(editableIncident.primary_category || '')} variant="outline">
                          {editableIncident.primary_category}
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          {getPlatformIcon(editableIncident.platform || '')} {editableIncident.platform}
                        </Badge>
                        {editableIncident.sentiment && (
                          <Badge className={getSentimentColor(editableIncident.sentiment)} variant="outline">
                            {editableIncident.sentiment}
                          </Badge>
                        )}
                      </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Severity */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Severity Level</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.severity_level || ''}
                            onValueChange={(value) => handleFieldChange('severity_level', value as any)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {SEVERITY_LEVELS.map((level) => (
                                <SelectItem key={level} value={level}>
                                  {level}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.severity_level}
                          </div>
                        )}
                      </div>

                      {/* Category */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Primary Category</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.primary_category || ''}
                            onValueChange={(value) => handleFieldChange('primary_category', value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {INCIDENT_CATEGORIES.map((category) => (
                                <SelectItem key={category} value={category}>
                                  {category.length > 30 ? `${category.substring(0, 30)}...` : category}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.primary_category}
                          </div>
                        )}
                      </div>

                      {/* District */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">District</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.district || 'not_specified'}
                            onValueChange={(value) => handleFieldChange('district', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select district" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="not_specified">Not specified</SelectItem>
                              {MALAWI_DISTRICTS.map((district) => (
                                <SelectItem key={district} value={district}>
                                  {district}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.district || 'Not specified'}
                          </div>
                        )}
                      </div>

                      {/* Platform */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Platform</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.platform || ''}
                            onValueChange={(value) => handleFieldChange('platform', value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {PLATFORMS.map((platform) => (
                                <SelectItem key={platform} value={platform}>
                                  {getPlatformIcon(platform)} {platform}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {getPlatformIcon(editableIncident.platform || '')} {editableIncident.platform}
                          </div>
                        )}
                      </div>

                      {/* Sentiment */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Sentiment</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.sentiment || 'not_specified'}
                            onValueChange={(value) => handleFieldChange('sentiment', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select sentiment" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="not_specified">Not specified</SelectItem>
                              {SENTIMENT_OPTIONS.map((sentiment) => (
                                <SelectItem key={sentiment} value={sentiment}>
                                  {sentiment}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.sentiment || 'Not specified'}
                          </div>
                        )}
                      </div>

                      {/* Date of Incident */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Date of Incident</label>
                        {isEditing ? (
                          <input
                            type="date"
                            value={editableIncident.date_of_incident || ''}
                            onChange={(e) => handleFieldChange('date_of_incident', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                          />
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.date_of_incident ? 
                              new Date(editableIncident.date_of_incident).toLocaleDateString() : 
                              'Not specified'
                            }
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Additional moderation fields */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Gender of Victim */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Gender of Victim</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.gender_of_victim || 'not_specified'}
                            onValueChange={(value) => handleFieldChange('gender_of_victim', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select gender" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="not_specified">Not specified</SelectItem>
                              <SelectItem value="Male">Male</SelectItem>
                              <SelectItem value="Female">Female</SelectItem>
                              <SelectItem value="Other">Other</SelectItem>
                              <SelectItem value="Mixed">Mixed</SelectItem>
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.gender_of_victim || 'Not specified'}
                          </div>
                        )}
                      </div>

                      {/* Election Relevance */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Election Relevance</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.election_relevance || 'not_specified'}
                            onValueChange={(value) => handleFieldChange('election_relevance', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select relevance" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="not_specified">Not specified</SelectItem>
                              <SelectItem value="Direct">Direct</SelectItem>
                              <SelectItem value="Indirect">Indirect</SelectItem>
                              <SelectItem value="Not relevant">Not relevant</SelectItem>
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.election_relevance || 'Not specified'}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Political Context & Entities */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center justify-between">
                      Political Context & Entities
                      {!isEditing && (
                        <div className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                          Enable edit mode to modify
                        </div>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Candidate Names */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Related Candidate</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.candidate || 'not_specified'}
                            onValueChange={(value) => handleFieldChange('candidate', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select candidate" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="not_specified">Not specified</SelectItem>
                              {MALAWI_CANDIDATES.map((candidate) => (
                                <SelectItem key={candidate} value={candidate}>
                                  {candidate}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.candidate || 'Not specified'}
                          </div>
                        )}
                      </div>

                      {/* Political Entities */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Political Entity/Organization</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.political_entity || 'not_specified'}
                            onValueChange={(value) => handleFieldChange('political_entity', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select entity" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="not_specified">Not specified</SelectItem>
                              {POLITICAL_ENTITIES.map((entity) => (
                                <SelectItem key={entity} value={entity}>
                                  {entity.length > 35 ? `${entity.substring(0, 35)}...` : entity}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.political_entity || 'Not specified'}
                          </div>
                        )}
                      </div>

                      {/* Prominent Names */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Prominent Names Mentioned</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.prominent_name || 'not_specified'}
                            onValueChange={(value) => handleFieldChange('prominent_name', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select prominent name" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="not_specified">Not specified</SelectItem>
                              {PROMINENT_NAMES.map((name) => (
                                <SelectItem key={name} value={name}>
                                  {name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.prominent_name || 'Not specified'}
                          </div>
                        )}
                      </div>

                      {/* Perpetrator Profile */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Perpetrator Profile</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.perpetrator_profile || 'not_specified'}
                            onValueChange={(value) => handleFieldChange('perpetrator_profile', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select perpetrator profile" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="not_specified">Not specified</SelectItem>
                              {Object.entries(PERPETRATOR_VICTIM_PROFILES).map(([category, profiles]) => [
                                <div key={`${category}-header`} className="px-2 py-1 text-sm font-semibold text-gray-700 bg-gray-100">
                                  {category}
                                </div>,
                                ...profiles.map((profile) => (
                                  <SelectItem key={profile} value={profile} className="pl-4">
                                    {profile.length > 40 ? `${profile.substring(0, 40)}...` : profile}
                                  </SelectItem>
                                ))
                              ]).flat()}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.perpetrator_profile || 'Not specified'}
                          </div>
                        )}
                      </div>

                      {/* Victim Profile */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Victim Profile</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.victim_profile || 'not_specified'}
                            onValueChange={(value) => handleFieldChange('victim_profile', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select victim profile" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="not_specified">Not specified</SelectItem>
                              {Object.entries(PERPETRATOR_VICTIM_PROFILES).map(([category, profiles]) => [
                                <div key={`${category}-header`} className="px-2 py-1 text-sm font-semibold text-gray-700 bg-gray-100">
                                  {category}
                                </div>,
                                ...profiles.map((profile) => (
                                  <SelectItem key={profile} value={profile} className="pl-4">
                                    {profile.length > 40 ? `${profile.substring(0, 40)}...` : profile}
                                  </SelectItem>
                                ))
                              ]).flat()}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.victim_profile || 'Not specified'}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Post Content */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Post Content</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Post Summary</label>
                      {isEditing ? (
                        <Textarea
                          value={editableIncident.post_summary || ''}
                          onChange={(e) => handleFieldChange('post_summary', e.target.value)}
                          rows={3}
                          className="w-full"
                        />
                      ) : (
                        <div className="text-sm text-gray-600 p-3 bg-gray-50 rounded">
                          {editableIncident.post_summary}
                        </div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Details</label>
                      {isEditing ? (
                        <Textarea
                          value={editableIncident.details || ''}
                          onChange={(e) => handleFieldChange('details', e.target.value)}
                          rows={4}
                          className="w-full"
                        />
                      ) : (
                        <div className="text-sm text-gray-600 p-3 bg-gray-50 rounded">
                          {editableIncident.details}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Parties Involved */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <User className="w-5 h-5" />
                      Parties Involved
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Perpetrator</label>
                      {isEditing ? (
                        <Textarea
                          value={editableIncident.perpetrator || ''}
                          onChange={(e) => handleFieldChange('perpetrator', e.target.value)}
                          rows={2}
                          className="w-full"
                        />
                      ) : (
                        <div className="text-sm text-gray-600 p-3 bg-red-50 rounded">
                          {editableIncident.perpetrator || 'Not specified'}
                        </div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Victims Profile</label>
                      {isEditing ? (
                        <Textarea
                          value={editableIncident.victims_profile || ''}
                          onChange={(e) => handleFieldChange('victims_profile', e.target.value)}
                          rows={2}
                          className="w-full"
                        />
                      ) : (
                        <div className="text-sm text-gray-600 p-3 bg-blue-50 rounded">
                          {editableIncident.victims_profile || 'Not specified'}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Violation Categories */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center justify-between">
                      Violation Categories
                      {!isEditing && (
                        <div className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                          Enable edit mode to modify
                        </div>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Political/Electoral Misconduct */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Political/Electoral Misconduct</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.electoral_misconduct || 'none'}
                            onValueChange={(value) => handleFieldChange('electoral_misconduct', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">None</SelectItem>
                              {POLITICAL_ELECTORAL_MISCONDUCT.map((item) => (
                                <SelectItem key={item} value={item}>
                                  {item.length > 40 ? `${item.substring(0, 40)}...` : item}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.electoral_misconduct || 'None specified'}
                          </div>
                        )}
                      </div>

                      {/* Physical Integrity Violation */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Physical Integrity Violation</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.physical_integrity_violation || 'none'}
                            onValueChange={(value) => handleFieldChange('physical_integrity_violation', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">None</SelectItem>
                              {PHYSICAL_INTEGRITY_VIOLATIONS.map((item) => (
                                <SelectItem key={item} value={item}>
                                  {item.length > 40 ? `${item.substring(0, 40)}...` : item}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.physical_integrity_violation || 'None specified'}
                          </div>
                        )}
                      </div>

                      {/* Law Enforcement Misconduct */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Law Enforcement Misconduct</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.law_enforcement_misconduct || 'none'}
                            onValueChange={(value) => handleFieldChange('law_enforcement_misconduct', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">None</SelectItem>
                              {LAW_ENFORCEMENT_MISCONDUCT.map((item) => (
                                <SelectItem key={item} value={item}>
                                  {item.length > 40 ? `${item.substring(0, 40)}...` : item}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.law_enforcement_misconduct || 'None specified'}
                          </div>
                        )}
                      </div>

                      {/* Civil Rights Restriction */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Civil Rights Restriction</label>
                        {isEditing ? (
                          <Textarea
                            value={editableIncident.civil_rights_restriction || ''}
                            onChange={(e) => handleFieldChange('civil_rights_restriction', e.target.value)}
                            rows={2}
                            className="w-full"
                            placeholder="Describe civil rights restrictions..."
                          />
                        ) : (
                          <div className="text-sm text-gray-600 p-2 bg-gray-50 rounded">
                            {editableIncident.civil_rights_restriction || 'None specified'}
                          </div>
                        )}
                      </div>

                      {/* Gender-Based Violence */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Gender-Based Violence</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.gender_based_violence || 'none'}
                            onValueChange={(value) => handleFieldChange('gender_based_violence', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">None</SelectItem>
                              {GENDER_BASED_VIOLENCE.map((item) => (
                                <SelectItem key={item} value={item}>
                                  {item.length > 40 ? `${item.substring(0, 40)}...` : item}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.gender_based_violence || 'None specified'}
                          </div>
                        )}
                      </div>

                      {/* Political Attacks */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Political Attacks/Harassment</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.political_attacks || 'none'}
                            onValueChange={(value) => handleFieldChange('political_attacks', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">None</SelectItem>
                              {POLITICAL_ATTACKS_HARASSMENT.map((item) => (
                                <SelectItem key={item} value={item}>
                                  {item.length > 40 ? `${item.substring(0, 40)}...` : item}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.political_attacks || 'None specified'}
                          </div>
                        )}
                      </div>

                      {/* Additional violation categories */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Discrimination Type</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.discrimination_type || 'none'}
                            onValueChange={(value) => handleFieldChange('discrimination_type', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">None</SelectItem>
                              {DISCRIMINATION_DISADVANTAGED_GROUPS.map((item) => (
                                <SelectItem key={item} value={item}>
                                  {item.length > 40 ? `${item.substring(0, 40)}...` : item}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.discrimination_type || 'None specified'}
                          </div>
                        )}
                      </div>

                      {/* PWA Attacks */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">PWA Attacks</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.pwa_attacks || 'none'}
                            onValueChange={(value) => handleFieldChange('pwa_attacks', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">None</SelectItem>
                              {PWA_ATTACKS.map((item) => (
                                <SelectItem key={item} value={item}>
                                  {item.length > 40 ? `${item.substring(0, 40)}...` : item}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.pwa_attacks || 'None specified'}
                          </div>
                        )}
                      </div>

                      {/* Protests/Demonstrations */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Protests/Demonstrations</label>
                        {isEditing ? (
                          <Select
                            value={editableIncident.protest_type || 'none'}
                            onValueChange={(value) => handleFieldChange('protest_type', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">None</SelectItem>
                              {PROTESTS_DEMONSTRATIONS.map((item) => (
                                <SelectItem key={item} value={item}>
                                  {item.length > 40 ? `${item.substring(0, 40)}...` : item}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="text-sm text-gray-600">
                            {editableIncident.protest_type || 'None specified'}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </ScrollArea>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-4">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => window.open(incident.source_url, '_blank')}
                  disabled={!incident.source_url}
                >
                  <Eye className="w-4 h-4 mr-2" />
                  View Original
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => window.open(incident.source_url, '_blank')}
                  disabled={!incident.source_url}
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Open Source
                </Button>
              </CardContent>
            </Card>

            {/* Metadata */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  Metadata
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <div className="text-xs font-medium text-gray-500">Incident ID</div>
                  <div className="text-sm">{incident.id}</div>
                </div>
                <div>
                  <div className="text-xs font-medium text-gray-500">Created At</div>
                  <div className="text-sm">
                    {new Date(incident.created_at).toLocaleString()}
                  </div>
                </div>
                <div>
                  <div className="text-xs font-medium text-gray-500">Last Updated</div>
                  <div className="text-sm">
                    {incident.updated_at ? 
                      new Date(incident.updated_at).toLocaleString() : 
                      'Never'
                    }
                  </div>
                </div>
                <div>
                  <div className="text-xs font-medium text-gray-500">Location</div>
                  <div className="text-sm flex items-center gap-1">
                    <MapPin className="w-3 h-3" />
                    {editableIncident.district || 'Not specified'}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Review Status */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Review Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {isEditing ? (
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Approval Status</label>
                      <Select
                        value={approvalStatus}
                        onValueChange={(value) => {
                          setApprovalStatus(value as any);
                          setHasChanges(true);
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pending">Pending Review</SelectItem>
                          <SelectItem value="approved">Approved</SelectItem>
                          <SelectItem value="rejected">Rejected</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                ) : (
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${
                      approvalStatus === 'approved' ? 'text-green-600' :
                      approvalStatus === 'rejected' ? 'text-red-600' : 
                      'text-orange-600'
                    }`}>
                      {approvalStatus === 'approved' ? 'Approved' :
                       approvalStatus === 'rejected' ? 'Rejected' : 
                       'Pending'}
                    </div>
                    <p className="text-xs text-gray-600">
                      {approvalStatus === 'approved' ? 'Ready for Publication' :
                       approvalStatus === 'rejected' ? 'Will not be Published' : 
                       'Awaiting Review'}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Footer Actions */}
        <div className="flex justify-between items-center pt-4 mt-6 border-t border-gray-200">
          {/* Left side - Delete button (only when not editing) */}
          <div className="flex items-center gap-2">
            {!isEditing && (
              <Button
                variant="destructive"
                size="sm"
                onClick={handleDelete}
                className="flex items-center gap-2"
              >
                <Trash2 className="w-4 h-4" />
                Delete Incident
              </Button>
            )}
          </div>
          
          {/* Right side - Navigation and save buttons */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onClose}
            >
              Close
            </Button>
            {isEditing && (
              <Button
                size="sm"
                onClick={handleSave}
                className="bg-green-600 hover:bg-green-700"
                disabled={!hasChanges}
              >
                <Save className="w-4 h-4 mr-1" />
                Save & Submit
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default IncidentModerationModal;