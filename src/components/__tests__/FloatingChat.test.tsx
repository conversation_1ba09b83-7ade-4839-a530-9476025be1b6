import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import FloatingChat from '../FloatingChat';

// Mock the API service
jest.mock('@/services/chatApiService', () => ({
  chatApiService: {
    sendMessage: jest.fn(),
    setConfig: jest.fn(),
  },
  mockChatResponse: jest.fn(() => 
    Promise.resolve({
      response: 'Mock AI response',
      conversation_id: 'test-conversation',
      timestamp: new Date().toISOString(),
      message_id: 'test-message-id',
    })
  ),
}));

// Mock the chat hook
jest.mock('@/hooks/useChat', () => ({
  useChat: () => ({
    messages: [
      {
        id: '1',
        text: 'Hello! I\'m your AI assistant.',
        sender: 'ai',
        timestamp: new Date(),
      },
    ],
    isLoading: false,
    error: null,
    sendMessage: jest.fn(),
    clearMessages: jest.fn(),
    exportMessages: jest.fn(() => ({
      messages: [],
      conversationId: 'test',
      exportDate: new Date().toISOString(),
      totalMessages: 1,
    })),
  }),
}));

describe('FloatingChat', () => {
  test('renders floating chat button', () => {
    render(<FloatingChat />);
    
    const chatButton = screen.getByRole('button');
    expect(chatButton).toBeInTheDocument();
  });

  test('opens chat window when button is clicked', () => {
    render(<FloatingChat />);
    
    const chatButton = screen.getByRole('button');
    fireEvent.click(chatButton);
    
    expect(screen.getByText('AI Assistant')).toBeInTheDocument();
    expect(screen.getByText('Hello! I\'m your AI assistant.')).toBeInTheDocument();
  });

  test('can minimize and restore chat window', () => {
    render(<FloatingChat />);
    
    // Open chat
    const chatButton = screen.getByRole('button');
    fireEvent.click(chatButton);
    
    // Minimize chat
    const minimizeButton = screen.getByRole('button', { name: /minimize/i });
    fireEvent.click(minimizeButton);
    
    // Click on title to restore
    const title = screen.getByText('AI Assistant');
    fireEvent.click(title);
    
    // Should show input field again
    expect(screen.getByPlaceholderText('Type your message...')).toBeInTheDocument();
  });

  test('displays initial AI message', () => {
    render(<FloatingChat />);
    
    const chatButton = screen.getByRole('button');
    fireEvent.click(chatButton);
    
    expect(screen.getByText('Hello! I\'m your AI assistant.')).toBeInTheDocument();
  });

  test('shows settings panel when settings button is clicked', () => {
    render(<FloatingChat />);
    
    // Open chat
    const chatButton = screen.getByRole('button');
    fireEvent.click(chatButton);
    
    // Click settings button
    const settingsButton = screen.getByRole('button', { name: /settings/i });
    fireEvent.click(settingsButton);
    
    expect(screen.getByText('Chat Settings')).toBeInTheDocument();
  });
});

export default {};
