import { <PERSON>, User, LogOut, <PERSON><PERSON><PERSON><PERSON>gle, Shield, BarChart3 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface NavigationProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  onLogout: () => void;
}

export const Navigation = ({ activeTab, setActiveTab, onLogout }: NavigationProps) => {
  return (
    <nav className="bg-blue-600 text-white shadow-lg">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo and Brand */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <img 
                src="/lovable-uploads/3e2b6588-d21b-42b6-8b8f-30cf6f521314.png" 
                alt="Magwero Logo" 
                className="w-8 h-8 object-contain"
              />
              <span className="text-xl font-bold">Magwero</span>
            </div>
          </div>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-8">            <button
              onClick={() => setActiveTab("dashboard")}
              className={`px-4 py-2 rounded-lg transition-colors ${
                activeTab === "dashboard" 
                  ? "bg-blue-700 text-white" 
                  : "text-blue-100 hover:text-white hover:bg-blue-500"
              }`}
            >
              Dashboard
            </button>
            <button
              onClick={() => setActiveTab("analytics")}
              className={`px-4 py-2 rounded-lg transition-colors ${
                activeTab === "analytics" 
                  ? "bg-blue-700 text-white" 
                  : "text-blue-100 hover:text-white hover:bg-blue-500"
              }`}
            >
              Analytics
            </button>
            <button
              onClick={() => setActiveTab("election_analytics")}
              className={`px-4 py-2 rounded-lg transition-colors ${
                activeTab === "election_analytics" 
                  ? "bg-blue-700 text-white" 
                  : "text-blue-100 hover:text-white hover:bg-blue-500"
              }`}
            >
              Election Analytics
            </button>
            <button
              onClick={() => setActiveTab("early_warning")}
              className={`px-4 py-2 rounded-lg transition-colors ${
                activeTab === "early_warning" 
                  ? "bg-blue-700 text-white" 
                  : "text-blue-100 hover:text-white hover:bg-blue-500"
              }`}
            >
              Early Warning
            </button>
            <button
              onClick={() => setActiveTab("preventive_actions")}
              className={`px-4 py-2 rounded-lg transition-colors ${
                activeTab === "preventive_actions" 
                  ? "bg-blue-700 text-white" 
                  : "text-blue-100 hover:text-white hover:bg-blue-500"
              }`}
            >
              Prevention
            </button>
            <button
              onClick={() => setActiveTab("alerts")}
              className={`px-4 py-2 rounded-lg transition-colors ${
                activeTab === "alerts" 
                  ? "bg-blue-700 text-white" 
                  : "text-blue-100 hover:text-white hover:bg-blue-500"
              }`}
            >
              Alerts
            </button>
            <button
              onClick={() => setActiveTab("reports")}
              className={`px-4 py-2 rounded-lg transition-colors ${
                activeTab === "reports" 
                  ? "bg-blue-700 text-white" 
                  : "text-blue-100 hover:text-white hover:bg-blue-500"
              }`}
            >
              Reports
            </button>
            <button
              onClick={() => setActiveTab("settings")}
              className={`px-4 py-2 rounded-lg transition-colors ${
                activeTab === "settings" 
                  ? "bg-blue-700 text-white" 
                  : "text-blue-100 hover:text-white hover:bg-blue-500"
              }`}
            >
              Settings
            </button>
           
          </div>

          {/* User Actions */}
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" className="relative text-white hover:bg-blue-500">
              <Bell className="w-5 h-5" />
              <Badge className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 flex items-center justify-center p-0">
                3
              </Badge>
            </Button>
            
            <div className="flex items-center space-x-2">
              <span className="text-sm">Admin User</span>
              <Button variant="ghost" size="sm" className="text-white hover:bg-blue-500">
                <User className="w-5 h-5" />
              </Button>
            </div>
            
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-white hover:bg-blue-500"
              onClick={onLogout}
            >
              <LogOut className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
};