import React, { useEffect, useRef, useState } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Refresh<PERSON>w, AlertTriangle } from "lucide-react";
import DistrictIncidentsModal from './DistrictIncidentsModal';

// Fix for default markers in leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface MapProps {
  statisticsData?: Record<string, number>;
  categoryData?: {
    security_incidents?: number;
    elections_incidents?: number;
    health_environment_incidents?: number;
    gender_incidents?: number;
    governance_hr_incidents?: number;
  };
  severityData?: Record<string, number>;
  loading?: boolean;
  height?: number;
  showCategories?: boolean;
}

const SimpleLeafletMap: React.FC<MapProps> = ({ 
  statisticsData = {}, 
  categoryData = {},
  severityData = {},
  loading = false, 
  height = 750,
  showCategories = true
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const [selectedDistrict, setSelectedDistrict] = useState<{name: string, count: number} | null>(null);

  // Mock data for testing
  const mockDistrictStats = {
    "Balaka": 12,
    "Blantyre": 25,
    "Chiradzulu": 8,
    "Chitipa": 3,
    "Dedza": 15,
    "Dowa": 10,
    "Karonga": 6,
    "Kasungu": 14,
    "Likoma": 2,
    "Lilongwe": 28,
    "Machinga": 9,
    "Mangochi": 18,
    "Mchinji": 7,
    "Mulanje": 11,
    "Mwanza": 4,
    "Mzimba": 16,
    "Neno": 5,
    "Nkhata Bay": 8,
    "Nkhotakota": 12,
    "Nsanje": 6,
    "Ntcheu": 13,
    "Ntchisi": 4,
    "Phalombe": 9,
    "Rumphi": 7,
    "Salima": 10,
    "Thyolo": 14,
    "Zomba": 20,
    "Mzuzu": 15
  };

  const mockCategoryData = {
    security_incidents: 85,
    elections_incidents: 67,
    health_environment_incidents: 42,
    gender_incidents: 28,
    governance_hr_incidents: 53
  };

  const mockSeverityData = {
    "Critical": 15,
    "High": 42,
    "Medium": 76,
    "Low": 142
  };

  // Use mock data if no real data provided
  const currentStats = Object.keys(statisticsData).length > 0 ? statisticsData : mockDistrictStats;
  const currentCategories = Object.keys(categoryData).length > 0 ? categoryData : mockCategoryData;
  const currentSeverity = Object.keys(severityData).length > 0 ? severityData : mockSeverityData;

  // Get district color based on value
  const getDistrictColor = (value: number): string => {
    if (value === 0) return '#cccccc';
    if (value <= 5) return '#fee2e2';
    if (value <= 10) return '#fecaca';
    if (value <= 20) return '#f87171';
    return '#dc2626';
  };

  // Get severity color based on severity level
  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  useEffect(() => {
    if (!mapRef.current || mapInstanceRef.current) return;

    // Initialize the map
    const map = L.map(mapRef.current, {
      zoomControl: true,
      scrollWheelZoom: true,
      doubleClickZoom: false,
      dragging: true
    }).setView([-13.254308, 34.301525], 6.5);  // Reduced initial zoom for better fit
    mapInstanceRef.current = map;

    // Don't add the base tile layer yet - we'll add it after the mask
    
    // Create background rectangles to hide surrounding areas
    const maskTop = L.rectangle([[-90, -180], [-8, 180]], {
      color: '#f8f9fa',
      fillColor: '#f8f9fa',
      fillOpacity: 1,
      weight: 0,
      interactive: false
    }).addTo(map);

    const maskBottom = L.rectangle([[-18, -180], [90, 180]], {
      color: '#f8f9fa',
      fillColor: '#f8f9fa', 
      fillOpacity: 1,
      weight: 0,
      interactive: false
    }).addTo(map);

    const maskLeft = L.rectangle([[-18, -180], [-8, 32]], {
      color: '#f8f9fa',
      fillColor: '#f8f9fa',
      fillOpacity: 1,
      weight: 0,
      interactive: false
    }).addTo(map);

    const maskRight = L.rectangle([[-18, 37], [-8, 180]], {
      color: '#f8f9fa',
      fillColor: '#f8f9fa',
      fillOpacity: 1,
      weight: 0,
      interactive: false
    }).addTo(map);

    // Now add tile layer with reduced opacity
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
      opacity: 0.4  // Make base map even less prominent
    }).addTo(map);

    // Load and add GeoJSON data
    const loadGeoJSON = async () => {
      try {
        const response = await fetch('/malawi-districts.json');
        if (!response.ok) {
          throw new Error('Failed to load map data');
        }
        const geoJsonData = await response.json();

        // Store the district layer reference
        const districtLayer = L.geoJSON(geoJsonData, {
          style: (feature) => {
            const districtName = feature?.properties?.shapeName;
            const value = currentStats[districtName] || 0;
            return {
              fillColor: getDistrictColor(value),
              weight: 2,
              opacity: 1,
              color: '#666',
              dashArray: '',
              fillOpacity: 0.8
            };
          },
          onEachFeature: (feature, layer) => {
            const districtName = feature.properties?.shapeName;
            const value = currentStats[districtName] || 0;
            
            // Bind popup for basic info (still useful for hover info)
            layer.bindPopup(`
              <div style="font-family: Arial, sans-serif;">
                <strong style="font-size: 16px;">${districtName}</strong><br/>
                <span style="font-size: 14px;">Incidents: <strong>${value}</strong></span><br/>
                <small style="color: #666;">Click for detailed view</small>
              </div>
            `);

            layer.on({
              click: () => {
                if (value > 0) {
                  setSelectedDistrict({ name: districtName, count: value });
                }
              },
              mouseover: (e) => {
                const target = e.target;
                target.setStyle({
                  weight: 3,
                  color: '#333',
                  fillOpacity: 0.9
                });
                target.bringToFront();
                
                // Show popup on hover
                target.openPopup();
              },
              mouseout: (e) => {
                const target = e.target;
                target.setStyle({
                  fillColor: getDistrictColor(value),
                  weight: 2,
                  opacity: 1,
                  color: '#666',
                  fillOpacity: 0.8
                });
                
                // Close popup on mouseout
                target.closePopup();
              }
            });
          }
        }).addTo(map);

        // Add district labels as markers
        geoJsonData.features.forEach((feature: any) => {
          const districtName = feature.properties?.shapeName;
          const value = currentStats[districtName] || 0;
          
          if (value > 0) {
            // Calculate center of district (simplified)
            const bounds = L.geoJSON(feature).getBounds();
            const center = bounds.getCenter();
            
            L.marker(center, {
              icon: L.divIcon({
                className: 'district-label-marker',
                html: `<div style="
                  background: rgba(255,255,255,0.9);
                  border: 1px solid #666;
                  border-radius: 4px;
                  padding: 2px 6px;
                  font-size: 12px;
                  font-weight: bold;
                  color: #333;
                  text-align: center;
                  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                  pointer-events: none;
                ">${value}</div>`,
                iconSize: [30, 20],
                iconAnchor: [15, 10]
              })
            }).addTo(map);
          }
        });

      } catch (error) {
        console.error('Error loading GeoJSON:', error);
      }
    };

    loadGeoJSON();

    // Set strict map bounds to Malawi area only
    map.setMaxBounds([[-17.5, 32.5], [-9, 36.5]]);
    map.setMinZoom(6);  // Lower minimum zoom for better canvas fit
    map.setMaxZoom(10); // Reduced maximum zoom

    // Cleanup function
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [currentStats]);

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Interactive Map</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center" style={{ height: `${height}px` }}>
            <div className="flex items-center space-x-2">
              <RefreshCw className="w-6 h-6 animate-spin" />
              <span>Loading map...</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <DistrictIncidentsModal
        isOpen={selectedDistrict !== null}
        onClose={() => setSelectedDistrict(null)}
        districtName={selectedDistrict?.name || ''}
        incidentCount={selectedDistrict?.count || 0}
      />
      
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
      {/* Map */}
      <div className="lg:col-span-3">
        <Card>
          <CardHeader>
            <CardTitle>Malawi Districts - Incident Distribution</CardTitle>
          </CardHeader>
          <CardContent className="p-0 relative">
            <div
              ref={mapRef}
              style={{ height: `${height}px`, width: '100%' }}
              className="rounded-lg overflow-hidden"
            />
            
            {/* Legend Overlay */}
            <div className="absolute top-4 right-4 bg-white/95 backdrop-blur-sm rounded-lg shadow-lg border border-gray-200 p-4 max-w-xs z-[1000]">
              <div className="space-y-3">
                <div className="text-sm font-semibold text-gray-800 border-b border-gray-200 pb-2">
                  Incident Count Legend
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-4 h-4 border border-gray-400 rounded-sm" 
                      style={{ backgroundColor: '#cccccc' }}
                    />
                    <span className="text-xs text-gray-700">No incidents</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-4 h-4 border border-gray-400 rounded-sm" 
                      style={{ backgroundColor: '#fee2e2' }}
                    />
                    <span className="text-xs text-gray-700">1 - 5</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-4 h-4 border border-gray-400 rounded-sm" 
                      style={{ backgroundColor: '#fecaca' }}
                    />
                    <span className="text-xs text-gray-700">6 - 10</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-4 h-4 border border-gray-400 rounded-sm" 
                      style={{ backgroundColor: '#f87171' }}
                    />
                    <span className="text-xs text-gray-700">11 - 20</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-4 h-4 border border-gray-400 rounded-sm" 
                      style={{ backgroundColor: '#dc2626' }}
                    />
                    <span className="text-xs text-gray-700">21+</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sidebar */}
      <div className="lg:col-span-1 space-y-6">
        {/* Category Breakdown */}
        {showCategories && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Categories</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(currentCategories).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-700 capitalize">
                        {key.replace(/_/g, ' ').replace('incidents', '').trim()}
                      </p>
                    </div>
                    <div className="text-right">
                      <span className="text-lg font-bold text-blue-600">{value}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Top Locations */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Top Locations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(currentStats)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
                .map(([district, count]) => (
                  <div key={district} className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">{district}</span>
                    <span className="text-sm font-bold text-red-600">{count}</span>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>

        {/* Incidents by Severity */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Incidents by Severity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(currentSeverity)
                .sort(([,a], [,b]) => b - a)
                .map(([severity, count]) => (
                  <div key={severity} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className={getSeverityColor(severity)}>
                        {severity}
                      </Badge>
                    </div>
                    <span className="text-sm font-bold text-gray-900">{count}</span>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
    </>
  );
};

export default SimpleLeafletMap;