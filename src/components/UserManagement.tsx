import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Plus, Search, Users, UserCheck, UserX, Shield } from "lucide-react";
import { UserTable } from "./UserTable";
import { UserForm } from "./UserForm";
import { PasswordChangeForm } from "./PasswordChangeForm";
import { userService } from "@/services/userService";
import { 
  User, 
  UserFilters, 
  CreateUserFormData, 
  UpdateUserFormData,
  ChangePasswordFormData,
  AdminChangePasswordFormData,
  UserRole 
} from "@/types/userTypes";
import { useAuth } from "@/App";

export const UserManagement = () => {
  const [filters, setFilters] = useState<UserFilters>({
    page: 1,
    limit: 10,
    search: "",
    role: undefined,
    is_active: undefined,
  });
  const [isUserFormOpen, setIsUserFormOpen] = useState(false);
  const [isPasswordFormOpen, setIsPasswordFormOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [formMode, setFormMode] = useState<'create' | 'edit' | 'view'>('create');

  const { toast } = useToast();
  const { user: currentUser } = useAuth();
  const queryClient = useQueryClient();

  // Fetch users
  const {
    data: usersResponse,
    isLoading: isLoadingUsers,
    error: usersError,
  } = useQuery({
    queryKey: ['users', filters],
    queryFn: () => userService.getUsers(filters),
    enabled: !!currentUser,
  });

  // Fetch user stats
  const {
    data: statsResponse,
    isLoading: isLoadingStats,
  } = useQuery({
    queryKey: ['userStats'],
    queryFn: () => userService.getUserStats(),
    enabled: !!currentUser,
  });

  // Create user mutation
  const createUserMutation = useMutation({
    mutationFn: (userData: CreateUserFormData) => userService.createUser(userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['userStats'] });
      toast({
        title: "User Created",
        description: "User has been created successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update user mutation
  const updateUserMutation = useMutation({
    mutationFn: ({ id, userData }: { id: string | number; userData: UpdateUserFormData }) =>
      userService.updateUser(id, userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['userStats'] });
      toast({
        title: "User Updated",
        description: "User has been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete user mutation
  const deleteUserMutation = useMutation({
    mutationFn: (userId: string | number) => userService.deleteUser(userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['userStats'] });
      toast({
        title: "User Deleted",
        description: "User has been deleted successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Change password mutation
  const changePasswordMutation = useMutation({
    mutationFn: ({ userId, passwordData }: { 
      userId: string | number; 
      passwordData: AdminChangePasswordFormData 
    }) => userService.changeUserPassword(userId, passwordData),
    onSuccess: () => {
      toast({
        title: "Password Changed",
        description: "Password has been changed successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleCreateUser = () => {
    setFormMode('create');
    setSelectedUser(null);
    setIsUserFormOpen(true);
  };

  const handleEditUser = (user: User) => {
    setFormMode('edit');
    setSelectedUser(user);
    setIsUserFormOpen(true);
  };

  const handleViewUser = (user: User) => {
    setFormMode('view');
    setSelectedUser(user);
    setIsUserFormOpen(true);
  };

  const handleChangePassword = (user: User) => {
    setSelectedUser(user);
    setIsPasswordFormOpen(true);
  };

  const handleDeleteUser = (userId: string | number) => {
    deleteUserMutation.mutate(userId);
  };

  const handleUserFormSubmit = async (data: CreateUserFormData | UpdateUserFormData) => {
    if (formMode === 'create') {
      await createUserMutation.mutateAsync(data as CreateUserFormData);
    } else if (formMode === 'edit' && selectedUser) {
      await updateUserMutation.mutateAsync({
        id: selectedUser.id,
        userData: data as UpdateUserFormData,
      });
    }
  };

  const handlePasswordFormSubmit = async (data: AdminChangePasswordFormData) => {
    if (selectedUser) {
      await changePasswordMutation.mutateAsync({
        userId: selectedUser.id,
        passwordData: data,
      });
    }
  };

  const handleSearchChange = (value: string) => {
    setFilters(prev => ({ ...prev, search: value, page: 1 }));
  };

  const handleRoleFilterChange = (value: string) => {
    setFilters(prev => ({ 
      ...prev, 
      role: value === 'all' ? undefined : value as UserRole,
      page: 1 
    }));
  };

  const handleStatusFilterChange = (value: string) => {
    setFilters(prev => ({ 
      ...prev, 
      is_active: value === 'all' ? undefined : value === 'active',
      page: 1 
    }));
  };

  const users = usersResponse?.data || [];
  const stats = {
    total_users: statsResponse?.data?.total_users || 0,
    active_users: statsResponse?.data?.active_users || 0,
    inactive_users: statsResponse?.data?.inactive_users || 0,
    users_by_role: {
      admin: statsResponse?.data?.users_by_role?.admin || 0,
      moderator: statsResponse?.data?.users_by_role?.moderator || 0,
      viewer: statsResponse?.data?.users_by_role?.viewer || 0
    }
  };

  if (usersError) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Error loading users: {(usersError as Error).message}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      {isLoadingStats ? (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_users}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Users</CardTitle>
              <UserCheck className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.active_users}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Inactive Users</CardTitle>
              <UserX className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.inactive_users}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Administrators</CardTitle>
              <Shield className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.users_by_role.admin}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Actions */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>User Management</CardTitle>
              <CardDescription>
                Manage system users, roles, and permissions
              </CardDescription>
            </div>
            <Button onClick={handleCreateUser}>
              <Plus className="mr-2 h-4 w-4" />
              Add User
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by name or email..."
                  value={filters.search}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="role-filter">Role</Label>
              <Select
                value={filters.role || 'all'}
                onValueChange={handleRoleFilterChange}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                  <SelectItem value="moderator">Moderator</SelectItem>
                  <SelectItem value="viewer">Viewer</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="status-filter">Status</Label>
              <Select
                value={filters.is_active === undefined ? 'all' : filters.is_active ? 'active' : 'inactive'}
                onValueChange={handleStatusFilterChange}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <UserTable
            users={users}
            isLoading={isLoadingUsers}
            onEdit={handleEditUser}
            onDelete={handleDeleteUser}
            onChangePassword={handleChangePassword}
            onView={handleViewUser}
          />
        </CardContent>
      </Card>

      {/* User Form Dialog */}
      <UserForm
        isOpen={isUserFormOpen}
        onClose={() => setIsUserFormOpen(false)}
        onSubmit={handleUserFormSubmit}
        user={selectedUser}
        isLoading={createUserMutation.isPending || updateUserMutation.isPending}
      />

      {/* Password Change Dialog */}
      <PasswordChangeForm
        isOpen={isPasswordFormOpen}
        onClose={() => setIsPasswordFormOpen(false)}
        onSubmit={handlePasswordFormSubmit}
        user={selectedUser}
        isLoading={changePasswordMutation.isPending}
      />
    </div>
  );
};
