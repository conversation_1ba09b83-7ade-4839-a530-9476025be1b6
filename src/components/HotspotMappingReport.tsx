import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Map, MapPin, AlertTriangle, TrendingUp, Download, Calendar, Filter, Users, Clock } from 'lucide-react';

const HotspotMappingReport = () => {
  const [selectedRegion, setSelectedRegion] = useState("all");
  const [selectedTimeframe, setSelectedTimeframe] = useState("30days");
  const [selectedCrimeType, setSelectedCrimeType] = useState("all");

  const regions = [
    { id: "all", label: "All Regions" },
    { id: "northern", label: "Northern Region" },
    { id: "central", label: "Central Region" },
    { id: "southern", label: "Southern Region" }
  ];

  const timeframes = [
    { id: "7days", label: "Last 7 Days" },
    { id: "30days", label: "Last 30 Days" },
    { id: "90days", label: "Last 3 Months" },
    { id: "6months", label: "Last 6 Months" }
  ];

  const crimeTypes = [
    { id: "all", label: "All Crime Types" },
    { id: "violence", label: "Political Violence" },
    { id: "theft", label: "Theft & Robbery" },
    { id: "gbv", label: "Gender-Based Violence" },
    { id: "trafficking", label: "Human Trafficking" },
    { id: "corruption", label: "Corruption" }
  ];

  // Dummy hotspot data
  const hotspotData = {
    overview: {
      totalIncidents: 1247,
      activeHotspots: 15,
      emergingHotspots: 3,
      riskLevel: "High",
      lastUpdated: "2025-05-30 14:30"
    },
    hotspots: [
      {
        id: 1,
        location: "Lilongwe Central",
        district: "Lilongwe",
        region: "Central",
        coordinates: { lat: -13.9626, lng: 33.7741 },
        riskScore: 8.7,
        riskLevel: "Critical",
        totalIncidents: 156,
        crimeTypes: [
          { type: "Political Violence", count: 67, trend: "increasing" },
          { type: "Theft", count: 45, trend: "stable" },
          { type: "GBV", count: 28, trend: "decreasing" },
          { type: "Corruption", count: 16, trend: "increasing" }
        ],
        recentIncidents: [
          { date: "2025-05-29", type: "Political Violence", description: "Rally confrontation", severity: "High" },
          { date: "2025-05-28", type: "Theft", description: "Market theft incident", severity: "Medium" },
          { date: "2025-05-27", type: "GBV", description: "Domestic violence case", severity: "High" }
        ],
        demographics: {
          populationDensity: "High",
          youthPopulation: 68,
          unemploymentRate: 45,
          povertyRate: 72
        }
      },
      {
        id: 2,
        location: "Blantyre City",
        district: "Blantyre",
        region: "Southern",
        coordinates: { lat: -15.7861, lng: 35.0058 },
        riskScore: 7.9,
        riskLevel: "High",
        totalIncidents: 134,
        crimeTypes: [
          { type: "Theft", count: 52, trend: "increasing" },
          { type: "Political Violence", count: 38, trend: "stable" },
          { type: "GBV", count: 31, trend: "decreasing" },
          { type: "Trafficking", count: 13, trend: "increasing" }
        ],
        recentIncidents: [
          { date: "2025-05-30", type: "Theft", description: "Armed robbery at market", severity: "Critical" },
          { date: "2025-05-29", type: "Political Violence", description: "Campaign violence", severity: "High" },
          { date: "2025-05-28", type: "Trafficking", description: "Child trafficking case", severity: "Critical" }
        ],
        demographics: {
          populationDensity: "Very High",
          youthPopulation: 71,
          unemploymentRate: 52,
          povertyRate: 68
        }
      },
      {
        id: 3,
        location: "Mzuzu",
        district: "Mzimba",
        region: "Northern",
        coordinates: { lat: -11.4607, lng: 34.0136 },
        riskScore: 6.2,
        riskLevel: "Medium",
        totalIncidents: 89,
        crimeTypes: [
          { type: "GBV", count: 34, trend: "increasing" },
          { type: "Theft", count: 28, trend: "stable" },
          { type: "Political Violence", count: 18, trend: "decreasing" },
          { type: "Corruption", count: 9, trend: "stable" }
        ],
        recentIncidents: [
          { date: "2025-05-30", type: "GBV", description: "Sexual assault case", severity: "High" },
          { date: "2025-05-29", type: "Theft", description: "Vehicle theft", severity: "Medium" },
          { date: "2025-05-27", type: "Political Violence", description: "Minor altercation", severity: "Low" }
        ],
        demographics: {
          populationDensity: "Medium",
          youthPopulation: 58,
          unemploymentRate: 38,
          povertyRate: 63
        }
      }
    ],
    trendAnalysis: {
      weekOverWeek: 15.3,
      monthOverMonth: -8.2,
      emergingPatterns: [
        "Increased political violence near rally locations",
        "Rising theft incidents in market areas",
        "GBV cases showing seasonal patterns"
      ]
    },
    riskFactors: [
      { factor: "High unemployment rates", weight: 0.35, description: "Areas with >50% youth unemployment show 3x higher crime rates" },
      { factor: "Political event proximity", weight: 0.28, description: "Crime increases 40% within 2km of political events" },
      { factor: "Market density", weight: 0.22, description: "High-density markets correlate with theft and fraud incidents" },
      { factor: "Population density", weight: 0.15, description: "Dense urban areas show higher overall crime rates" }
    ]
  };

  const getRiskLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing': return <TrendingUp className="w-4 h-4 text-red-500" />;
      case 'decreasing': return <TrendingUp className="w-4 h-4 text-green-500 rotate-180" />;
      default: return <div className="w-4 h-4 bg-gray-400 rounded-full" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Map className="w-6 h-6" />
            Crime & Violence Hotspot Mapping Report
          </h2>
          <p className="text-gray-600 mt-1">Analysis and mapping of crime and violence hotspots with risk assessment</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Calendar className="w-4 h-4 mr-2" />
            Schedule
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Report Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Region</label>
              <Select value={selectedRegion} onValueChange={setSelectedRegion}>
                <SelectTrigger>
                  <SelectValue placeholder="Select region" />
                </SelectTrigger>
                <SelectContent>
                  {regions.map(region => (
                    <SelectItem key={region.id} value={region.id}>
                      {region.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Time Period</label>
              <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
                <SelectTrigger>
                  <SelectValue placeholder="Select timeframe" />
                </SelectTrigger>
                <SelectContent>
                  {timeframes.map(timeframe => (
                    <SelectItem key={timeframe.id} value={timeframe.id}>
                      {timeframe.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Crime Type</label>
              <Select value={selectedCrimeType} onValueChange={setSelectedCrimeType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select crime type" />
                </SelectTrigger>
                <SelectContent>
                  {crimeTypes.map(type => (
                    <SelectItem key={type.id} value={type.id}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button className="w-full">Apply Filters</Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Overview Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Incidents</p>
                <p className="text-2xl font-bold">{hotspotData.overview.totalIncidents.toLocaleString()}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Hotspots</p>
                <p className="text-2xl font-bold">{hotspotData.overview.activeHotspots}</p>
              </div>
              <MapPin className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Emerging Hotspots</p>
                <p className="text-2xl font-bold">{hotspotData.overview.emergingHotspots}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Risk Level</p>
                <Badge className={getRiskLevelColor(hotspotData.overview.riskLevel)}>
                  {hotspotData.overview.riskLevel}
                </Badge>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Last Updated</p>
                <p className="text-xs font-medium">{hotspotData.overview.lastUpdated}</p>
              </div>
              <Clock className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analysis Tabs */}
      <Tabs defaultValue="hotspots" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="hotspots">Active Hotspots</TabsTrigger>
          <TabsTrigger value="trends">Trend Analysis</TabsTrigger>
          <TabsTrigger value="risk-factors">Risk Factors</TabsTrigger>
          <TabsTrigger value="map">Interactive Map</TabsTrigger>
        </TabsList>

        <TabsContent value="hotspots" className="space-y-4">
          {hotspotData.hotspots.map((hotspot) => (
            <Card key={hotspot.id} className="border-l-4 border-l-red-500">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="w-5 h-5" />
                      {hotspot.location}
                    </CardTitle>
                    <p className="text-gray-600">{hotspot.district}, {hotspot.region} Region</p>
                  </div>
                  <div className="text-right">
                    <Badge className={getRiskLevelColor(hotspot.riskLevel)}>
                      {hotspot.riskLevel} Risk
                    </Badge>
                    <p className="text-sm text-gray-600 mt-1">Score: {hotspot.riskScore}/10</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Crime Types */}
                  <div>
                    <h4 className="font-semibold mb-3">Crime Distribution</h4>
                    <div className="space-y-2">
                      {hotspot.crimeTypes.map((crime, idx) => (
                        <div key={idx} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <div className="flex items-center gap-2">
                            <span className="text-sm">{crime.type}</span>
                            {getTrendIcon(crime.trend)}
                          </div>
                          <span className="font-medium">{crime.count}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Recent Incidents */}
                  <div>
                    <h4 className="font-semibold mb-3">Recent Incidents</h4>
                    <div className="space-y-2">
                      {hotspot.recentIncidents.map((incident, idx) => (
                        <div key={idx} className="p-2 bg-gray-50 rounded">
                          <div className="flex justify-between items-start mb-1">
                            <span className="text-sm font-medium">{incident.type}</span>
                            <Badge className={getRiskLevelColor(incident.severity)} size="sm">
                              {incident.severity}
                            </Badge>
                          </div>
                          <p className="text-xs text-gray-600">{incident.description}</p>
                          <p className="text-xs text-gray-500 mt-1">{incident.date}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Demographics */}
                  <div>
                    <h4 className="font-semibold mb-3">Demographics & Risk Factors</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm">Population Density</span>
                        <span className="font-medium">{hotspot.demographics.populationDensity}</span>
                      </div>
                      <div className="flex justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm">Youth Population</span>
                        <span className="font-medium">{hotspot.demographics.youthPopulation}%</span>
                      </div>
                      <div className="flex justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm">Unemployment Rate</span>
                        <span className="font-medium">{hotspot.demographics.unemploymentRate}%</span>
                      </div>
                      <div className="flex justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm">Poverty Rate</span>
                        <span className="font-medium">{hotspot.demographics.povertyRate}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Trend Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-4">Short-term Trends</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-red-50 rounded">
                      <span>Week-over-Week Change</span>
                      <div className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-red-500" />
                        <span className="font-bold text-red-600">+{hotspotData.trendAnalysis.weekOverWeek}%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-green-50 rounded">
                      <span>Month-over-Month Change</span>
                      <div className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-green-500 rotate-180" />
                        <span className="font-bold text-green-600">{hotspotData.trendAnalysis.monthOverMonth}%</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-4">Emerging Patterns</h4>
                  <div className="space-y-2">
                    {hotspotData.trendAnalysis.emergingPatterns.map((pattern, idx) => (
                      <div key={idx} className="flex items-start gap-2 p-2 bg-yellow-50 rounded">
                        <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5" />
                        <span className="text-sm">{pattern}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="risk-factors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Risk Factor Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {hotspotData.riskFactors.map((factor, idx) => (
                  <div key={idx} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-semibold">{factor.factor}</h4>
                      <Badge variant="outline">Weight: {(factor.weight * 100).toFixed(0)}%</Badge>
                    </div>
                    <p className="text-gray-600 text-sm">{factor.description}</p>
                    <div className="mt-3">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-yellow-400 to-red-500 h-2 rounded-full"
                          style={{ width: `${factor.weight * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="map" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Interactive Hotspot Map</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-96 bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <Map className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-600">Interactive Map Placeholder</h3>
                  <p className="text-gray-500">Leaflet/MapBox integration would show:</p>
                  <ul className="text-sm text-gray-500 mt-2 text-left max-w-xs">
                    <li>• Hotspot markers with risk levels</li>
                    <li>• Heat map overlay</li>
                    <li>• District boundaries</li>
                    <li>• Recent incident locations</li>
                    <li>• Risk factor overlays</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default HotspotMappingReport;