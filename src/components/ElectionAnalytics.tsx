import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as RechartsTooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Legend
} from "recharts";
import { <PERSON><PERSON>Triangle, FileText, Map, Newspaper, Pie<PERSON>hart as PieChartIcon } from "lucide-react";

const candidateData = [
  { name: "Candidate A", mentions: 3240, sentiment: 65, color: "#3b82f6" },
  { name: "Candidate B", mentions: 2830, sentiment: 48, color: "#ef4444" },
  { name: "Candidate C", mentions: 1970, sentiment: 72, color: "#10b981" },
  { name: "Candidate D", mentions: 1540, sentiment: 35, color: "#f59e0b" },
  { name: "Candidate E", mentions: 1200, sentiment: 50, color: "#8b5cf6" }
];

const narrativeData = [
  { name: "Election Fairness", value: 35, color: "#3b82f6" },
  { name: "Policy Debates", value: 25, color: "#10b981" },
  { name: "Corruption Allegations", value: 20, color: "#f59e0b" },
  { name: "Personal Attacks", value: 15, color: "#ef4444" },
  { name: "Other Topics", value: 5, color: "#8b5cf6" }
];

const sentimentTrendData = [
  { date: "Jan", positive: 65, negative: 35, neutral: 40 },
  { date: "Feb", positive: 58, negative: 42, neutral: 45 },
  { date: "Mar", positive: 52, negative: 48, neutral: 38 },
  { date: "Apr", positive: 48, negative: 52, neutral: 42 },
  { date: "May", positive: 52, negative: 48, neutral: 35 },
  { date: "Jun", positive: 55, negative: 45, neutral: 40 }
];

const locationData = [
  { name: "Lilongwe", value: 35, color: "#3b82f6" },
  { name: "Blantyre", value: 30, color: "#10b981" },
  { name: "Mzuzu", value: 15, color: "#f59e0b" },
  { name: "Zomba", value: 12, color: "#ef4444" },
  { name: "Other", value: 8, color: "#8b5cf6" }
];

const riskFactors = [
  { 
    id: 1, 
    title: "Hate Speech Prevalence", 
    level: "Medium", 
    color: "bg-orange-100 text-orange-800", 
    trend: "Increasing",
    lastUpdated: "Today at 15:30",
    impact: "Moderate"
  },
  { 
    id: 2, 
    title: "Election Misinformation", 
    level: "High", 
    color: "bg-red-100 text-red-800", 
    trend: "Increasing",
    lastUpdated: "Today at 14:45",
    impact: "Severe"
  },
  { 
    id: 3, 
    title: "Regional Tensions", 
    level: "Low", 
    color: "bg-green-100 text-green-800", 
    trend: "Stable",
    lastUpdated: "Today at 12:20",
    impact: "Minor"
  },
  { 
    id: 4, 
    title: "Media Polarization", 
    level: "Medium", 
    color: "bg-orange-100 text-orange-800", 
    trend: "Stable",
    lastUpdated: "Today at 10:15",
    impact: "Moderate"
  },
  { 
    id: 5, 
    title: "Foreign Influence", 
    level: "Medium", 
    color: "bg-orange-100 text-orange-800", 
    trend: "Increasing",
    lastUpdated: "Yesterday at 18:30",
    impact: "Moderate"
  }
];

export const ElectionAnalytics = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Election Analytics Platform</h1>
          <p className="text-gray-600 mt-1">Real-time monitoring of election-related discourse</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          Generate Report
        </Button>
      </div>

      <Tabs defaultValue="overview">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="candidates">Candidate Analysis</TabsTrigger>
          <TabsTrigger value="narratives">Narrative Tracking</TabsTrigger>
          <TabsTrigger value="risks">Risk Factors</TabsTrigger>
        </TabsList>
        
        {/* OVERVIEW TAB */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="border-l-4 border-l-blue-500">
              <CardContent className="p-6">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Monitored Posts</p>
                  <p className="text-3xl font-bold text-blue-600 mt-1">24,669</p>
                  <p className="text-xs text-gray-500 mt-1">+12% from last month</p>
                </div>
              </CardContent>
            </Card>
            
            <Card className="border-l-4 border-l-green-500">
              <CardContent className="p-6">
                <div>
                  <p className="text-sm font-medium text-gray-600">Candidate Mentions</p>
                  <p className="text-3xl font-bold text-green-600 mt-1">9,782</p>
                  <p className="text-xs text-gray-500 mt-1">+8% from last month</p>
                </div>
              </CardContent>
            </Card>
            
            <Card className="border-l-4 border-l-orange-500">
              <CardContent className="p-6">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Alerts</p>
                  <p className="text-3xl font-bold text-orange-600 mt-1">47</p>
                  <p className="text-xs text-gray-500 mt-1">+15% from last month</p>
                </div>
              </CardContent>
            </Card>
            
            <Card className="border-l-4 border-l-red-500">
              <CardContent className="p-6">
                <div>
                  <p className="text-sm font-medium text-gray-600">Risk Level</p>
                  <p className="text-3xl font-bold text-red-600 mt-1">Medium</p>
                  <p className="text-xs text-gray-500 mt-1">No change from last month</p>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PieChartIcon className="w-5 h-5 mr-2" />
                  Election Topics Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={narrativeData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={120}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {narrativeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <RechartsTooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Map className="w-5 h-5 mr-2" />
                  Geographic Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={locationData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <RechartsTooltip />
                    <Bar dataKey="value">
                      {locationData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Newspaper className="w-5 h-5 mr-2" />
                Sentiment Trend Over Time
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={sentimentTrendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <RechartsTooltip />
                  <Legend />
                  <Line type="monotone" dataKey="positive" stroke="#10b981" strokeWidth={3} />
                  <Line type="monotone" dataKey="negative" stroke="#ef4444" strokeWidth={3} />
                  <Line type="monotone" dataKey="neutral" stroke="#6b7280" strokeWidth={3} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* CANDIDATES TAB */}
        <TabsContent value="candidates" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Candidate Mention Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={candidateData} layout="vertical">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="name" type="category" />
                  <RechartsTooltip />
                  <Bar dataKey="mentions">
                    {candidateData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Sentiment Analysis by Candidate</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={candidateData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <RechartsTooltip />
                  <Bar dataKey="sentiment" name="Positive Sentiment (%)" fill="#10b981" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {candidateData.map((candidate, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle>{candidate.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Total Mentions</span>
                      <span className="font-bold">{candidate.mentions.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Sentiment Score</span>
                      <Badge className={candidate.sentiment > 60 ? "bg-green-100 text-green-800" : candidate.sentiment > 40 ? "bg-orange-100 text-orange-800" : "bg-red-100 text-red-800"}>
                        {candidate.sentiment}%
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Key Topics</span>
                      <div className="flex space-x-2">
                        <Badge variant="outline">Economy</Badge>
                        <Badge variant="outline">Healthcare</Badge>
                        <Badge variant="outline">Education</Badge>
                      </div>
                    </div>
                    <Button className="w-full bg-blue-600 hover:bg-blue-700">View Detailed Analysis</Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* NARRATIVES TAB */}
        <TabsContent value="narratives" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Top Election Narratives</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex justify-between items-center p-4 bg-blue-50 rounded-lg">
                  <div>
                    <h3 className="font-bold text-lg">Election Fairness</h3>
                    <p className="text-gray-600 text-sm mt-1">Discussions about election integrity, transparency and fairness of the electoral process</p>
                  </div>
                  <Badge className="bg-blue-100 text-blue-800">35% of mentions</Badge>
                </div>

                <div className="flex justify-between items-center p-4 bg-green-50 rounded-lg">
                  <div>
                    <h3 className="font-bold text-lg">Policy Debates</h3>
                    <p className="text-gray-600 text-sm mt-1">Discussions focusing on candidate policies, manifestos and development agendas</p>
                  </div>
                  <Badge className="bg-green-100 text-green-800">25% of mentions</Badge>
                </div>

                <div className="flex justify-between items-center p-4 bg-orange-50 rounded-lg">
                  <div>
                    <h3 className="font-bold text-lg">Corruption Allegations</h3>
                    <p className="text-gray-600 text-sm mt-1">Claims and discussions about corruption involving candidates or political parties</p>
                  </div>
                  <Badge className="bg-orange-100 text-orange-800">20% of mentions</Badge>
                </div>

                <div className="flex justify-between items-center p-4 bg-red-50 rounded-lg">
                  <div>
                    <h3 className="font-bold text-lg">Personal Attacks</h3>
                    <p className="text-gray-600 text-sm mt-1">Ad hominem attacks, character assassinations and smear campaigns</p>
                  </div>
                  <Badge className="bg-red-100 text-red-800">15% of mentions</Badge>
                </div>

                <div className="flex justify-between items-center p-4 bg-purple-50 rounded-lg">
                  <div>
                    <h3 className="font-bold text-lg">Other Topics</h3>
                    <p className="text-gray-600 text-sm mt-1">Miscellaneous election-related discussions</p>
                  </div>
                  <Badge className="bg-purple-100 text-purple-800">5% of mentions</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Narrative Change Over Time</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={[
                  { date: "Jan", fairness: 25, policy: 30, corruption: 15, attacks: 20, other: 10 },
                  { date: "Feb", fairness: 28, policy: 28, corruption: 18, attacks: 18, other: 8 },
                  { date: "Mar", fairness: 30, policy: 25, corruption: 20, attacks: 15, other: 10 },
                  { date: "Apr", fairness: 32, policy: 22, corruption: 22, attacks: 16, other: 8 },
                  { date: "May", fairness: 35, policy: 25, corruption: 20, attacks: 15, other: 5 },
                ]}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <RechartsTooltip />
                  <Legend />
                  <Line type="monotone" dataKey="fairness" name="Election Fairness" stroke="#3b82f6" strokeWidth={2} />
                  <Line type="monotone" dataKey="policy" name="Policy Debates" stroke="#10b981" strokeWidth={2} />
                  <Line type="monotone" dataKey="corruption" name="Corruption Allegations" stroke="#f59e0b" strokeWidth={2} />
                  <Line type="monotone" dataKey="attacks" name="Personal Attacks" stroke="#ef4444" strokeWidth={2} />
                  <Line type="monotone" dataKey="other" name="Other Topics" stroke="#8b5cf6" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* RISKS TAB */}
        <TabsContent value="risks" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <AlertTriangle className="w-5 h-5 mr-2" />
                  Election Risk Factors
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {riskFactors.map((risk) => (
                    <div key={risk.id} className="flex justify-between items-center p-4 border rounded-lg">
                      <div>
                        <h3 className="font-bold">{risk.title}</h3>
                        <div className="flex space-x-6 mt-2">
                          <div className="text-sm">
                            <span className="text-gray-600">Trend:</span> {risk.trend}
                          </div>
                          <div className="text-sm">
                            <span className="text-gray-600">Impact:</span> {risk.impact}
                          </div>
                          <div className="text-sm">
                            <span className="text-gray-600">Updated:</span> {risk.lastUpdated}
                          </div>
                        </div>
                      </div>
                      <Badge className={risk.color}>
                        {risk.level}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Risk Level Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <PieChart>
                    <Pie
                      data={[
                        { name: "Critical", value: 5, color: "#7c2d12" },
                        { name: "High", value: 20, color: "#ef4444" },
                        { name: "Medium", value: 45, color: "#f59e0b" },
                        { name: "Low", value: 30, color: "#10b981" }
                      ]}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {[
                        { color: "#7c2d12" },
                        { color: "#ef4444" },
                        { color: "#f59e0b" },
                        { color: "#10b981" }
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <RechartsTooltip />
                  </PieChart>
                </ResponsiveContainer>
                <div className="grid grid-cols-2 gap-2 mt-4">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-[#7c2d12] mr-2 rounded-sm"></div>
                    <span className="text-xs text-gray-600">Critical (5%)</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-[#ef4444] mr-2 rounded-sm"></div>
                    <span className="text-xs text-gray-600">High (20%)</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-[#f59e0b] mr-2 rounded-sm"></div>
                    <span className="text-xs text-gray-600">Medium (45%)</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-[#10b981] mr-2 rounded-sm"></div>
                    <span className="text-xs text-gray-600">Low (30%)</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Recommended Preventive Actions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 border-l-4 border-l-red-500 bg-red-50 rounded-r-lg">
                  <h3 className="font-bold text-red-700">High Priority</h3>
                  <p className="mt-1 text-gray-700">Increase monitoring of election misinformation narratives and implement rapid response messaging.</p>
                </div>

                <div className="p-4 border-l-4 border-l-orange-500 bg-orange-50 rounded-r-lg">
                  <h3 className="font-bold text-orange-700">Medium Priority</h3>
                  <p className="mt-1 text-gray-700">Engage with community leaders to address regional tensions and promote peaceful dialogue.</p>
                </div>

                <div className="p-4 border-l-4 border-l-orange-500 bg-orange-50 rounded-r-lg">
                  <h3 className="font-bold text-orange-700">Medium Priority</h3>
                  <p className="mt-1 text-gray-700">Monitor hate speech prevalence and coordinate with platforms for content moderation.</p>
                </div>

                <div className="p-4 border-l-4 border-l-green-500 bg-green-50 rounded-r-lg">
                  <h3 className="font-bold text-green-700">Low Priority</h3>
                  <p className="mt-1 text-gray-700">Continue standard media monitoring procedures and regular reporting.</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
