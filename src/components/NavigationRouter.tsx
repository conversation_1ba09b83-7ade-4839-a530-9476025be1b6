import { NavLink } from "react-router-dom";
import { Bell, User, LogOut, AlertTriangle, Shield, BarChart3 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface NavigationProps {
  onLogout: () => void;
}

export const NavigationRouter = ({ onLogout }: NavigationProps) => {
  return (
    <nav className="bg-blue-600 text-white shadow-lg">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo and Brand */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <img 
                src="/lovable-uploads/3e2b6588-d21b-42b6-8b8f-30cf6f521314.png" 
                alt="Magwero Logo" 
                className="w-8 h-8 object-contain"
              />
              <span className="text-xl font-bold">Magwero</span>
            </div>
          </div>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-8">
            <NavLink
              to="/dashboard"
              className={({ isActive }) =>
                `px-4 py-2 rounded-lg transition-colors ${
                  isActive 
                    ? "bg-blue-700 text-white" 
                    : "text-blue-100 hover:text-white hover:bg-blue-500"
                }`
              }
            >
              Dashboard
            </NavLink>
            <NavLink
              to="/dashboard/analytics"
              className={({ isActive }) =>
                `px-4 py-2 rounded-lg transition-colors ${
                  isActive 
                    ? "bg-blue-700 text-white" 
                    : "text-blue-100 hover:text-white hover:bg-blue-500"
                }`
              }
            >
              Analytics
            </NavLink>
            <NavLink
              to="/dashboard/alerts"
              className={({ isActive }) =>
                `px-4 py-2 rounded-lg transition-colors ${
                  isActive 
                    ? "bg-blue-700 text-white" 
                    : "text-blue-100 hover:text-white hover:bg-blue-500"
                }`
              }
            >
              Alerts
            </NavLink>
          </div>

          {/* Right side - Alerts and User Menu */}
          <div className="flex items-center space-x-4">
            {/* Alert notifications */}
            <div className="relative">
              <Bell className="w-6 h-6 text-blue-100 hover:text-white cursor-pointer" />
              <Badge className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center bg-red-500 text-white text-xs rounded-full">
                3
              </Badge>
            </div>

            {/* Threat level indicator */}
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-yellow-300" />
              <span className="text-sm font-medium">Medium Risk</span>
            </div>

            {/* User menu */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <User className="w-5 h-5" />
                <span className="text-sm">Admin User</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onLogout}
                className="text-blue-100 hover:text-white hover:bg-blue-500"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};
