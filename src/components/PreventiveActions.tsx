import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  ShieldCheck, 
  Clock, 
  Users, 
  Check, 
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
  FileCheck,
  CalendarClock
} from "lucide-react";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as RechartsTooltip, 
  ResponsiveContainer,
  <PERSON>Chart,
  Line,
  Pie<PERSON>hart,
  Pie,
  Cell
} from "recharts";

// Mock data for preventive actions
const preventiveActions = [
  {
    id: 1,
    title: "Misinformation Response Campaign",
    description: "Targeted media campaign to counter widespread misinformation about electoral process",
    status: "In Progress",
    priority: "High",
    priorityColor: "bg-red-100 text-red-800",
    targetDate: "June 15, 2025",
    progress: 45,
    assigned: ["UNDP", "Election Commission", "Media Council"],
    impact: "National",
    kpis: [
      { name: "Misinformation Reduction", target: "-25%", current: "-12%" },
      { name: "Public Trust", target: "+15%", current: "+7%" }
    ]
  },
  {
    id: 2,
    title: "Peace Ambassador Network",
    description: "Deploy community peace ambassadors in high-risk areas to promote dialogue and reduce tensions",
    status: "Active",
    priority: "High",
    priorityColor: "bg-red-100 text-red-800",
    targetDate: "Ongoing through election period",
    progress: 75,
    assigned: ["NGO Consortium", "Community Leaders", "UNDP"],
    impact: "Regional - High Risk Areas",
    kpis: [
      { name: "Incident Prevention", target: "85%", current: "79%" },
      { name: "Community Engagement", target: "10,000 people", current: "7,450 people" }
    ]
  },
  {
    id: 3,
    title: "Cross-Party Dialogue Sessions",
    description: "Facilitated dialogue sessions with leaders from rival political parties to foster cooperation",
    status: "Planned",
    priority: "Medium",
    priorityColor: "bg-orange-100 text-orange-800",
    targetDate: "July 1, 2025",
    progress: 0,
    assigned: ["UN Political Affairs", "Electoral Commission"],
    impact: "National",
    kpis: [
      { name: "Party Participation", target: "All major parties", current: "Not started" },
      { name: "Joint Declarations", target: "3", current: "0" }
    ]
  },
  {
    id: 4,
    title: "Media Literacy Campaign",
    description: "Public education on identifying and reporting hate speech and disinformation online",
    status: "Active",
    priority: "Medium",
    priorityColor: "bg-orange-100 text-orange-800",
    targetDate: "Ongoing",
    progress: 60,
    assigned: ["Media Council", "Ministry of Education"],
    impact: "National",
    kpis: [
      { name: "Public Reach", target: "5 million", current: "3.2 million" },
      { name: "Reporting Increase", target: "+50%", current: "+35%" }
    ]
  },
  {
    id: 5,
    title: "Rapid Response Team Deployment",
    description: "Mobile units prepared to deploy to areas with rising tensions to prevent escalation",
    status: "Ready",
    priority: "High",
    priorityColor: "bg-red-100 text-red-800",
    targetDate: "On standby",
    progress: 100,
    assigned: ["Security Forces", "Peace Commission"],
    impact: "National",
    kpis: [
      { name: "Response Time", target: "<30 minutes", current: "Ready" },
      { name: "Coverage", target: "All districts", current: "85% ready" }
    ]
  }
];

// Stats data
const effectivenessData = [
  { name: "Very Effective", value: 35, color: "#10b981" },
  { name: "Effective", value: 40, color: "#3b82f6" },
  { name: "Partially Effective", value: 15, color: "#f59e0b" },
  { name: "Not Effective", value: 10, color: "#ef4444" }
];

const progressByMonth = [
  { month: "Jan", implemented: 12, planned: 18 },
  { month: "Feb", implemented: 15, planned: 20 },
  { month: "Mar", implemented: 18, planned: 22 },
  { month: "Apr", implemented: 22, planned: 25 },
  { month: "May", implemented: 26, planned: 28 },
  { month: "Jun", implemented: 28, planned: 30 }
];

export const PreventiveActions = () => {
  const [filter, setFilter] = useState("all");
  
  const filteredActions = filter === "all" 
    ? preventiveActions 
    : preventiveActions.filter(action => action.status.toLowerCase() === filter);

  const getStatusIcon = (status: string) => {
    switch(status) {
      case "Active":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "In Progress":
        return <Loader2 className="h-5 w-5 text-blue-600" />;
      case "Planned":
        return <CalendarClock className="h-5 w-5 text-orange-600" />;
      case "Ready":
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case "Completed":
        return <FileCheck className="h-5 w-5 text-green-600" />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Preventive Action Management</h1>
          <p className="text-gray-600 mt-1">Plan, track and measure impact of preventive interventions</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <ShieldCheck className="w-4 h-4 mr-2" />
          Create New Action
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Actions</p>
                <p className="text-3xl font-bold text-gray-900 mt-1">28</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <ShieldCheck className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">In Progress</p>
                <p className="text-3xl font-bold text-blue-600 mt-1">14</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <Clock className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-3xl font-bold text-green-600 mt-1">9</p>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <Check className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Stakeholders</p>
                <p className="text-3xl font-bold text-purple-600 mt-1">16</p>
              </div>
              <div className="bg-purple-100 p-3 rounded-full">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="actions">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="actions">Actions Inventory</TabsTrigger>
          <TabsTrigger value="impact">Impact Analysis</TabsTrigger>
          <TabsTrigger value="planning">Strategic Planning</TabsTrigger>
        </TabsList>
        
        <TabsContent value="actions" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Preventive Actions</CardTitle>
                <div className="flex space-x-2">
                  <Button 
                    variant={filter === "all" ? "default" : "outline"} 
                    onClick={() => setFilter("all")}
                    className={filter === "all" ? "bg-blue-600 hover:bg-blue-700" : ""}
                  >
                    All
                  </Button>
                  <Button 
                    variant={filter === "active" ? "default" : "outline"}
                    onClick={() => setFilter("active")}
                    className={filter === "active" ? "bg-green-600 hover:bg-green-700" : ""}
                  >
                    Active
                  </Button>
                  <Button 
                    variant={filter === "in progress" ? "default" : "outline"}
                    onClick={() => setFilter("in progress")}
                    className={filter === "in progress" ? "bg-blue-600 hover:bg-blue-700" : ""}
                  >
                    In Progress
                  </Button>
                  <Button 
                    variant={filter === "planned" ? "default" : "outline"}
                    onClick={() => setFilter("planned")}
                    className={filter === "planned" ? "bg-orange-600 hover:bg-orange-700" : ""}
                  >
                    Planned
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredActions.map((action) => (
                  <Card key={action.id} className="overflow-hidden">
                    <CardContent className="p-0">
                      <div className="flex flex-col lg:flex-row">
                        <div className="p-4 lg:p-6 flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center">
                              {getStatusIcon(action.status)}
                              <span className="font-bold text-lg ml-2">{action.title}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge className={action.priorityColor}>
                                {action.priority} Priority
                              </Badge>
                              <Badge className="bg-blue-100 text-blue-800">
                                {action.status}
                              </Badge>
                            </div>
                          </div>
                          <p className="text-gray-600 mb-4">{action.description}</p>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                              <p className="text-sm text-gray-500 mb-1">Progress</p>
                              <div className="w-full bg-gray-200 rounded-full h-2.5">
                                <div 
                                  className={`h-2.5 rounded-full ${
                                    action.progress >= 75 ? "bg-green-600" :
                                    action.progress >= 40 ? "bg-blue-600" : "bg-orange-600"
                                  }`} 
                                  style={{ width: `${action.progress}%` }}
                                ></div>
                              </div>
                              <p className="text-xs text-right mt-1">{action.progress}% Complete</p>
                            </div>
                            
                            <div>
                              <p className="text-sm text-gray-500 mb-1">Target Date</p>
                              <p className="text-sm font-medium">{action.targetDate}</p>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm text-gray-500 mb-1">Assigned To</p>
                              <div className="flex flex-wrap gap-1">
                                {action.assigned.map((org, idx) => (
                                  <Badge key={idx} variant="outline" className="bg-gray-50">
                                    {org}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                            
                            <div>
                              <p className="text-sm text-gray-500 mb-1">Impact Area</p>
                              <p className="text-sm font-medium">{action.impact}</p>
                            </div>
                          </div>
                        </div>
                        
                        <div className="bg-gray-50 p-4 lg:p-6 lg:border-l border-gray-200 lg:min-w-[300px]">
                          <h4 className="font-medium mb-4">Key Performance Indicators</h4>
                          
                          <div className="space-y-4">
                            {action.kpis.map((kpi, idx) => (
                              <div key={idx}>
                                <div className="flex justify-between mb-1">
                                  <span className="text-sm text-gray-600">{kpi.name}</span>
                                  <div>
                                    <span className="text-sm text-gray-600">Target: </span>
                                    <span className="text-sm font-medium">{kpi.target}</span>
                                  </div>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-sm text-gray-600">Current: </span>
                                  <span className="text-sm font-medium">{kpi.current}</span>
                                </div>
                              </div>
                            ))}
                          </div>
                          
                          <div className="mt-6 space-y-2">
                            <Button className="w-full bg-blue-600 hover:bg-blue-700">
                              Update Progress
                            </Button>
                            <Button variant="outline" className="w-full">
                              View Details
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="impact" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Action Effectiveness</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={effectivenessData}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={100}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {effectivenessData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <RechartsTooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
                
                <div className="grid grid-cols-2 gap-4 mt-4">
                  {effectivenessData.map((item, i) => (
                    <div key={i} className="flex items-center">
                      <div className="w-3 h-3 mr-2 rounded-sm" style={{ backgroundColor: item.color }}></div>
                      <div className="flex items-center justify-between w-full">
                        <span className="text-sm text-gray-600">{item.name}</span>
                        <span className="font-medium">{item.value}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Implementation Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={progressByMonth}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <RechartsTooltip />
                    <Bar dataKey="implemented" name="Implemented" fill="#3b82f6" />
                    <Bar dataKey="planned" name="Planned" fill="#d1d5db" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Impact by Intervention Type</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2 p-4 bg-green-50 rounded-lg">
                    <h3 className="font-bold text-green-800 flex items-center">
                      <CheckCircle className="w-5 h-5 mr-2 text-green-600" />
                      Media Engagement
                    </h3>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-700">Effectiveness:</span>
                      <Badge className="bg-green-100 text-green-800">High</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-700">Cost-Efficiency:</span>
                      <span className="font-medium">85%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-700">Reach:</span>
                      <span className="font-medium">3.8M people</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2 p-4 bg-blue-50 rounded-lg">
                    <h3 className="font-bold text-blue-800 flex items-center">
                      <CheckCircle className="w-5 h-5 mr-2 text-blue-600" />
                      Community Outreach
                    </h3>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-700">Effectiveness:</span>
                      <Badge className="bg-blue-100 text-blue-800">Medium</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-700">Cost-Efficiency:</span>
                      <span className="font-medium">72%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-700">Reach:</span>
                      <span className="font-medium">1.2M people</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2 p-4 bg-orange-50 rounded-lg">
                    <h3 className="font-bold text-orange-800 flex items-center">
                      <CheckCircle className="w-5 h-5 mr-2 text-orange-600" />
                      Political Dialogue
                    </h3>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-700">Effectiveness:</span>
                      <Badge className="bg-orange-100 text-orange-800">Medium</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-700">Cost-Efficiency:</span>
                      <span className="font-medium">65%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-700">Reach:</span>
                      <span className="font-medium">Key stakeholders</span>
                    </div>
                  </div>
                </div>

                <div className="p-4 border rounded-lg">
                  <h3 className="font-bold mb-4">Incident Reduction by Category</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Hate Speech</span>
                        <span>-32%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div className="h-2.5 rounded-full bg-green-600" style={{ width: '32%' }}></div>
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Misinformation</span>
                        <span>-28%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div className="h-2.5 rounded-full bg-green-600" style={{ width: '28%' }}></div>
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Political Violence</span>
                        <span>-18%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div className="h-2.5 rounded-full bg-blue-600" style={{ width: '18%' }}></div>
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Electoral Process Disruption</span>
                        <span>-22%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div className="h-2.5 rounded-full bg-blue-600" style={{ width: '22%' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="planning" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Strategic Planning Framework</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="p-4 border rounded-lg space-y-4">
                      <h3 className="font-bold text-lg mb-2 text-blue-700">Phase 1: Pre-Election (Current)</h3>
                      
                      <div className="space-y-2">
                        <div className="flex items-center">
                          <Check className="w-5 h-5 text-green-500 mr-2" />
                          <span>Media literacy campaigns across all 28 districts</span>
                        </div>
                        <div className="flex items-center">
                          <Check className="w-5 h-5 text-green-500 mr-2" />
                          <span>Community peace forums established in high-risk areas</span>
                        </div>
                        <div className="flex items-center">
                          <Check className="w-5 h-5 text-green-500 mr-2" />
                          <span>Political party code of conduct agreement secured</span>
                        </div>
                        <div className="flex items-center">
                          <XCircle className="w-5 h-5 text-orange-500 mr-2" />
                          <span className="text-gray-700">Deploy peace ambassadors in all regions</span>
                        </div>
                        <div className="flex items-center">
                          <XCircle className="w-5 h-5 text-orange-500 mr-2" />
                          <span className="text-gray-700">Complete early warning system enhancement</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-4 border rounded-lg space-y-4">
                      <h3 className="font-bold text-lg mb-2 text-blue-700">Phase 2: Election Period</h3>
                      
                      <div className="space-y-2">
                        <div className="flex items-center">
                          <CalendarClock className="w-5 h-5 text-blue-500 mr-2" />
                          <span>Rapid response teams on 24-hour standby</span>
                        </div>
                        <div className="flex items-center">
                          <CalendarClock className="w-5 h-5 text-blue-500 mr-2" />
                          <span>Crisis communication center activation</span>
                        </div>
                        <div className="flex items-center">
                          <CalendarClock className="w-5 h-5 text-blue-500 mr-2" />
                          <span>Real-time monitoring of polling stations</span>
                        </div>
                        <div className="flex items-center">
                          <CalendarClock className="w-5 h-5 text-blue-500 mr-2" />
                          <span>Daily stakeholder briefings and coordination</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-4 border rounded-lg space-y-4">
                      <h3 className="font-bold text-lg mb-2 text-blue-700">Phase 3: Post-Election</h3>
                      
                      <div className="space-y-2">
                        <div className="flex items-center">
                          <CalendarClock className="w-5 h-5 text-blue-500 mr-2" />
                          <span>Result acceptance campaigns</span>
                        </div>
                        <div className="flex items-center">
                          <CalendarClock className="w-5 h-5 text-blue-500 mr-2" />
                          <span>Conflict resolution mechanisms for disputed areas</span>
                        </div>
                        <div className="flex items-center">
                          <CalendarClock className="w-5 h-5 text-blue-500 mr-2" />
                          <span>Peaceful transition monitoring</span>
                        </div>
                        <div className="flex items-center">
                          <CalendarClock className="w-5 h-5 text-blue-500 mr-2" />
                          <span>Post-election reconciliation initiatives</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Resource Allocation</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={200}>
                    <PieChart>
                      <Pie
                        data={[
                          { name: "Media Campaigns", value: 35, color: "#3b82f6" },
                          { name: "Community Engagement", value: 25, color: "#10b981" },
                          { name: "Security & Response", value: 20, color: "#f59e0b" },
                          { name: "Monitoring Systems", value: 15, color: "#8b5cf6" },
                          { name: "Other", value: 5, color: "#6b7280" }
                        ]}
                        cx="50%"
                        cy="50%"
                        innerRadius={40}
                        outerRadius={80}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {[
                          { color: "#3b82f6" },
                          { color: "#10b981" },
                          { color: "#f59e0b" },
                          { color: "#8b5cf6" },
                          { color: "#6b7280" }
                        ].map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                  
                  <div className="grid grid-cols-1 gap-2 mt-4">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-[#3b82f6] mr-2 rounded-sm"></div>
                      <div className="flex items-center justify-between w-full">
                        <span className="text-xs text-gray-600">Media Campaigns</span>
                        <span className="font-medium text-xs">35%</span>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-[#10b981] mr-2 rounded-sm"></div>
                      <div className="flex items-center justify-between w-full">
                        <span className="text-xs text-gray-600">Community Engagement</span>
                        <span className="font-medium text-xs">25%</span>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-[#f59e0b] mr-2 rounded-sm"></div>
                      <div className="flex items-center justify-between w-full">
                        <span className="text-xs text-gray-600">Security & Response</span>
                        <span className="font-medium text-xs">20%</span>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-[#8b5cf6] mr-2 rounded-sm"></div>
                      <div className="flex items-center justify-between w-full">
                        <span className="text-xs text-gray-600">Monitoring Systems</span>
                        <span className="font-medium text-xs">15%</span>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-[#6b7280] mr-2 rounded-sm"></div>
                      <div className="flex items-center justify-between w-full">
                        <span className="text-xs text-gray-600">Other</span>
                        <span className="font-medium text-xs">5%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Create New Action</CardTitle>
                </CardHeader>
                <CardContent>
                  <form className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="action-title">Action Title</Label>
                      <Input id="action-title" placeholder="Enter action title..." />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="action-description">Description</Label>
                      <textarea 
                        id="action-description" 
                        placeholder="Describe the action..."
                        className="w-full p-2 border border-gray-300 rounded-md min-h-[80px]"
                      ></textarea>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="priority">Priority</Label>
                        <select 
                          id="priority" 
                          className="w-full p-2 border border-gray-300 rounded-md"
                        >
                          <option value="high">High</option>
                          <option value="medium">Medium</option>
                          <option value="low">Low</option>
                        </select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="target-date">Target Date</Label>
                        <Input id="target-date" type="date" />
                      </div>
                    </div>
                    <Button className="w-full bg-blue-600 hover:bg-blue-700">
                      Create Action
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
