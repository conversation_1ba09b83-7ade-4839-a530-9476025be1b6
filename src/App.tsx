
import { useState, createContext, useContext, useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import Login from "./pages/Login";
import Dashboard from "./pages/DashboardLayout";
import DashboardHome from "./pages/DashboardHome";
import Analytics from "./pages/Analytics";
import ElectionAnalyticsPage from "./pages/ElectionAnalyticsPage";
import EarlyWarningPage from "./pages/EarlyWarningPage";
import PreventiveActionsPage from "./pages/PreventiveActionsPage";
import AlertsPage from "./pages/AlertsPage";
import Reports from "./pages/Reports";
import AllReports from "./pages/AllReports";
import SystemUsers from "./pages/SystemUsers";
import SettingsPage from "./pages/SettingsPage";
import PlatformSettings from "./pages/PlatformSettings";
import NotFound from "./pages/NotFound";
import FloatingChat from "./components/FloatingChat";
import { authService } from "./services/authService";
import { AuthContextType, AuthUser, UserRole } from "./types/userTypes";
import "./services/chatApiTest"; // Import test functions

const queryClient = new QueryClient();

// Auth Context - using imported type

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Protected Route Component
const ProtectedRoute = ({ children, requiredRole }: {
  children: React.ReactNode;
  requiredRole?: UserRole;
}) => {
  const { isAuthenticated, user } = useAuth();

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRole && user && !hasRequiredRole(user.role, requiredRole)) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};

// Helper function to check role hierarchy
const hasRequiredRole = (userRole: UserRole, requiredRole: UserRole): boolean => {
  const roleHierarchy: Record<UserRole, number> = {
    viewer: 1,
    moderator: 2,
    admin: 3,
  };

  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
};

const App = () => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Initialize auth state from storage
    const initializeAuth = async () => {
      try {
        const storedToken = authService.getToken();
        const storedUser = authService.getUser();

        if (storedToken && storedUser) {
          // Verify token is still valid
          if (!authService.isTokenExpired()) {
            setToken(storedToken);
            setUser(storedUser);
          } else {
            authService.logout();
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        authService.logout();
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (email: string, password: string): Promise<void> => {
    try {
      const userData = await authService.login(email, password);
      setUser(userData);
      setToken(authService.getToken());
    } catch (error) {
      throw error; // Re-throw to be handled by the login component
    }
  };

  const logout = (): void => {
    authService.logout();
    setUser(null);
    setToken(null);
  };

  const updateUser = (userData: AuthUser): void => {
    setUser(userData);
    authService.updateUser(userData);
  };

  const hasRole = (role: UserRole): boolean => {
    return user?.role === role;
  };

  const hasPermission = (permission: string): boolean => {
    return authService.hasPermission(permission);
  };

  const authValue: AuthContextType = {
    user,
    token,
    isAuthenticated: !!(token && user),
    isLoading,
    login,
    logout,
    updateUser,
    hasRole,
    hasPermission,
  };

  return (
    <QueryClientProvider client={queryClient}>
      <AuthContext.Provider value={authValue}>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route 
              path="/" 
              element={
                <ProtectedRoute>
                  <Navigate to="/dashboard" replace />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/dashboard" 
              element={
                <ProtectedRoute>
                  <Dashboard onLogout={logout} />
                </ProtectedRoute>
              }
            >              <Route index element={<DashboardHome />} />
              <Route path="analytics" element={<Analytics />} />
              <Route path="election-analytics" element={<ElectionAnalyticsPage />} />
              <Route path="early-warning" element={<EarlyWarningPage />} />
              <Route path="preventive-actions" element={<PreventiveActionsPage />} />
              <Route path="reports" element={<Reports />} />
              <Route path="all-reports" element={<AllReports />} />
              <Route path="alerts" element={<AlertsPage />} />
              <Route
                path="users"
                element={
                  <ProtectedRoute requiredRole="admin">
                    <SystemUsers />
                  </ProtectedRoute>
                }
              />
              <Route path="media-profiles" element={<SettingsPage />} />
              <Route path="platform-settings" element={<PlatformSettings />} />
            </Route><Route path="*" element={<NotFound />} />
          </Routes>
          {/* FloatingChat component available on all authenticated pages */}
          {authValue.isAuthenticated && <FloatingChat />}
        </BrowserRouter>
      </AuthContext.Provider>
    </QueryClientProvider>
  );
};

export default App;
