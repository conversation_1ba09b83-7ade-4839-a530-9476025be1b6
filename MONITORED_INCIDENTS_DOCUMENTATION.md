# Monitored Incidents Feature Documentation

## Overview
The Monitored Incidents feature is a comprehensive tab within the Reports page that fetches and displays incident data from the `/api/reports/incidents` endpoint. It provides powerful filtering capabilities and dual display modes (cards and table) for viewing all incident fields.

## Features

### 1. Data Display
- **Card View**: Rich, detailed cards showing incident summaries, categories, severity levels, metadata, and array values properly rendered
- **Table View**: Comprehensive tabular format displaying all incident fields including array values with proper formatting
- **Array Handling**: Special rendering for array fields (key_themes, entities_involved, etc.) with expandable display
- **Responsive Design**: Adapts to different screen sizes with optimized column layouts

### 2. Filtering Capabilities
The interface includes dropdown filters for:
- **Severity Levels**: All, Critical, High, Medium, Low
- **Districts**: Complete list of all 30 Malawi districts plus National level
- **Platforms**: Twitter, Facebook, Instagram, Website, TikTok, WhatsApp, Other
- **Categories**: All major incident categories based on the election monitoring classification
- **Candidate Names**: Key political candidates including <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Professor <PERSON>, <PERSON>
- **Political Entities**: Comprehensive list of political parties, CSOs, movements, and groupings with their abbreviations (MCP, DPP, UTM, UDF, PP, AFORD, HRDC, CHRR, etc.)

### 3. Pagination & Load More
- Displays results in batches with configurable limit (default: 20 per page)
- "Load More" button for progressive data loading
- Shows current count vs total available incidents

### 4. Real-time Updates
- Refresh button to fetch latest data
- Loading states and error handling
- Auto-refresh capabilities

## API Integration

### Endpoint
```
GET http://localhost:3200/api/reports/incidents
```

### Query Parameters
- `limit`: Number of incidents per page (default: 50)
- `offset`: Starting position for pagination (default: 0)
- `severity`: Filter by severity level (all|Critical|High|Medium|Low)
- `district`: Filter by district (all|<district_name>)
- `category`: Filter by incident category
- `platform`: Filter by platform
- `candidate`: Filter by candidate name
- `political_entity`: Filter by political party/CSO/movement

### Response Structure
```json
{
  "success": true,
  "data": {
    "incidents": [
      {
        "id": 124,
        "post_summary": "Brief summary of the incident",
        "primary_category": "Political/electoral misconduct",
        "severity_level": "Medium",
        "district": "Zomba",
        "platform": "Website",
        "perpetrator": "Political Party",
        "victims_profile": "Political Candidate",
        "details": "Detailed description",
        "date_of_incident": "2025-06-16T22:00:00.000Z",
        "created_at": "2025-06-19T11:19:49.000Z",
        // ... additional fields
      }
    ],
    "pagination": {
      "total": 50,
      "limit": 50,
      "offset": 0,
      "has_more": true
    },
    "filters_applied": {
      "severity": "all",
      "district": "all",
      "category": "all",
      "platform": "all"
    }
  },
  "message": "Incidents retrieved successfully"
}
```

## Component Architecture

### Files Structure
```
src/
├── types/
│   └── incidentsTypes.ts          # TypeScript types and constants
├── services/
│   └── incidentsService.ts        # API service layer
├── hooks/
│   └── useIncidents.ts            # Custom hook for state management
├── components/
│   └── MonitoredIncidents.tsx     # Main component
└── pages/
    └── Reports.tsx                # Parent page with tabs
```

### Key Components

#### 1. Types (`incidentsTypes.ts`)
- Comprehensive TypeScript interfaces for all incident data
- Constants for districts, categories, platforms, and severity levels
- Type-safe filter definitions

#### 2. Service (`incidentsService.ts`)
- Axios-based HTTP client for API communication
- Error handling and logging
- Query parameter construction

#### 3. Hook (`useIncidents.ts`)
- State management for incidents data, loading, and errors
- Filter state management with auto-refresh on filter changes
- Pagination handling with "load more" functionality
- Data appending for progressive loading

#### 4. Component (`MonitoredIncidents.tsx`)
- Dual view modes (cards/table)
- Interactive filters with dropdown menus
- Statistics cards showing key metrics
- Responsive design patterns

## Filter Classifications

### Severity Levels
Based on incident impact:
- **Critical**: Immediate threat to election integrity
- **High**: Significant electoral violations
- **Medium**: Moderate incidents requiring attention
- **Low**: Minor incidents for monitoring

### Districts (Complete Malawi Coverage)
All 30 districts plus national level:
- National, Balaka, Blantyre, Chikwawa, Chiradzulu, Chitipa, Dedza, Dowa, Karonga, Kasungu, Likoma, Lilongwe, Machinga, Mangochi, Mchinji, Mulanje, Mwanza, Mzimba, Mzuzu, Neno, Nkhata Bay, Nkhotakota, Nsanje, Ntcheu, Ntchisi, Phalombe, Rumphi, Salima, Thyolo, Zomba

### Categories
Based on election monitoring classification:
- Political/electoral misconduct
- Violation of right to physical integrity (violent attacks)
- Law enforcement misconduct
- Restrictions on civil and political rights
- Politically motivated attacks/harassment/intimidation/incitement

### Platforms
Social media and news sources:
- Twitter/X, Facebook, Instagram, Website, TikTok, WhatsApp, Other

## Usage Instructions

### Accessing the Feature
1. Navigate to the Reports page
2. Click on the "Monitored Incidents" tab

### Filtering Data
1. Use the dropdown filters to narrow down results:
   - Select severity level for incident priority
   - Choose specific districts for geographic filtering
   - Filter by platform to focus on specific sources
   - Select categories for incident type filtering
2. Filters are applied automatically with debouncing
3. Use "Refresh" button to reload with current filters

### Viewing Data
1. **Card View**: Default view with rich incident details
2. **Table View**: Click "Table" button for compact tabular display
3. **Load More**: Click to fetch additional results
4. **View Details**: Click on individual incidents for expanded view

### Performance Considerations
- Default pagination limit: 20 incidents per page
- Progressive loading to handle large datasets
- Responsive design for mobile/tablet compatibility
- Optimized filtering to minimize API calls

## Integration Points

### Navigation
- Integrated into Reports page tab structure
- Consistent with existing dashboard navigation patterns

### State Management
- Independent filter state per tab
- Persistent filter selections during session
- Auto-refresh on filter changes

### Error Handling
- Network error recovery
- User-friendly error messages
- Loading states for better UX

## Future Enhancements

### Potential Improvements
1. **Export Functionality**: CSV/PDF export of filtered results
2. **Advanced Search**: Text search within incident summaries
3. **Date Range Filtering**: Custom date range selection
4. **Sorting Options**: Sort by date, severity, district
5. **Incident Details Modal**: Expanded view with full incident analysis
6. **Real-time Notifications**: Push notifications for high-severity incidents
7. **Bulk Actions**: Select multiple incidents for batch operations

### Performance Optimizations
1. **Virtual Scrolling**: For large datasets
2. **Caching**: Client-side caching of filter results
3. **Debounced Filtering**: Reduce API calls during rapid filter changes
4. **Lazy Loading**: Load images and additional data on demand

This implementation provides a robust foundation for monitoring election-related incidents with comprehensive filtering, multiple view modes, and excellent user experience.
